"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/vessel/info/page",{

/***/ "(app-pages-browser)/./src/app/ui/vessels/view.tsx":
/*!*************************************!*\
  !*** ./src/app/ui/vessels/view.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ VesselsView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _crew_multiselect_dropdown_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../crew/multiselect-dropdown/multiselect-dropdown */ \"(app-pages-browser)/./src/app/ui/crew/multiselect-dropdown/multiselect-dropdown.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_hooks_use_mobile__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/hooks/use-mobile */ \"(app-pages-browser)/./src/components/hooks/use-mobile.tsx\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_helpers_vesselHelper__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/helpers/vesselHelper */ \"(app-pages-browser)/./src/app/helpers/vesselHelper.ts\");\n/* harmony import */ var _components_header_image__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./components/header-image */ \"(app-pages-browser)/./src/app/ui/vessels/components/header-image.tsx\");\n/* harmony import */ var _components_logbook_entries_card__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./components/logbook-entries-card */ \"(app-pages-browser)/./src/app/ui/vessels/components/logbook-entries-card.tsx\");\n/* harmony import */ var _components_maintenance_card__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./components/maintenance-card */ \"(app-pages-browser)/./src/app/ui/vessels/components/maintenance-card.tsx\");\n/* harmony import */ var _components_training_drills_card__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./components/training-drills-card */ \"(app-pages-browser)/./src/app/ui/vessels/components/training-drills-card.tsx\");\n/* harmony import */ var _components_crew_card__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./components/crew-card */ \"(app-pages-browser)/./src/app/ui/vessels/components/crew-card.tsx\");\n/* harmony import */ var _components_inventory_card__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./components/inventory-card */ \"(app-pages-browser)/./src/app/ui/vessels/components/inventory-card.tsx\");\n/* harmony import */ var _components_tabs_holder__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./components/tabs-holder */ \"(app-pages-browser)/./src/app/ui/vessels/components/tabs-holder.tsx\");\n/* harmony import */ var _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/app/offline/models/logBookEntry */ \"(app-pages-browser)/./src/app/offline/models/logBookEntry.js\");\n/* harmony import */ var _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/app/offline/models/vessel */ \"(app-pages-browser)/./src/app/offline/models/vessel.js\");\n/* harmony import */ var _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/app/offline/models/seaLogsMember */ \"(app-pages-browser)/./src/app/offline/models/seaLogsMember.js\");\n/* harmony import */ var _app_helpers_trainingHelper__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/app/helpers/trainingHelper */ \"(app-pages-browser)/./src/app/helpers/trainingHelper.ts\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_footer_wrapper__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/components/footer-wrapper */ \"(app-pages-browser)/./src/components/footer-wrapper.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_DateRange__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @/components/DateRange */ \"(app-pages-browser)/./src/components/DateRange.tsx\");\n/* harmony import */ var _editor__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ../editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.30_@ba_ed8daac48216b87d589b3ebdbcc06997/node_modules/nuqs/dist/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst vesselStatuses = [\n    {\n        label: \"On Voyage\",\n        value: \"OnVoyage\"\n    },\n    {\n        label: \"Ready For Voyage\",\n        value: \"AvailableForVoyage\"\n    },\n    {\n        label: \"Out Of Service\",\n        value: \"OutOfService\"\n    }\n];\nconst vesselStatusReason = [\n    {\n        label: \"Crew Unavailable\",\n        value: \"CrewUnavailable\"\n    },\n    {\n        label: \"Skipper/Master Unavailable\",\n        value: \"MasterUnavailable\"\n    },\n    {\n        label: \"Planned Maintenance\",\n        value: \"PlannedMaintenance\"\n    },\n    {\n        label: \"Breakdown\",\n        value: \"Breakdown\"\n    },\n    {\n        label: \"Other\",\n        value: \"Other\"\n    }\n];\nfunction VesselsView(param) {\n    let { vesselId, tab } = param;\n    var _vessel_statusHistory_nodes, _vessel_statusHistory, _vessel_departments;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams)();\n    const [vessel, setVessel] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)();\n    const [logbooks, setLogbooks] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [currentLogEntryAction, setCurrentLogEntryAction] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)();\n    const [currentLogEntry, setCurrentLogEntry] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)();\n    const [totalEntries, setTotalEntries] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [maintenanceTasks, setMaintenanceTasks] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [taskCounter, setTaskCounter] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [trainingSessions, setTrainingSessions] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)();\n    const [trainingSessionDues, setTrainingSessionDues] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [trainingSessionDuesSummary, setTrainingSessionDuesSummary] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [crewInfo, setCrewInfo] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)();\n    const [taskCrewInfo, setTaskCrewInfo] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)();\n    const [inventories, setInventories] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [engineList, setEngineList] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)();\n    const [fuelTankList, setFuelTankList] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)();\n    const [waterTankList, setWaterTankList] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)();\n    const [sewageSystemList, setSewageSystemList] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)();\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const [vesselTab, setVesselTab] = (0,nuqs__WEBPACK_IMPORTED_MODULE_31__.useQueryState)(\"tab\", {\n        defaultValue: \"logEntries\"\n    });\n    const perPage = 10;\n    const [displayAddCrew, setDisplayAddCrew] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [vesselCrewIDs, setVesselCrewIDs] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [bannerImage, setBannerImage] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [isNewLogEntryDisabled, setIsNewLogEntryDisabled] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(true);\n    const [imCrew, setImCrew] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [edit_logBookEntry, setEdit_logBookEntry] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [edit_task, setEdit_task] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [edit_docs, setEdit_docs] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [delete_docs, setDelete_docs] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [trainingsDueCount, setTrainingsDueCount] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [displayEditStatus, setDisplayEditStatus] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [vesselStatus, setVesselStatus] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const isMobile = (0,_components_hooks_use_mobile__WEBPACK_IMPORTED_MODULE_12__.useIsMobile)();\n    const tabsRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\n    const lbeModel = new _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_22__[\"default\"]();\n    const vesselModel = new _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_23__[\"default\"]();\n    const seaLogsMemberModel = new _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_24__[\"default\"]();\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.hasPermission)(\"ADD_LOGBOOKENTRY\", permissions) || (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.hasPermission)(\"EDIT_LOGBOOKENTRY\", permissions)) {\n                setEdit_logBookEntry(true);\n            } else {\n                setEdit_logBookEntry(false);\n            }\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.hasPermission)(\"EDIT_TASK\", permissions)) {\n                setEdit_task(true);\n            } else {\n                setEdit_task(false);\n            }\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.hasPermission)(\"EDIT_VESSEL_DOCUMENT\", permissions)) {\n                setEdit_docs(true);\n            } else {\n                setEdit_docs(false);\n            }\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.hasPermission)(\"DELETE_VESSEL_DOCUMENT\", permissions)) {\n                setDelete_docs(true);\n            } else {\n                setDelete_docs(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        var _vessel_statusHistory, _vessel_statusHistory_nodes, _vessel_statusHistory1;\n        setVesselStatus(vessel === null || vessel === void 0 ? void 0 : (_vessel_statusHistory = vessel.statusHistory) === null || _vessel_statusHistory === void 0 ? void 0 : _vessel_statusHistory.nodes[0]);\n        if ((vessel === null || vessel === void 0 ? void 0 : (_vessel_statusHistory1 = vessel.statusHistory) === null || _vessel_statusHistory1 === void 0 ? void 0 : (_vessel_statusHistory_nodes = _vessel_statusHistory1.nodes) === null || _vessel_statusHistory_nodes === void 0 ? void 0 : _vessel_statusHistory_nodes.length) === 0) {\n            createVesselStatus({\n                variables: {\n                    input: {\n                        date: dayjs__WEBPACK_IMPORTED_MODULE_9___default()().format(\"YYYY-MM-DD\"),\n                        vesselID: +(vessel === null || vessel === void 0 ? void 0 : vessel.id)\n                    }\n                }\n            });\n        }\n    }, [\n        vessel\n    ]);\n    const handleUpdateVesselStatus = ()=>{\n        {\n            (vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.status) === \"OutOfService\" ? createVesselStatus({\n                variables: {\n                    input: {\n                        vesselID: vessel === null || vessel === void 0 ? void 0 : vessel.id,\n                        date: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.date,\n                        status: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.status,\n                        comment: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.comment,\n                        reason: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.reason,\n                        otherReason: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.otherReason,\n                        expectedReturn: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.expectedReturn\n                    }\n                }\n            }) : createVesselStatus({\n                variables: {\n                    input: {\n                        vesselID: vessel === null || vessel === void 0 ? void 0 : vessel.id,\n                        date: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.date,\n                        status: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.status\n                    }\n                }\n            });\n        }\n    };\n    const handleVesselStatusDate = (date)=>{\n        setVesselStatus({\n            ...vesselStatus,\n            date: date\n        });\n    };\n    const handleVesselStatusReturnDate = (date)=>{\n        setVesselStatus({\n            ...vesselStatus,\n            expectedReturn: date\n        });\n    };\n    const [createVesselStatus] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_32__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CREATE_VESSELSTATUS, {\n        onCompleted: (response)=>{\n            const data = response.createLogBookEntry;\n            setVesselStatus({\n                ...vesselStatus,\n                vesselID: vessel === null || vessel === void 0 ? void 0 : vessel.id,\n                date: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.date,\n                status: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.status,\n                comment: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.comment,\n                reason: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.reason,\n                otherReason: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.otherReason,\n                expectedReturn: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.expectedReturn\n            });\n            setDisplayEditStatus(false);\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(error.message);\n        }\n    });\n    const handleVesselStatusChange = (value)=>{\n        if (!hasLogbookOpen) {\n            (value === null || value === void 0 ? void 0 : value.value) === \"OnVoyage\" ? sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"There is no Open LogBook entry, Please create a Logbook entry to set the vessel on voyage\") : setVesselStatus({\n                ...vesselStatus,\n                status: value === null || value === void 0 ? void 0 : value.value\n            });\n        } else {\n            value.value !== \"OnVoyage\" ? sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"There is an Open LogBook entry, Please complete the entry in order to update the vessel status\") : setVesselStatus({\n                ...vesselStatus,\n                status: value === null || value === void 0 ? void 0 : value.value\n            });\n        }\n    };\n    const handleVesselStatusReasonChange = (value)=>{\n        setVesselStatus({\n            ...vesselStatus,\n            reason: value === null || value === void 0 ? void 0 : value.value\n        });\n    };\n    const handleSetLogbooks = (data)=>{\n        data.sort((a, b)=>b.state === \"Locked\" ? -1 : a.state === \"Locked\" ? 1 : 0);\n        let lbs = [\n            ...data.filter((entry)=>entry.state !== \"Locked\").sort((a, b)=>new Date(a.startDate).getTime() - new Date(b.startDate).getTime()),\n            ...data.filter((entry)=>entry.state === \"Locked\").sort((a, b)=>new Date(b.startDate).getTime() - new Date(a.startDate).getTime())\n        ];\n        setLogbooks(lbs);\n        setIsNewLogEntryDisabled(false);\n        {\n            data.filter((entry)=>entry.state !== \"Locked\").length > 0 && setCurrentLogEntryAction(data.filter((entry)=>entry.state !== \"Locked\")[0]);\n        }\n        setTotalEntries(data.length);\n        {\n            data.filter((entry)=>entry.state !== \"Locked\").length > 0 && loadLogEntry(data.filter((entry)=>entry.state !== \"Locked\")[0].id);\n        }\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_7__.getLogBookEntries)(vesselId, handleSetLogbooks);\n    // getInventoryByVesselId(vesselId, setInventories)\n    const [queryInventoriesByVessel] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_INVENTORIES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readInventories.nodes;\n            if (data) {\n                setInventories(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryInventories error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        loadInventories();\n    }, []);\n    const loadInventories = async ()=>{\n        await queryInventoriesByVessel({\n            variables: {\n                filter: {\n                    vesselID: {\n                        eq: +vesselId\n                    }\n                }\n            }\n        });\n    };\n    const handleSetTrainingSessionDues = (data)=>{\n        const dues = (0,_app_helpers_trainingHelper__WEBPACK_IMPORTED_MODULE_25__.mergeTrainingSessionDues)(data).slice(0, 5);\n        setTrainingsDueCount((0,_app_helpers_vesselHelper__WEBPACK_IMPORTED_MODULE_14__.getTrainingsDueCount)(vesselId));\n        setTrainingSessionDues(data);\n        setTrainingSessionDuesSummary(dues);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_7__.getTrainingSessionDuesByVesselId)(vesselId, handleSetTrainingSessionDues);\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_7__.getTrainingSessionsByVesselId)(vesselId, setTrainingSessions);\n    const [queryGetEngines] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_ENGINES, {\n        // fetchPolicy: 'no-cache',\n        onCompleted: (response)=>{\n            const data = response.readEngines.nodes;\n            setEngineList(data);\n        },\n        onError: (error)=>{\n            console.error(\"getEngines error\", error);\n        }\n    });\n    const getEngines = async (engineIds)=>{\n        await queryGetEngines({\n            variables: {\n                id: engineIds\n            }\n        });\n    };\n    const [queryGetFuelTanks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_FUELTANKS, {\n        // fetchPolicy: 'no-cache',\n        onCompleted: (response)=>{\n            const data = response.readFuelTanks.nodes;\n            setFuelTankList(data);\n        },\n        onError: (error)=>{\n            console.error(\"getFuelTanks error\", error);\n        }\n    });\n    const getFuelTanks = async (fuelTankIds)=>{\n        await queryGetFuelTanks({\n            variables: {\n                id: fuelTankIds\n            }\n        });\n    };\n    const [queryGetWaterTanks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_WATERTANKS, {\n        // fetchPolicy: 'no-cache',\n        onCompleted: (response)=>{\n            const data = response.readWaterTanks.nodes;\n            setWaterTankList(data);\n        },\n        onError: (error)=>{\n            console.error(\"getWaterTanks error\", error);\n        }\n    });\n    const getWaterTanks = async (waterTankIds)=>{\n        await queryGetWaterTanks({\n            variables: {\n                id: waterTankIds\n            }\n        });\n    };\n    const [queryGetSewageSystems] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_SEWAGESYSTEMS, {\n        // fetchPolicy: 'no-cache',\n        onCompleted: (response)=>{\n            const data = response.readSewageSystems.nodes;\n            setSewageSystemList(data);\n        },\n        onError: (error)=>{\n            console.error(\"getSewageSystems error\", error);\n        }\n    });\n    const getSewageSystems = async (sewageSystemIds)=>{\n        await queryGetSewageSystems({\n            variables: {\n                id: sewageSystemIds\n            }\n        });\n    };\n    const handleSetVessel = (vessel)=>{\n        var _vessel_seaLogsMembers, _vessel_parentComponent_Components, _vessel_parentComponent_Components1, _vessel_parentComponent_Components2, _vessel_parentComponent_Components3, _vessel_documents_nodes, _vessel_documents;\n        setVessel(vessel);\n        setVesselCrewIDs(vessel === null || vessel === void 0 ? void 0 : (_vessel_seaLogsMembers = vessel.seaLogsMembers) === null || _vessel_seaLogsMembers === void 0 ? void 0 : _vessel_seaLogsMembers.nodes.map((crew)=>crew.id));\n        (vessel === null || vessel === void 0 ? void 0 : vessel.seaLogsMembers) && loadCrewMemberInfo(vessel.seaLogsMembers.nodes.filter((crew)=>!crew.archived).map((crew)=>+crew.id));\n        const engineIds = vessel === null || vessel === void 0 ? void 0 : (_vessel_parentComponent_Components = vessel.parentComponent_Components) === null || _vessel_parentComponent_Components === void 0 ? void 0 : _vessel_parentComponent_Components.nodes.filter((item)=>item.basicComponent.componentCategory === \"Engine\").map((item)=>{\n            return item.basicComponent.id;\n        });\n        const fuelTankIds = vessel === null || vessel === void 0 ? void 0 : (_vessel_parentComponent_Components1 = vessel.parentComponent_Components) === null || _vessel_parentComponent_Components1 === void 0 ? void 0 : _vessel_parentComponent_Components1.nodes.filter((item)=>item.basicComponent.componentCategory === \"FuelTank\").map((item)=>{\n            return item.basicComponent.id;\n        });\n        const waterTankIds = vessel === null || vessel === void 0 ? void 0 : (_vessel_parentComponent_Components2 = vessel.parentComponent_Components) === null || _vessel_parentComponent_Components2 === void 0 ? void 0 : _vessel_parentComponent_Components2.nodes.filter((item)=>item.basicComponent.componentCategory === \"WaterTank\").map((item)=>{\n            return item.basicComponent.id;\n        });\n        const sewageSystemIds = vessel === null || vessel === void 0 ? void 0 : (_vessel_parentComponent_Components3 = vessel.parentComponent_Components) === null || _vessel_parentComponent_Components3 === void 0 ? void 0 : _vessel_parentComponent_Components3.nodes.filter((item)=>item.basicComponent.componentCategory === \"SewageSystem\").map((item)=>{\n            return item.basicComponent.id;\n        });\n        (engineIds === null || engineIds === void 0 ? void 0 : engineIds.length) > 0 && getEngines(engineIds);\n        (fuelTankIds === null || fuelTankIds === void 0 ? void 0 : fuelTankIds.length) > 0 && getFuelTanks(fuelTankIds);\n        (waterTankIds === null || waterTankIds === void 0 ? void 0 : waterTankIds.length) > 0 && getWaterTanks(waterTankIds);\n        (sewageSystemIds === null || sewageSystemIds === void 0 ? void 0 : sewageSystemIds.length) > 0 && getSewageSystems(sewageSystemIds);\n        (vessel === null || vessel === void 0 ? void 0 : (_vessel_documents = vessel.documents) === null || _vessel_documents === void 0 ? void 0 : (_vessel_documents_nodes = _vessel_documents.nodes) === null || _vessel_documents_nodes === void 0 ? void 0 : _vessel_documents_nodes.length) > 0 && setDocuments(vessel.documents.nodes);\n        if ((vessel === null || vessel === void 0 ? void 0 : vessel.logBookID) == 0) {\n            createNewLogBook(vessel);\n        }\n        if ((vessel === null || vessel === void 0 ? void 0 : vessel.bannerImageID) !== \"0\" && (vessel === null || vessel === void 0 ? void 0 : vessel.bannerImageID)) {\n            getFileDetails({\n                variables: {\n                    id: [\n                        vessel.bannerImageID\n                    ]\n                }\n            });\n        }\n    };\n    const [getFileDetails, { data, loading, error }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_FILES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            var _data_;\n            const data = response.readFiles.nodes;\n            setBannerImage(\"https://api.sealogs.com/assets/\" + ((_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.fileFilename));\n        },\n        onError: (error)=>{\n            console.error(error);\n        }\n    });\n    const createNewLogBook = async (vessel)=>{\n        await createLogBook({\n            variables: {\n                input: {\n                    title: vessel.title\n                }\n            }\n        });\n    };\n    const [createLogBook] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_32__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CREATE_LOGBOOK, {\n        onCompleted: (response)=>{\n            updateVessel({\n                variables: {\n                    input: {\n                        id: vesselId,\n                        logBookID: response.createLogBook.id\n                    }\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"createLogBook error\", error);\n        }\n    });\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_7__.getVesselByID)(vesselId, handleSetVessel);\n    const handleSetMaintenanceTasks = (data)=>{\n        if (data.length === 0) {\n            setMaintenanceTasks(false);\n        }\n        const tasks = data.filter((task)=>{\n            return (task === null || task === void 0 ? void 0 : task.archived) !== 1;\n        });\n        const inventoryTasks = inventories.flatMap((inventory)=>{\n            const checks = inventory.componentMaintenanceChecks || [];\n            return checks;\n        });\n        const combinedTasks = [\n            ...tasks,\n            ...inventoryTasks\n        ];\n        const seenIds = new Set();\n        const deduplicatedTasks = combinedTasks.filter((task)=>{\n            const isDuplicate = seenIds.has(task.id);\n            seenIds.add(task.id);\n            return !isDuplicate;\n        });\n        deduplicatedTasks.sort((a, b)=>{\n            var _a_isOverDue, _b_isOverDue;\n            // Add null checks for isOverDue\n            const aStatus = (_a_isOverDue = a.isOverDue) === null || _a_isOverDue === void 0 ? void 0 : _a_isOverDue.status;\n            const bStatus = (_b_isOverDue = b.isOverDue) === null || _b_isOverDue === void 0 ? void 0 : _b_isOverDue.status;\n            if (aStatus === \"High\" && bStatus !== \"High\") {\n                return -1;\n            } else if (aStatus !== \"High\" && bStatus === \"High\") {\n                return 1;\n            } else if (aStatus === \"Medium\" && bStatus !== \"Medium\") {\n                return -1;\n            } else if (aStatus !== \"Medium\" && bStatus === \"Medium\") {\n                return 1;\n            } else if (aStatus === \"Medium\" && bStatus === \"Medium\") {\n                return dayjs__WEBPACK_IMPORTED_MODULE_9___default()(b.startDate).diff(a.startDate);\n            } else if (aStatus === \"High\" && bStatus === \"High\") {\n                var _a_isOverDue1, _a_isOverDue_days_match, _b_isOverDue1, _b_isOverDue_days_match;\n                const aDays = ((_a_isOverDue1 = a.isOverDue) === null || _a_isOverDue1 === void 0 ? void 0 : _a_isOverDue1.days) ? parseInt(((_a_isOverDue_days_match = a.isOverDue.days.match(/(\\d+)/)) === null || _a_isOverDue_days_match === void 0 ? void 0 : _a_isOverDue_days_match[0]) || \"0\") : 0;\n                const bDays = ((_b_isOverDue1 = b.isOverDue) === null || _b_isOverDue1 === void 0 ? void 0 : _b_isOverDue1.days) ? parseInt(((_b_isOverDue_days_match = b.isOverDue.days.match(/(\\d+)/)) === null || _b_isOverDue_days_match === void 0 ? void 0 : _b_isOverDue_days_match[0]) || \"0\") : 0;\n                return bDays - aDays;\n            } else {\n                // rest of the sort logic remains the same\n                if (a.isCompleted === \"1\" && b.isCompleted === \"1\") {\n                    if (a.expires === \"NA\" && b.expires !== \"NA\") {\n                        return 1;\n                    } else if (a.expires !== \"NA\" && b.expires === \"NA\") {\n                        return -1;\n                    } else {\n                        return new Date(b.expires).getTime() - new Date(a.expires).getTime();\n                    }\n                } else if (a.isCompleted === \"1\") {\n                    return 1;\n                } else if (b.isCompleted === \"1\") {\n                    return -1;\n                } else {\n                    return dayjs__WEBPACK_IMPORTED_MODULE_9___default()(a.expires).diff(b.expires);\n                }\n            }\n        });\n        setMaintenanceTasks(deduplicatedTasks);\n        // setMaintenanceTasks(\n        //     deduplicatedTasks.filter((task: any) => {\n        //         return task?.archived !== 1\n        //     }),\n        // )\n        const appendedData = Array.from(new Set(deduplicatedTasks.filter((task)=>task.assignedToID > 0).map((task)=>task.assignedToID)));\n        loadCrewMemberInfo(appendedData, true);\n        // const tomorrow = new Date()\n        // tomorrow.setDate(tomorrow.getDate() + 1)\n        // const taskCounter = deduplicatedTasks.filter(\n        //     (task: any) =>\n        //         task.status !== 'Completed' &&\n        //         task.status !== 'Save_As_Draft' &&\n        //         task.isOverDue?.ignore !== true,\n        // ).length\n        const taskCounter = (0,_app_helpers_vesselHelper__WEBPACK_IMPORTED_MODULE_14__.getTasksDueCount)(vesselId);\n        setTaskCounter(taskCounter);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_7__.getComponentMaintenanceCheckByVesselId)(vesselId, handleSetMaintenanceTasks);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        handleSetMaintenanceTasks(maintenanceTasks);\n    }, [\n        inventories\n    ]);\n    const loadLogEntry = async (logEntryId)=>{\n        await queryLogEntry({\n            variables: {\n                logbookEntryId: logEntryId\n            }\n        });\n    };\n    const loadCrewMemberInfo = async function(crewIds) {\n        let task = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (crewIds.length > 0) {\n            task ? await queryTaskMembersInfo({\n                variables: {\n                    crewMemberIDs: crewIds\n                }\n            }) : await queryCrewMemberInfo({\n                variables: {\n                    crewMemberIDs: crewIds\n                }\n            });\n        }\n    };\n    const [queryLogEntry] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_LOGBOOK_ENTRY_BY_ID, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneLogBookEntry;\n            if (data) {\n                setCurrentLogEntry(data);\n            }\n        }\n    });\n    const [queryCrewMemberInfo] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_CREW_BY_IDS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers.nodes;\n            if (data) {\n                setCrewInfo(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryCrewMemberInfo error\", error);\n        }\n    });\n    const [queryTaskMembersInfo] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_CREW_BY_IDS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers.nodes;\n            if (data) {\n                setTaskCrewInfo(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryTaskMembersInfo error\", error);\n        }\n    });\n    const handlePagination = (page)=>{\n        if (page < 0 || currentPage === page) {\n            return;\n        }\n        setCurrentPage(page);\n    };\n    const deleteFile = (fileId)=>{\n        const newDocuments = documents.filter((doc)=>doc.id !== fileId);\n        setDocuments(newDocuments);\n        updateVessel({\n            variables: {\n                input: {\n                    id: vesselId,\n                    documents: newDocuments.map((doc)=>doc.id).join(\",\")\n                }\n            }\n        });\n    };\n    const deleteAllFiles = (document)=>{\n        const newDocuments = [];\n        setDocuments(newDocuments);\n        updateVessel({\n            variables: {\n                input: {\n                    id: vesselId,\n                    documents: newDocuments.map((doc)=>doc.id).join(\",\")\n                }\n            }\n        });\n    };\n    const [updateVessel] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_32__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UPDATE_VESSEL, {\n        onError: (error)=>{\n            console.error(\"updateVessel error\", error);\n        }\n    });\n    const handleUpdateVesselCrew = async ()=>{\n        await updateVessel({\n            variables: {\n                input: {\n                    id: vesselId,\n                    seaLogsMembers: vesselCrewIDs.join(\",\")\n                }\n            }\n        });\n        setDisplayAddCrew(false);\n        loadCrewMemberInfo(vesselCrewIDs);\n    };\n    const handleOnChangeVesselCrew = (data)=>{\n        setVesselCrewIDs(data.map((item)=>item.value));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (vesselTab === \"documents\") {\n            updateVessel({\n                variables: {\n                    input: {\n                        id: vesselId,\n                        documents: documents.map((doc)=>doc.id).join(\",\")\n                    }\n                }\n            });\n        }\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_8___default()(tab)) {\n            setVesselTab(tab);\n        }\n    }, [\n        documents,\n        tab\n    ]);\n    const handleCreateNewLogEntry = async ()=>{\n        if (!edit_logBookEntry) {\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"You do not have permission to create a new log entry\");\n            return;\n        }\n        if (hasLogbookOpen) {\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.custom((t)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-destructive text-destructive-foreground p-4 rounded-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-semibold\",\n                            children: \"Error\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                            lineNumber: 777,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                \"Please complete the open log entry\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    href: \"/log-entries?vesselID=\".concat(vesselId, \"&logentryID=\").concat(logbooks.filter((entry)=>entry.state !== \"Locked\")[0].id),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"underline\",\n                                        children: \"here\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                        lineNumber: 786,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                    lineNumber: 780,\n                                    columnNumber: 25\n                                }, this),\n                                \" \",\n                                \"before creating a new one.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                            lineNumber: 778,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                    lineNumber: 776,\n                    columnNumber: 17\n                }, this));\n        } else if ((vessel === null || vessel === void 0 ? void 0 : vessel.logBookID) > 0) {\n            setIsNewLogEntryDisabled(true);\n            await createLogEntry({\n                variables: {\n                    input: {\n                        logBookID: vessel.logBookID,\n                        vehicleID: vesselId\n                    }\n                }\n            });\n        }\n    };\n    const [createLogEntry] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_32__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CREATE_LOGBOOK_ENTRY, {\n        onCompleted: (response)=>{\n            router.push(\"/log-entries?vesselID=\".concat(vesselId, \"&logentryID=\").concat(response.createLogBookEntry.id));\n        },\n        onError: (error)=>{\n            console.error(\"createLogEntry error\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(error.message);\n        }\n    });\n    const hasLogbookOpen = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(()=>{\n        return logbooks.filter((entry)=>entry.state !== \"Locked\").length > 0;\n    }, [\n        logbooks\n    ]);\n    const vesselStatusLabel = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(()=>{\n        if ((vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.status) === \"OutOfService\") {\n            return \"Out Of Service\";\n        }\n        return hasLogbookOpen ? \"On voyage\" : \"Ready for voyage\";\n    }, [\n        hasLogbookOpen,\n        vesselStatus\n    ]);\n    const vesselStatusVariant = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(()=>{\n        if ((vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.status) === \"OutOfService\") {\n            return \"destructive\" // Red for out of service\n            ;\n        }\n        return hasLogbookOpen ? \"warning\" : \"success\" // Orange for on voyage, green for ready\n        ;\n    }, [\n        hasLogbookOpen,\n        vesselStatus\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        setImCrew((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.isCrew)() || false);\n    }, []);\n    const scrollToTabs = ()=>{\n        if (tabsRef.current) {\n            tabsRef.current.scrollIntoView({\n                behavior: \"smooth\",\n                block: \"start\"\n            });\n        }\n    };\n    const addButton = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"hidden md:flex flex-row justify-end\",\n        children: [\n            hasLogbookOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"invisible\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                lineNumber: 855,\n                columnNumber: 17\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: vesselTab === \"logEntries\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: !imCrew && /*<SeaLogsButton\r\n                                    text=\"New log entry\"\r\n                                    type=\"secondary\"\r\n                                    icon=\"new_logbook\"\r\n                                    color=\"slblue\"\r\n                                    action={handleCreateNewLogEntry}\r\n                                    isDisabled={isNewLogEntryDisabled}\r\n                                />*/ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                        disabled: isNewLogEntryDisabled,\n                        onClick: handleCreateNewLogEntry,\n                        children: \"New log entry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                        lineNumber: 869,\n                        columnNumber: 33\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                    lineNumber: 859,\n                    columnNumber: 25\n                }, this)\n            }, void 0, false),\n            vesselTab === \"crew\" && !imCrew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                onClick: ()=>{\n                    var _vessel_seaLogsMembers;\n                    setVesselCrewIDs(vessel === null || vessel === void 0 ? void 0 : (_vessel_seaLogsMembers = vessel.seaLogsMembers) === null || _vessel_seaLogsMembers === void 0 ? void 0 : _vessel_seaLogsMembers.nodes.map((crew)=>crew.id));\n                    setDisplayAddCrew(true);\n                },\n                children: \"Add crew\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                lineNumber: 880,\n                columnNumber: 17\n            }, this),\n            vesselTab === \"maintenance\" && /*<SeaLogsButton\r\n                    text=\"Add task\"\r\n                    type=\"primary\"\r\n                    icon=\"check\"\r\n                    color=\"slblue\"\r\n                    // link={`/maintenance/new?vesselID=${vesselId}&redirect_to=${pathname}?${searchParams.toString()}%26tab=maintenance`}\r\n                    action={() => {\r\n                        router.push(\r\n                            `/maintenance/new?vesselID=${vesselId}&redirect_to=${pathname}?${searchParams.toString()}%26tab=maintenance`,\r\n                        )\r\n                    }}\r\n                />*/ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                onClick: ()=>{\n                    router.push(\"/maintenance/new?vesselID=\".concat(vesselId, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString(), \"%26tab=maintenance\"));\n                },\n                children: \"Add task\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                lineNumber: 905,\n                columnNumber: 17\n            }, this),\n            permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.hasPermission)(\"RECORD_TRAINING\", permissions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: vesselTab === \"crew_training\" && /*<SeaLogsButton\r\n                                text=\"Add Training\"\r\n                                type=\"primary\"\r\n                                icon=\"check\"\r\n                                color=\"slblue\"\r\n                                // link={`/crew-training/create?vesselID=${vesselId}`}\r\n                                action={() => {\r\n                                    router.push(\r\n                                        `/crew-training/create?vesselID=${vesselId}`,\r\n                                    )\r\n                                }}\r\n                            />*/ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                    onClick: ()=>{\n                        router.push(\"/crew-training/create?vesselID=\".concat(vesselId));\n                    },\n                    children: \"Add Training\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                    lineNumber: 929,\n                    columnNumber: 25\n                }, this)\n            }, void 0, false),\n            vesselTab === \"inventory\" && !imCrew && /*<SeaLogsButton\r\n                    text=\"Add Inventory\"\r\n                    type=\"primary\"\r\n                    icon=\"check\"\r\n                    color=\"slblue\"\r\n                    // link={`/inventory/new?vesselID=${vesselId}&redirectTo=${pathname}?${searchParams.toString()}%26tab=inventory`}\r\n                    action={() => {\r\n                        router.push(\r\n                            `/inventory/new?vesselID=${vesselId}&redirectTo=${pathname}?${searchParams.toString()}%26tab=inventory`,\r\n                        )\r\n                    }}\r\n                />*/ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                onClick: ()=>{\n                    router.push(\"/inventory/new?vesselID=\".concat(vesselId, \"&redirectTo=\").concat(pathname, \"?\").concat(searchParams.toString(), \"%26tab=inventory\"));\n                },\n                children: \"Add Inventory\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                lineNumber: 953,\n                columnNumber: 17\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n        lineNumber: 853,\n        columnNumber: 9\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    vessel && (vessel === null || vessel === void 0 ? void 0 : (_vessel_statusHistory = vessel.statusHistory) === null || _vessel_statusHistory === void 0 ? void 0 : (_vessel_statusHistory_nodes = _vessel_statusHistory.nodes) === null || _vessel_statusHistory_nodes === void 0 ? void 0 : _vessel_statusHistory_nodes.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_28__.Badge, {\n                        variant: vesselStatusVariant,\n                        type: \"normal\",\n                        className: \"flex flex-wrap absolute top-2 right-2 md:flex-inline items-center px-1 phablet:px-4 gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Status:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                lineNumber: 973,\n                                columnNumber: 25\n                            }, this),\n                            vesselStatusLabel,\n                            hasLogbookOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                size: \"sm\",\n                                variant: \"outline\",\n                                className: \"py-0 h-full\",\n                                //iconLeft={PencilIcon}\n                                onClick: ()=>setDisplayEditStatus(true),\n                                children: \"Edit\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                lineNumber: 977,\n                                columnNumber: 29\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                        lineNumber: 969,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header_image__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        bannerImage: bannerImage\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                        lineNumber: 988,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                lineNumber: 967,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full flex flex-col gap-8 overflow-hidden relative px-1 phablet:px-4 -mt-20 md:-mt-24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-col-1 lg:grid-cols-3 gap-8 lg:gap-6 xl:gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_logbook_entries_card__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            vesselId: vesselId,\n                            logbooks: logbooks,\n                            imCrew: imCrew,\n                            handleCreateNewLogEntry: handleCreateNewLogEntry,\n                            isNewLogEntryDisabled: isNewLogEntryDisabled,\n                            setVesselTab: setVesselTab,\n                            vesselTitle: vessel === null || vessel === void 0 ? void 0 : vessel.title,\n                            scrollToTabs: scrollToTabs\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                            lineNumber: 992,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_maintenance_card__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            maintenanceTasks: maintenanceTasks,\n                            pathname: pathname,\n                            setVesselTab: setVesselTab,\n                            scrollToTabs: scrollToTabs\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                            lineNumber: 1003,\n                            columnNumber: 21\n                        }, this),\n                        permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.hasPermission)(\"VIEW_TRAINING\", permissions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_training_drills_card__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            trainingSessionDuesSummary: trainingSessionDuesSummary,\n                            setVesselTab: setVesselTab,\n                            scrollToTabs: scrollToTabs\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                            lineNumber: 1012,\n                            columnNumber: 29\n                        }, this),\n                        isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_crew_card__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    crewInfo: crewInfo,\n                                    setVesselTab: setVesselTab,\n                                    vesselId: vesselId,\n                                    pathname: pathname\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                    lineNumber: 1023,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_card__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    inventories: inventories,\n                                    setVesselTab: setVesselTab,\n                                    vesselId: vesselId,\n                                    pathname: pathname\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                    lineNumber: 1030,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                    lineNumber: 991,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                lineNumber: 990,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-1 phablet:px-4 hidden md:block\",\n                ref: tabsRef,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_tabs_holder__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                    taskCounter: taskCounter,\n                    trainingDueCounter: trainingsDueCount,\n                    vessel: vessel,\n                    engineList: engineList,\n                    fuelTankList: fuelTankList,\n                    waterTankList: waterTankList,\n                    sewageSystemList: sewageSystemList,\n                    vesselId: vesselId,\n                    logbooks: logbooks,\n                    totalEntries: totalEntries,\n                    perPage: perPage,\n                    handlePagination: handlePagination,\n                    currentPage: currentPage,\n                    maintenanceTasks: maintenanceTasks,\n                    crewInfo: crewInfo,\n                    trainingSessions: trainingSessions,\n                    trainingSessionDues: trainingSessionDues,\n                    inventories: inventories,\n                    imCrew: imCrew,\n                    edit_docs: edit_docs,\n                    setDocuments: setDocuments,\n                    documents: documents,\n                    delete_docs: delete_docs,\n                    deleteFile: deleteFile\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                    lineNumber: 1043,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                lineNumber: 1041,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_28__.AlertDialogNew, {\n                openDialog: displayAddCrew,\n                setOpenDialog: setDisplayAddCrew,\n                handleCreate: handleUpdateVesselCrew,\n                title: \"Add Crew\",\n                actionText: \"Add Crew\",\n                children: vessel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_multiselect_dropdown_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    value: vesselCrewIDs,\n                    onChange: handleOnChangeVesselCrew,\n                    departments: vessel === null || vessel === void 0 ? void 0 : (_vessel_departments = vessel.departments) === null || _vessel_departments === void 0 ? void 0 : _vessel_departments.nodes\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                    lineNumber: 1077,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                lineNumber: 1070,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_28__.AlertDialogNew, {\n                size: \"xl\",\n                openDialog: displayEditStatus,\n                setOpenDialog: setDisplayEditStatus,\n                handleCreate: handleUpdateVesselStatus,\n                title: \"Update Vessel Status\",\n                actionText: \"Update\",\n                className: \"space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DateRange__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                        mode: \"single\",\n                        onChange: handleVesselStatusDate,\n                        placeholder: \"Select date\",\n                        value: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.date\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                        lineNumber: 1092,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2.5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_28__.Label, {\n                                label: \"Status\",\n                                className: \"flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_28__.Combobox, {\n                                    id: \"vessel-status\",\n                                    options: vesselStatuses,\n                                    placeholder: \"Status\",\n                                    value: vesselStatuses.find((status)=>(vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.status) === status.value),\n                                    onChange: handleVesselStatusChange\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                    lineNumber: 1100,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                lineNumber: 1099,\n                                columnNumber: 21\n                            }, this),\n                            (vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.status) === \"OutOfService\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_28__.Label, {\n                                label: \"Reason for out of service\",\n                                className: \"flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_28__.Combobox, {\n                                    id: \"vessel-status-reason\",\n                                    options: vesselStatusReason,\n                                    placeholder: \"Reason\",\n                                    value: vesselStatusReason.find((status)=>(vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.reason) === status.value),\n                                    onChange: handleVesselStatusReasonChange\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                    lineNumber: 1115,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                lineNumber: 1112,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                        lineNumber: 1098,\n                        columnNumber: 17\n                    }, this),\n                    (vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.status) === \"OutOfService\" && (vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.reason) === \"Other\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_28__.Textarea, {\n                        id: \"vessel-status-other\",\n                        placeholder: \"Other description\",\n                        value: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.otherReason,\n                        onChange: (e)=>setVesselStatus({\n                                ...vesselStatus,\n                                otherReason: e.target.value\n                            })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                        lineNumber: 1131,\n                        columnNumber: 25\n                    }, this),\n                    (vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.status) === \"OutOfService\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_28__.Label, {\n                        label: \"Comments\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_editor__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                            id: \"comment\",\n                            placeholder: \"Comment\",\n                            className: \"bg-background\",\n                            content: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.comment,\n                            handleEditorChange: (content)=>setVesselStatus({\n                                    ...vesselStatus,\n                                    comment: content\n                                })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                            lineNumber: 1145,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                        lineNumber: 1144,\n                        columnNumber: 21\n                    }, this),\n                    (vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.status) === \"OutOfService\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_28__.Label, {\n                        label: \"Expected date of return\",\n                        htmlFor: \"expected-return-date\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DateRange__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                            className: \"flex w-full\",\n                            mode: \"single\",\n                            onChange: handleVesselStatusReturnDate,\n                            placeholder: \"Select date\",\n                            value: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.expectedReturn\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                            lineNumber: 1163,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                        lineNumber: 1160,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                lineNumber: 1084,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_footer_wrapper__WEBPACK_IMPORTED_MODULE_27__.FooterWrapper, {\n                className: \" justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                        lineNumber: 1175,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                variant: \"back\",\n                                size: \"sm\",\n                                onClick: ()=>router.back(),\n                                iconLeft: _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"],\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                lineNumber: 1177,\n                                columnNumber: 21\n                            }, this),\n                            !imCrew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_26__.DropdownMenu, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_26__.DropdownMenuTrigger, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            size: \"sm\",\n                                            /*iconLeft={\r\n                                        <svg\r\n                                            key={'Edit'}\r\n                                            className=\"-ml-0.5 mr-1.5 h-5 w-5 group-hover:border-white\"\r\n                                            viewBox=\"0 0 36 36\"\r\n                                            fill=\"currentColor\"\r\n                                            aria-hidden=\"true\">\r\n                                            <path d=\"M33.87,8.32,28,2.42a2.07,2.07,0,0,0-2.92,0L4.27,23.2l-1.9,8.2a2.06,2.06,0,0,0,2,2.5,2.14,2.14,0,0,0,.43,0L13.09,32,33.87,11.24A2.07,2.07,0,0,0,33.87,8.32ZM12.09,30.2,4.32,31.83l1.77-7.62L21.66,8.7l6,6ZM29,13.25l-6-6,3.48-3.46,5.9,6Z\"></path>\r\n                                        </svg>\r\n                                    }*/ variant: \"outline\",\n                                            children: \"Edit\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                            lineNumber: 1187,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                        lineNumber: 1186,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_26__.DropdownMenuContent, {\n                                        align: \"end\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_26__.DropdownMenuGroup, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_26__.DropdownMenuItem, {\n                                                    onClick: ()=>{\n                                                        router.push(\"/vessel/edit?id=\".concat(vesselId));\n                                                    },\n                                                    children: \"Edit Vessel\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                                    lineNumber: 1205,\n                                                    columnNumber: 37\n                                                }, this),\n                                                (vessel === null || vessel === void 0 ? void 0 : vessel.logBookID) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_26__.DropdownMenuItem, {\n                                                    onClick: ()=>{\n                                                        router.push(\"/vessel/logbook-configuration?logBookID=\".concat(vessel.logBookID, \"&vesselID=\").concat(vesselId));\n                                                    },\n                                                    children: \"Edit Logbook Configuration\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                                    lineNumber: 1214,\n                                                    columnNumber: 41\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                            lineNumber: 1204,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                        lineNumber: 1203,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                lineNumber: 1185,\n                                columnNumber: 25\n                            }, this),\n                            addButton\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                        lineNumber: 1176,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                lineNumber: 1174,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(VesselsView, \"JByq6WniPGNLPzCblYspC7Xyji4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter,\n        nuqs__WEBPACK_IMPORTED_MODULE_31__.useQueryState,\n        _components_hooks_use_mobile__WEBPACK_IMPORTED_MODULE_12__.useIsMobile,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_32__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_32__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_32__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_32__.useMutation\n    ];\n});\n_c = VesselsView;\nvar _c;\n$RefreshReg$(_c, \"VesselsView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/vessels/view.tsx\n"));

/***/ })

});