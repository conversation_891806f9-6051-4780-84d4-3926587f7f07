'use client'

// React and Next.js imports
import { useEffect, useMemo, useRef, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import { useQueryState } from 'nuqs'
import { useMutation, useLazyQuery } from '@apollo/client'
import {
    CreateLogBookEntrySection_Signature,
    CreateTripReport_LogBookEntrySection,
    CreateTripReport_Stop,
    UpdateLogBookEntrySection_Signature,
    UpdateTripReport_LogBookEntrySection,
    DeleteTripReport_LogBookEntrySections,
} from '@/app/lib/graphQL/mutation'
import {
    GET_GEO_LOCATIONS,
    GET_EVENT_TYPES,
    ReadTripReportSchedules,
    ReadTripScheduleServices,
} from '@/app/lib/graphQL/query'

// Utility imports
import dayjs from 'dayjs'
import { isEmpty, trim, uniqueId } from 'lodash'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import { getOneClient, getVesselByID } from '@/app/lib/actions'
import { generateUniqueId } from '@/app/offline/helpers/functions'

// Model imports
import ClientModel from '@/app/offline/models/client'
import VesselModel from '@/app/offline/models/vessel'
import GeoLocationModel from '@/app/offline/models/geoLocation'
import EventTypeModel from '@/app/offline/models/eventType'
import TripReport_LogBookEntrySectionModel from '@/app/offline/models/tripReport_LogBookEntrySection'

// UI Component imports
import { Plus, Check, ArrowLeft, Trash2 } from 'lucide-react'
import { toast } from 'sonner'
import { H5, P } from '@/components/ui/typography'
import { Button } from '@/components/ui/button'
import { Combobox } from '@/components/ui/comboBox'
import { CheckFieldLabel } from '@/components/ui/check-field-label'
import { Separator } from '@/components/ui/separator'
import { Label } from '@/components/ui/label'
import {
    Accordion,
    AccordionItem,
    AccordionTrigger,
    AccordionContent,
} from '@/components/ui/accordion'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'

// Custom component imports
import Loading from '@/app/loading'
import SignaturePad from '@/components/signature-pad'
import LocationField from './components/location'
import DepartTime from './depart-time'
import ExpectedArrival from './exp-arrival-time'
import ActualArrival from './actual-arrival-time'
import Events from './events'
import POB from './trip-log-pob'
import VOB from './trip-log-vob'
import DGR from './trip-log-dgr'
import TripComments from './components/trip-comments'
import { cn } from '@/app/lib/utils'
import { FormFooter } from '@/components/ui'
import Master from './components/master'
import { isVesselArrivalLate } from '@/app/helpers/dateHelper'
import { useResponsiveLabel } from '../../../../utils/responsiveLabel'
import { useBreakpoints } from '@/components/hooks/useBreakpoints'

// Reusable TripReportAccordionContent component
const TripReportAccordionContent = ({
    currentTrip,
    offline,
    tripReport,
    updateTripReport,
    updateTripReport_LogBookEntrySection,
    currentTripRef,
    locations,
    locked,
    edit_tripReport,
    vessel,
    crewMembers,
    logBookConfig,
    client,
    canCarryVehicles,
    canCarryDangerousGoods,
    selectedDGR,
    displayDangerousGoods,
    setDisplayDangerousGoods,
    displayDangerousGoodsSailing,
    setDisplayDangerousGoodsSailing,
    allDangerousGoods,
    setAllDangerousGoods,
    logBookStartDate,
    masterID,
    vessels,
    setSelectedRowEvent,
    setCurrentEventTypeEvent,
    setCurrentStopEvent,
    currentEventTypeEvent,
    currentStopEvent,
    tripReport_Stops,
    setTripReport_Stops,
    displayDangerousGoodsPvpd,
    setDisplayDangerousGoodsPvpd,
    displayDangerousGoodsPvpdSailing,
    setDisplayDangerousGoodsPvpdSailing,
    allPVPDDangerousGoods,
    setAllPVPDDangerousGoods,
    selectedDGRPVPD,
    setSelectedDGRPVPD,
    fuelLogs,
    comment,
    setComment,
    displayFieldTripLog,
    signatureKey,
    signature,
    setSignature,
    handleCancel,
    handleSave,
}: any) => {
    return (
        <div
            className={cn(
                'space-y-6',
                locked || !edit_tripReport
                    ? 'opacity-70 pointer-events-none'
                    : '',
            )}>
            <div className="space-y-8 relative">
                <div className="flex flex-col inset-y-0 items-center pt-10 pb-4 absolute -left-5 sm:-left-[25px] min-h-24 w-5">
                    <div
                        className={cn(
                            'size-[11px] z-10 rounded-full',
                            'border border-outer-space-400 bg-background',
                        )}
                    />
                    <div
                        className={cn(
                            'border-l h-full border-wedgewood-200 border-dashed',
                        )}
                    />
                    <div
                        className={cn(
                            'size-[11px] z-10 rounded-full',
                            'border border-outer-space-400 bg-background',
                        )}
                    />
                </div>
                <DepartTime
                    offline={offline}
                    currentTrip={currentTrip}
                    tripReport={tripReport}
                    templateStyle={''}
                    updateTripReport={updateTripReport}
                />
                <Label label="Departure location">
                    <LocationField
                        offline={offline}
                        setCurrentLocation={(location: {
                            latitude: number
                            longitude: number
                        }) => {
                            // Store coordinates if needed for direct coordinate input
                        }}
                        handleLocationChange={(selectedLocation: {
                            value: string
                            label: string
                        }) => {
                            // Update the from location
                            if (offline) {
                                updateTripReport({
                                    id: [
                                        ...tripReport.map(
                                            (trip: any) => trip.id,
                                        ),
                                        currentTrip.id,
                                    ],
                                    currentTripID: currentTrip.id,
                                    key: 'fromLocationID',
                                    value: selectedLocation.value,
                                    label: selectedLocation.label,
                                })
                            } else {
                                // For online mode, use the mutation
                                updateTripReport_LogBookEntrySection({
                                    variables: {
                                        input: {
                                            id: currentTripRef.current?.id,
                                            fromLocationID:
                                                selectedLocation.value,
                                        },
                                    },
                                })
                            }
                        }}
                        currentEvent={{
                            geoLocationID: currentTrip?.fromLocationID || 0,
                            lat: currentTrip?.fromLat || 0,
                            long: currentTrip?.fromLong || 0,
                        }}
                        showAddNewLocation={true}
                        showUseCoordinates={true}
                        showCurrentLocation={true}
                    />
                </Label>
                <Separator />
                <H5>PEOPLE ON BOARD</H5>
                <POB
                    offline={offline}
                    currentTrip={currentTrip}
                    tripReport={tripReport}
                    vessel={vessel}
                    crewMembers={crewMembers}
                    logBookConfig={logBookConfig}
                    masterTerm={client?.masterTerm}
                    updateTripReport={updateTripReport}
                />
                <Separator />
                <div className="space-y-8">
                    {canCarryVehicles && (
                        <VOB
                            offline={offline}
                            currentTrip={currentTrip}
                            logBookConfig={logBookConfig}
                        />
                    )}

                    {/* Dangerous Goods section */}
                    {canCarryDangerousGoods && (
                        <DGR
                            offline={offline}
                            locked={locked || !edit_tripReport}
                            currentTrip={currentTrip}
                            logBookConfig={logBookConfig}
                            selectedDGR={selectedDGR}
                            members={crewMembers}
                            displayDangerousGoods={displayDangerousGoods}
                            setDisplayDangerousGoods={setDisplayDangerousGoods}
                            displayDangerousGoodsSailing={
                                displayDangerousGoodsSailing
                            }
                            setDisplayDangerousGoodsSailing={
                                setDisplayDangerousGoodsSailing
                            }
                            allDangerousGoods={allDangerousGoods}
                            setAllDangerousGoods={setAllDangerousGoods}
                        />
                    )}
                    <Separator />
                </div>

                <Events
                    offline={offline}
                    logBookStartDate={logBookStartDate}
                    currentTrip={currentTrip}
                    logBookConfig={logBookConfig}
                    updateTripReport={updateTripReport}
                    locked={locked}
                    geoLocations={locations}
                    tripReport={tripReport}
                    crewMembers={crewMembers}
                    masterID={masterID}
                    vessel={vessel}
                    vessels={vessels}
                    setSelectedRow={setSelectedRowEvent}
                    setCurrentEventType={setCurrentEventTypeEvent}
                    setCurrentStop={setCurrentStopEvent}
                    currentEventType={currentEventTypeEvent}
                    currentStop={currentStopEvent}
                    tripReport_Stops={tripReport_Stops}
                    setTripReport_Stops={setTripReport_Stops}
                    displayDangerousGoodsPvpd={displayDangerousGoodsPvpd}
                    setDisplayDangerousGoodsPvpd={setDisplayDangerousGoodsPvpd}
                    displayDangerousGoodsPvpdSailing={
                        displayDangerousGoodsPvpdSailing
                    }
                    setDisplayDangerousGoodsPvpdSailing={
                        setDisplayDangerousGoodsPvpdSailing
                    }
                    allPVPDDangerousGoods={allPVPDDangerousGoods}
                    setAllPVPDDangerousGoods={setAllPVPDDangerousGoods}
                    selectedDGRPVPD={selectedDGRPVPD}
                    setSelectedDGRPVPD={setSelectedDGRPVPD}
                    fuelLogs={fuelLogs}
                />
                <Separator />
                <Label label="Arrival location">
                    <LocationField
                        offline={offline}
                        setCurrentLocation={(location: {
                            latitude: number
                            longitude: number
                        }) => {
                            // Store coordinates if needed for direct coordinate input
                        }}
                        handleLocationChange={(selectedLoc: {
                            value: string
                            label: string
                        }) => {
                            // Update the to location
                            if (offline) {
                                updateTripReport({
                                    id: [
                                        ...tripReport.map(
                                            (trip: any) => trip.id,
                                        ),
                                        currentTrip.id,
                                    ],
                                    currentTripID: currentTrip.id,
                                    key: 'toLocationID',
                                    value: selectedLoc.value,
                                    label: selectedLoc.label,
                                })
                            } else {
                                // For online mode, use the mutation
                                updateTripReport_LogBookEntrySection({
                                    variables: {
                                        input: {
                                            id: currentTripRef.current?.id,
                                            toLocationID: selectedLoc.value,
                                        },
                                    },
                                })
                            }
                        }}
                        currentEvent={{
                            geoLocationID: currentTrip?.toLocationID || 0,
                            lat: currentTrip?.toLat || 0,
                            long: currentTrip?.toLong || 0,
                        }}
                        showAddNewLocation={true}
                        showUseCoordinates={true}
                        showCurrentLocation={true}
                    />
                </Label>
            </div>

            <ExpectedArrival
                offline={offline}
                currentTrip={currentTrip}
                tripReport={tripReport}
                updateTripReport={updateTripReport}
            />

            <ActualArrival
                offline={offline}
                currentTrip={currentTrip}
                tripReport={tripReport}
                updateTripReport={updateTripReport}
            />

            <TripComments
                offline={offline}
                currentTrip={currentTrip}
                tripReport={tripReport}
                setCommentField={setComment}
                updateTripReport={updateTripReport}
            />
            {displayFieldTripLog('MasterID') && (
                <div
                    className={`${locked || !edit_tripReport ? 'pointer-events-none' : ''} max-w-sm`}>
                    <Master
                        offline={offline}
                        currentTrip={currentTrip}
                        tripReport={tripReport}
                        crewMembers={crewMembers}
                        updateTripReport={updateTripReport}
                    />
                </div>
            )}

            {displayFieldTripLog('Signature') && (
                <SignaturePad
                    key={`${signatureKey}-${currentTrip.id}`}
                    locked={locked}
                    title="Signature Confirmation"
                    description="By signing below, I confirm that the recorded entries are accurate to the best of my knowledge and in accordance with the vessel's operating procedures and regulations."
                    signature={currentTripRef?.current?.sectionSignature}
                    onSignatureChanged={(sign: string) => {
                        setSignature(sign)
                    }}
                />
            )}

            {!locked && (
                <FormFooter className="justify-end flex-nowrap xs:gap-6">
                    <Button variant="back" onClick={handleCancel}>
                        Cancel
                    </Button>
                    <Button iconLeft={Check} onClick={handleSave}>
                        Update
                    </Button>
                </FormFooter>
            )}
        </div>
    )
}

export default function TripLog({
    tripReport = false,
    logBookConfig,
    updateTripReport,
    locked,
    crewMembers,
    masterID,
    createdTab = false,
    setCreatedTab,
    currentTrip = false,
    setCurrentTrip,
    vessels,
    offline = false,
    fuelLogs,
    logBookStartDate,
}: {
    tripReport: any
    logBookConfig: any
    updateTripReport: any
    locked: boolean
    crewMembers: any
    masterID: any
    createdTab: any
    setCreatedTab: any
    currentTrip: any
    setCurrentTrip: any
    vessels: any
    offline?: boolean
    fuelLogs?: any
    logBookStartDate: any
}) {
    // const router = useRouter()
    const searchParams = useSearchParams()
    const logentryID = searchParams.get('logentryID') ?? 0
    const vesselID = searchParams.get('vesselID') ?? 0
    const allowedVesselTypes = [
        'SLALL',
        'Tug_Boat',
        'Passenger_Ferry',
        'Water_Taxi',
    ]

    // Use nuqs to manage the tab state through URL query parameters
    const [tab, setTab] = useQueryState('tab', { defaultValue: 'crew' })
    const [tabTSS, setTabTSS] = useQueryState('tss', { defaultValue: '' })
    const [locations, setLocations] = useState<any>(false)
    const [eventTypes, setEventTypes] = useState<any>(false)
    const [openTripModal, setOpenTripModal] = useState(false)
    const [bufferTripID, setBufferTripID] = useState(0)
    const [vessel, setVessel] = useState<any>(false)
    const [selectedTab, setSelectedTab] = useState<any>(false)
    const [accordionValue, setAccordionValue] = useState<string>('')
    const [selectedDGR, setSelectedDGR] = useState<any>(0)
    const [openRiskAnalysis, setOpenTripStartRiskAnalysis] = useState(false)
    const [displayDangerousGoods, setDisplayDangerousGoods] = useState(false)
    const [displayDangerousGoodsSailing, setDisplayDangerousGoodsSailing] =
        useState(false)
    const [displayDangerousGoodsPvpd, setDisplayDangerousGoodsPvpd] =
        useState(false)
    const [
        displayDangerousGoodsPvpdSailing,
        setDisplayDangerousGoodsPvpdSailing,
    ] = useState<boolean | null>(null)
    // const [openEventModal, setOpenEventModal] = useState(false)
    // const [selectedRowdgr, setSelectedRowdgr] = useState<any>(false)
    const [tripReport_Stops, setTripReport_Stops] = useState<any>(false)
    const [selectedDGRPVPD, setSelectedDGRPVPD] = useState<any>(0)
    const [allPVPDDangerousGoods, setAllPVPDDangerousGoods] =
        useState<any>(false)
    const [selectedRowEvent, setSelectedRowEvent] = useState<any>(0)
    const [riskBufferEvDgr, setRiskBufferEvDgr] = useState<any>(false)
    const [allDangerousGoods, setAllDangerousGoods] = useState<any>(false)
    const [currentEventTypeEvent, setCurrentEventTypeEvent] =
        useState<any>(false)
    const [currentStopEvent, setCurrentStopEvent] = useState<any>(false)
    const [client, setClient] = useState<any>()
    const [signature, setSignature] = useState<any>(false)
    const [signatureKey, setSignatureKey] = useState<any>(uniqueId())
    const currentTripRef = useRef<any>(null)
    const [deleteTripItem, setDeleteTripItem] = useState<any>(false)
    const [deleteTripConfirmation, setDeleteTripConfirmation] = useState(false)

    const canCarryDangerousGoods = useMemo(() => {
        return !!vessel?.vesselSpecifics?.carriesDangerousGoods
    }, [vessel])

    const canCarryVehicles = useMemo(() => {
        return !!vessel?.vesselSpecifics?.carriesVehicles
    }, [vessel])

    // Initialize client
    if (!offline) {
        getOneClient(setClient)
    }

    // Update signature state when currentTrip changes
    useEffect(() => {
        if (currentTrip && currentTrip.sectionSignature) {
            setSignature(currentTrip.sectionSignature.signatureData || '')
        } else {
            setSignature('')
        }
    }, [currentTrip])
    const [comment, setComment] = useState<string>(
        tripReport
            ? tripReport?.find((trip: any) => trip.id === currentTrip.id)
                  ?.comment
            : '',
    )

    const [permissions, setPermissions] = useState<any>(false)
    const [edit_tripReport, setEdit_tripReport] = useState<any>(false)

    const clientModel = new ClientModel()
    const vesselModel = new VesselModel()
    const geoLocationModel = new GeoLocationModel()
    const eventTypeModel = new EventTypeModel()
    const tripReportModel = new TripReport_LogBookEntrySectionModel()
    const [openTripSelectionDialog, setOpenTripSelectionDialog] =
        useState(false)
    const [tripReportSchedules, setTripReportSchedules] = useState<any>([])
    const [selectedTripReportSchedule, setSelectedTripReportSchedule] =
        useState<any>(null)
    const [tripScheduleServices, setTripScheduleServices] = useState<any>([])
    const [selectedTripScheduleServiceID, setSelectedTripScheduleServiceID] =
        useState<any>(null)
    const [showNextTrips, setShowNextTrips] = useState(false)
    const [isCreatingScheduledTrip, setIsCreatingScheduledTrip] =
        useState(false)
    const [showPastScheduledTrips, setShowPastScheduledTrips] = useState(false)

    // Responsive hooks
    const bp = useBreakpoints()
    const responsiveLabel = useResponsiveLabel('standard')

    const init_permissions = () => {
        if (permissions) {
            if (hasPermission('EDIT_LOGBOOKENTRY_TRIPREPORT', permissions)) {
                setEdit_tripReport(true)
            } else {
                setEdit_tripReport(false)
            }
        }
    }

    const offlineLoad = async () => {
        const locations = await geoLocationModel.getAll()
        setLocations(locations)
        const types = await eventTypeModel.getAll()
        setEventTypes(types)
    }
    useEffect(() => {
        setPermissions(getPermissions)
        loadTripScheduleServices()
        if (!locations) {
            if (offline) {
                offlineLoad()
            } else {
                loadLocations()
                loadEventTypes()
            }
        }
    }, [])

    useEffect(() => {
        init_permissions()
    }, [permissions])

    useEffect(() => {
        if (createdTab) {
            setSelectedTab(createdTab)
        }
    }, [createdTab])

    if (!offline) {
        getVesselByID(+vesselID, setVessel)
    }

    const scrollToAccordionItem = (tripId: number) => {
        const element = document.getElementById(`triplog-${tripId}`)
        if (element) {
            element.scrollIntoView({ behavior: 'smooth' })
        }
    }

    useEffect(() => {
        if (tripReport && currentTrip) {
            const trip = tripReport.find(
                (trip: any) => trip.id === currentTrip.id,
            )
            currentTripRef.current = trip
            setCurrentTrip(trip)
        }
        if (tripReport && bufferTripID > 0) {
            const trip = tripReport.find(
                (trip: any) => trip.id === bufferTripID,
            )
            if (trip) {
                currentTripRef.current = trip
                setCurrentTrip(trip)
                // Only expand accordion and scroll for regular trips, not scheduled trips
                if (!selectedTripReportSchedule) {
                    setAccordionValue(trip.id.toString())
                    scrollToAccordionItem(trip.id)
                }
                setOpenTripModal(true)
                setSelectedTab(trip.id)
                setSignatureKey(uniqueId())
                // Initialize signature data if available
                if (trip.sectionSignature) {
                    setSignature(trip.sectionSignature.signatureData || '')
                } else {
                    setSignature('')
                }
                // Initialize trip-specific state
                setRiskBufferEvDgr(trip?.dangerousGoodsChecklist)
                setOpenTripStartRiskAnalysis(false)
                setAllDangerousGoods(false)
                setCurrentStopEvent(false)
                setCurrentEventTypeEvent(false)
                setSelectedRowEvent(false)
                setDisplayDangerousGoods(trip?.enableDGR === true)
                setDisplayDangerousGoodsSailing(
                    trip?.designatedDangerousGoodsSailing === true,
                )
                setDisplayDangerousGoodsPvpd(false)
                setDisplayDangerousGoodsPvpdSailing(null)
                setAllPVPDDangerousGoods(false)
                setSelectedDGRPVPD(false)
                setTripReport_Stops(false)
            }
            setBufferTripID(0)
        }
    }, [tripReport])

    const [loadLocations] = useLazyQuery(GET_GEO_LOCATIONS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            setLocations(response.readGeoLocations.nodes)
        },
        onError: (error) => {
            console.error('Error loading locations', error)
        },
    })

    const [loadEventTypes] = useLazyQuery(GET_EVENT_TYPES, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            setEventTypes(response.readEventTypes.nodes)
        },
        onError: (error) => {
            console.error('Error loading activity types', error)
        },
    })

    const [createTripReport_Stop] = useMutation(CreateTripReport_Stop, {
        onCompleted: () => {},
        onError: (error) => {
            console.error('Error creating passenger drop facility', error)
        },
    })

    const handleCreateTripReportScheduleStops = async (
        logBookEntrySectionID: any,
    ) => {
        if (!isEmpty(selectedTripReportSchedule)) {
            const tripStops =
                selectedTripReportSchedule.tripReportScheduleStops.nodes || []
            await Promise.all(
                tripStops.map(async (stop: any) => {
                    const input = {
                        logBookEntrySectionID: logBookEntrySectionID,
                        tripReportScheduleStopID: stop.id,
                        arriveTime: stop.arriveTime,
                        departTime: stop.departTime,
                        stopLocationID: stop.stopLocationID,
                    }
                    await createTripReport_Stop({
                        variables: { input: input },
                    })
                }),
            )
            setSelectedTripReportSchedule(null)
            if (tripReport) {
                updateTripReport({
                    id: [
                        ...tripReport.map((trip: any) => trip.id),
                        logBookEntrySectionID,
                    ],
                })
            } else {
                updateTripReport({
                    id: [logBookEntrySectionID],
                })
            }
        }
    }
    const [createTripReport_LogBookEntrySection] = useMutation(
        CreateTripReport_LogBookEntrySection,
        {
            onCompleted: (response) => {
                const data = response.createTripReport_LogBookEntrySection
                if (tripReport) {
                    updateTripReport({
                        id: [
                            ...tripReport.map((trip: any) => trip.id),
                            data.id,
                        ],
                    })
                } else {
                    updateTripReport({
                        id: [data.id],
                    })
                }
                setCreatedTab(data.id)
                setBufferTripID(data.id)
                handleCreateTripReportScheduleStops(data.id)
                // Always set accordion value and scroll for newly created trips
                setAccordionValue(data.id.toString())
                scrollToAccordionItem(data.id)
                // Reset the flag
                setIsCreatingScheduledTrip(false)
            },
            onError: (error) => {
                console.error('Error creating trip report', error)
            },
        },
    )
    const [updateTripReport_LogBookEntrySection] = useMutation(
        UpdateTripReport_LogBookEntrySection,
        {
            onCompleted: (response) => {
                const data = response.updateTripReport_LogBookEntrySection
                if (tripReport) {
                    updateTripReport({
                        id: [
                            ...tripReport.map((trip: any) => trip.id),
                            data.id,
                        ],
                        currentTripID: currentTrip.id,
                        key: 'comment',
                        value: comment,
                    })
                } else {
                    updateTripReport({
                        id: [data.id],
                        currentTripID: currentTrip.id,
                        key: 'comment',
                        value: comment,
                    })
                }
                setBufferTripID(data.id)
                setCurrentTrip(false)
            },
            onError: (error) => {
                console.error('Error creating trip report', error)
            },
        },
    )

    const [
        readTripReportSchedules,
        { loading: readTripReportSchedulesLoading },
    ] = useLazyQuery(ReadTripReportSchedules, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const data = response.readTripReportSchedules.nodes.filter(
                (trip: any) =>
                    // only show trips for the current vessel
                    trip.vehicles.nodes.some(
                        (vehicle: any) => +vehicle.id === +vesselID,
                    ),
                // &&
                // only show trips with no log book entry sections
                // trip.tripReport_LogBookEntrySections.nodes.length === 0,
            )
            if (showNextTrips) {
                // only show 1 past trip and 4 upcoming trips
                const currentTime = dayjs().format('HH:mm:ss')
                const pastIndex = data.findIndex(
                    (trip: any) => trip.departTime >= currentTime,
                )
                const result = (
                    pastIndex > 0 ? [data[pastIndex - 1]] : []
                ).concat(data.slice(pastIndex, pastIndex + 4))
                setTripReportSchedules(result)
            } else {
                setTripReportSchedules(data)
            }
        },
        onError: (error) => {
            console.error('Error loading TripReportSchedules', error)
        },
    })
    const [readTripScheduleServices] = useLazyQuery(ReadTripScheduleServices, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const data = response.readTripScheduleServices.nodes.map(
                (tss: any) => {
                    return {
                        label: tss.title,
                        value: tss.id,
                    }
                },
            )
            setTripScheduleServices(data)
            setTripReportSchedules([])
            // setOpenTripSelectionDialog(true)
        },
        onError: (error) => {
            console.error('Error loading TripScheduleServices', error)
        },
    })
    const loadTripScheduleServices = async () => {
        await readTripScheduleServices({
            variables: {
                filter: {
                    vehicles: { id: { eq: vesselID } },
                },
            },
        })
    }
    const loadTripReportSchedules = async (tripScheduleServiceID: any) => {
        setTripReportSchedules([])
        await readTripReportSchedules({
            variables: {
                filter: {
                    // archived: { eq: false },
                    // start: { eq: logBookStartDate },
                    tripScheduleServiceID: { eq: tripScheduleServiceID },
                },
            },
        })
    }
    const doCreateTripReport = async (
        input: any,
        isScheduledTrip: boolean = false,
    ) => {
        if (!edit_tripReport) {
            toast.error('You do not have permission to add a trip')
            return
        }
        setOpenTripModal(false)
        setCurrentTrip(false)
        setIsCreatingScheduledTrip(isScheduledTrip)
        if (offline) {
            const data = await tripReportModel.save({
                ...input,
                id: generateUniqueId(),
            })
            if (tripReport) {
                updateTripReport({
                    id: [...tripReport.map((trip: any) => trip.id), data.id],
                })
            } else {
                updateTripReport({
                    id: [data.id],
                })
            }
            setCreatedTab(data.id)
            setBufferTripID(data.id)
            // For offline mode, immediately set accordion value since the trip is created synchronously
            setAccordionValue(data.id.toString())
            // Reset the flag
            setIsCreatingScheduledTrip(false)
        } else {
            createTripReport_LogBookEntrySection({
                variables: {
                    input: input,
                },
            })
        }
        setRiskBufferEvDgr(false)
        setOpenTripStartRiskAnalysis(false)
        setAllDangerousGoods(false)
        setCurrentStopEvent(false)
        setCurrentEventTypeEvent(false)
        setSelectedRowEvent(false)
        setDisplayDangerousGoods(false)
        setDisplayDangerousGoodsSailing(false)
        setDisplayDangerousGoodsPvpd(false)
        setDisplayDangerousGoodsPvpdSailing(null)
        setAllPVPDDangerousGoods(false)
        setSelectedDGRPVPD(false)
        setTripReport_Stops(false)
    }

    const handleCustomTrip = () => {
        setOpenTripSelectionDialog(false)
        const input = {
            logBookEntryID: logentryID,
        }
        doCreateTripReport(input, false) // false indicates this is a regular trip
    }
    // Removed unused handleEditTrip function

    const [createLogBookEntrySection_Signature] = useMutation(
        CreateLogBookEntrySection_Signature,
        {
            onCompleted: (response) => {
                const data = response.createLogBookEntrySection_Signature
                updateTripReport_LogBookEntrySection({
                    variables: {
                        input: {
                            id: +data.logBookEntrySectionID,
                            sectionSignatureID: +data?.id,
                        },
                    },
                })
            },
            onError: (error) => {
                console.error('Error saving signature', error)
            },
        },
    )
    const [updateLogBookEntrySection_Signature] = useMutation(
        UpdateLogBookEntrySection_Signature,
        {
            onCompleted: (response) => {
                const data = response.updateLogBookEntrySection_Signature
                updateTripReport_LogBookEntrySection({
                    variables: {
                        input: {
                            id: currentTripRef.current?.id,
                            sectionSignatureID: +data?.id,
                        },
                    },
                })
            },
            onError: (error) => {
                console.error(
                    '341 TripLog updateLogBookEntrySection_Signature',
                    error,
                )
            },
        },
    )
    const handleSave = async () => {
        // Log signature information for debugging

        // Ensure we have a valid signature

        const sigVariables = {
            logBookEntrySectionID: currentTrip.id,
            memberID: localStorage.getItem('userId'),
            signatureData: signature || '',
        }
        if (+currentTrip.sectionSignatureID > 0) {
            // Update signature
            updateLogBookEntrySection_Signature({
                variables: {
                    input: {
                        ...sigVariables,
                        id: +currentTrip.sectionSignatureID,
                    },
                },
            })
        } else {
            // Create signature
            createLogBookEntrySection_Signature({
                variables: {
                    input: sigVariables ?? '',
                },
            })
        }
        if (offline) {
            // updateTripReport_LogBookEntrySection
            const data = await tripReportModel.save({
                id: currentTrip.id,
                comment: comment || null,
            })
            if (tripReport) {
                updateTripReport({
                    id: [...tripReport.map((trip: any) => trip.id), data.id],
                    currentTripID: currentTrip.id,
                    key: 'comment',
                    value: comment,
                })
            } else {
                updateTripReport({
                    id: [data.id],
                    currentTripID: currentTrip.id,
                    key: 'comment',
                    value: comment,
                })
            }
            setBufferTripID(data.id)
        } else {
            await updateTripReport_LogBookEntrySection({
                variables: {
                    input: {
                        id: currentTripRef.current?.id,
                        comment: comment || null,
                    },
                },
            })
            setOpenTripModal(false)
            setCurrentTrip(false)
        }
    }

    const displayFieldTripLog = (fieldName: string) => {
        const dailyChecks =
            logBookConfig?.customisedLogBookComponents?.nodes?.filter(
                (node: any) =>
                    node.componentClass === 'TripReport_LogBookComponent',
            )
        if (
            dailyChecks?.length > 0 &&
            dailyChecks[0]?.customisedComponentFields?.nodes.filter(
                (field: any) =>
                    field.fieldName === fieldName && field.status !== 'Off',
            ).length > 0
        ) {
            return true
        }
        return false
    }

    const convertTimeFormat = (time: string) => {
        if (time === null || time === undefined) return ''
        const [hours, minutes] = time.split(':')
        return `${hours}:${minutes}`
    }

    const handleCancel = () => {
        setOpenTripModal(false)
        setCurrentTrip(false)
        setAccordionValue('')
    }

    // Removed unused functions

    const initOffline = async () => {
        // getOneClient
        const client = await clientModel.getById(
            localStorage.getItem('clientId') ?? 0,
        )
        setClient(client)
        // getVesselByID(+vesselID, setVessel)
        const vessel = await vesselModel.getById(vesselID)
        setVessel(vessel)
    }
    useEffect(() => {
        if (offline) {
            initOffline()
        }
    }, [offline])

    useEffect(() => {
        if (!isEmpty(trim(tabTSS))) {
            loadTripReportSchedules(tabTSS)
        }
    }, [tabTSS])

    type TripReportSchedule = {
        id: string
        departTime: string
        arriveTime: string
        fromLocationID: string
        toLocationID: string
        start: string
        [key: string]: any
    }

    /**
     * Takes an array of trip report schedules and returns an array of the latest unique schedules based on:
     * - departTime
     * - arriveTime
     * - fromLocationID
     * - toLocationID
     * - start
     *
     * Schedules with the same combination of the above properties will be deduplicated, with the latest schedule (based on id) being kept.
     *
     * @param {TripReportSchedule[]} schedules - An array of trip report schedules
     * @returns {TripReportSchedule[]} - An array of the latest unique trip report schedules
     */
    const getLatestUniqueSchedules = (
        schedules: TripReportSchedule[],
    ): TripReportSchedule[] => {
        const map = new Map<string, TripReportSchedule>()

        for (const schedule of schedules) {
            const key = `${schedule.departTime}|${schedule.arriveTime}|${schedule.fromLocationID}|${schedule.toLocationID}|${schedule.start}`
            const existing = map.get(key)

            if (!existing || Number(schedule.id) > Number(existing.id)) {
                map.set(key, schedule)
            }
        }

        return Array.from(map.values())
    }

    // Create combined trip data that merges tripReport and tripReportSchedules
    const combinedTripData = useMemo(() => {
        const existingTrips = (tripReport || [])
            .filter((trip: any) => !trip?.archived)
            .map((trip: any) => ({
                ...trip,
                isScheduled: false,
                isCreated: true,
                sortTime: trip.departTime || '00:00:00',
                displayText: `<div>${
                    trip?.departTime
                        ? convertTimeFormat(trip?.departTime) + ' - '
                        : 'No depart time - '
                }${trip?.fromLocation?.title || ''}${
                    trip?.fromLocation?.title && trip?.toLocation?.title
                        ? ' -> '
                        : ''
                }<span class="${
                    isVesselArrivalLate(trip?.arriveTime, trip?.arrive)
                        ? 'text-cinnabar-500'
                        : ''
                }">${
                    trip?.arrive
                        ? convertTimeFormat(
                              dayjs(trip?.arrive).format('HH:mm ') + ' - ',
                          )
                        : trip?.arriveTime
                          ? convertTimeFormat(trip?.arriveTime) + ' - '
                          : ' No arrival time '
                }</span> ${trip?.toLocation?.title || ''}${
                    !trip?.fromLocation?.title && !trip?.toLocation?.title
                        ? ' '
                        : ' '
                }</div>`,
            }))

        const latestUniqueSchedules =
            getLatestUniqueSchedules(tripReportSchedules)

        // Get current time for filtering
        const currentTime = dayjs().format('HH:mm:ss')

        const scheduledTrips = (latestUniqueSchedules || []).map(
            (schedule: any) => {
                // Check if this schedule has already been created as a trip
                const isAlreadyCreated = existingTrips.some(
                    (trip: any) => trip.tripReportScheduleID === schedule.id,
                )

                return {
                    ...schedule,
                    isScheduled: true,
                    isCreated: isAlreadyCreated,
                    sortTime: schedule.departTime || '00:00:00',
                    displayText: `${schedule.departTime} - ${schedule.arriveTime} | ${schedule.fromLocation.title} → ${schedule.toLocation.title}`,
                }
            },
        )

        // Filter scheduled trips based on time and showPastScheduledTrips state
        const filteredScheduledTrips = scheduledTrips.filter((s: any) => {
            if (s.isCreated) return false // Don't show already created trips

            const tripTime = s.sortTime || '00:00:00'
            const isUpcoming = tripTime >= currentTime

            // Show upcoming trips by default, past trips only when showPastScheduledTrips is true
            return showPastScheduledTrips ? !isUpcoming : isUpcoming
        })

        // Combine and sort by departure time
        const combined = [...existingTrips, ...filteredScheduledTrips]
        return combined.sort((a, b) => {
            const timeA = a.sortTime || '00:00:00'
            const timeB = b.sortTime || '00:00:00'
            return timeA.localeCompare(timeB)
        })
    }, [tripReport, tripReportSchedules, showPastScheduledTrips])

    // Check if there are past scheduled trips available
    const hasPastScheduledTrips = useMemo(() => {
        const currentTime = dayjs().format('HH:mm:ss')
        const latestUniqueSchedules =
            getLatestUniqueSchedules(tripReportSchedules)
        const existingTrips = tripReport || []

        return latestUniqueSchedules.some((schedule: any) => {
            const isAlreadyCreated = existingTrips.some(
                (trip: any) => trip.tripReportScheduleID === schedule.id,
            )
            const tripTime = schedule.departTime || '00:00:00'
            const isPast = tripTime < currentTime

            return !isAlreadyCreated && isPast
        })
    }, [tripReport, tripReportSchedules])

    // Handle creating a trip from a scheduled item
    const handleCreateFromSchedule = (scheduleItem: any) => {
        setSelectedTripReportSchedule(scheduleItem)
        const input = {
            tripReportScheduleID: scheduleItem.id,
            departTime: scheduleItem.departTime,
            arriveTime: scheduleItem.arriveTime,
            fromLocationID: scheduleItem.fromLocationID,
            toLocationID: scheduleItem.toLocationID,
            logBookEntryID: logentryID,
        }
        doCreateTripReport(input, true) // true indicates this is a scheduled trip
    }

    // Implement confirmDeletetrip using  AlertNew component to confirm trip deletion
    const confirmDeleteTrip = (item: any) => {
        if (item) {
            setDeleteTripItem(item)
            setDeleteTripConfirmation(true)
        }
    }
    const [deleteTripReport_LogBookEntrySection] = useMutation(
        DeleteTripReport_LogBookEntrySections,
        {
            onCompleted: () => {
                // const data = response.deleteTripReport_LogBookEntrySection
                // updateTripReport({
                //     id: data,
                //     key: 'archived',
                //     value: true,
                // })
                updateTripReport({
                    id: [
                        ...tripReport
                            .filter(
                                (trip: any) => trip.id !== deleteTripItem.id,
                            )
                            .map((trip: any) => trip.id),
                    ],
                })

                setDeleteTripConfirmation(false)
                setDeleteTripItem(false)
            },
            onError: (error) => {
                console.error('Error deleting trip report', error)
                setDeleteTripConfirmation(false)
                setDeleteTripItem(false)
            },
        },
    )
    const handleDeleteTrip = async () => {
        if (deleteTripItem) {
            if (!offline) {
                await deleteTripReport_LogBookEntrySection({
                    variables: {
                        ids: [deleteTripItem.id],
                    },
                })
            } else {
                await tripReportModel.save({
                    id: deleteTripItem.id,
                    archived: true,
                })
            }
            setDeleteTripConfirmation(false)
            setDeleteTripItem(false)
        }
    }

    return (
        <>
            {/*!isWriteMode && ( */}
            <div>
                {allowedVesselTypes.includes(vessel.vesselType) && (
                    <div className="flex xs:flex-row flex-col xs:justify-between gap-2 xs:gap-4">
                        <Combobox
                            options={tripScheduleServices}
                            value={
                                tabTSS
                                    ? tripScheduleServices.find(
                                          (option: { value: any }) =>
                                              option.value === tabTSS,
                                      ) || null
                                    : tripScheduleServices.find(
                                          (option: { value: any }) =>
                                              option.value ===
                                              selectedTripScheduleServiceID,
                                      ) || null
                            }
                            onChange={(e: any) => {
                                setTabTSS(e?.value || '')
                                if (e) {
                                    setSelectedTripScheduleServiceID(e.value)
                                    loadTripReportSchedules(e.value)
                                } else {
                                    setSelectedTripScheduleServiceID(null)
                                    setTripReportSchedules([])
                                    setShowNextTrips(false)
                                }
                            }}
                            placeholder="Select Trip Schedule Service"
                            disabled={locked}
                        />
                        <Button
                            onClick={handleCustomTrip}
                            variant="primary"
                            disabled={locked}>
                            Add Non-Scheduled Trip
                        </Button>
                    </div>
                )}

                {/* {selectedTripScheduleServiceID && (
                    <CheckFieldLabel
                        id="show-next-trips"
                        type="checkbox"
                        checked={showNextTrips}
                        onCheckedChange={(checked) => {
                            setShowNextTrips(checked)
                            loadTripReportSchedules(
                                selectedTripScheduleServiceID,
                            )
                        }}
                        label="Show next trips"
                        className="mt-4"
                    />
                )} */}

                {/* {tripReportSchedules.length > 0 && (
                    <div>
                        <Button className="my-4">Load Earlier</Button>
                        <Button>Load Later</Button>
                    </div>
                )} */}
            </div>
            <div className="mt-4">
                This section covers the logbook entry. This can be made up of a
                single trip or many over the course of the voyage.
            </div>

            <div className="mt-4">
                {combinedTripData.length > 0 && (
                    <>
                        {/* Load More button for past scheduled trips */}
                        {hasPastScheduledTrips && !showPastScheduledTrips && (
                            <div className="mb-4">
                                <Button
                                    onClick={() =>
                                        setShowPastScheduledTrips(true)
                                    }
                                    variant="primary">
                                    Show Past Trips
                                </Button>
                            </div>
                        )}

                        {/* Show Less button when past trips are displayed */}
                        {showPastScheduledTrips && (
                            <div className="mb-4">
                                <Button
                                    onClick={() =>
                                        setShowPastScheduledTrips(false)
                                    }
                                    variant="primary">
                                    Hide Past Trips
                                </Button>
                            </div>
                        )}

                        <Accordion
                            type="single"
                            collapsible
                            value={accordionValue}
                            onValueChange={(value) => {
                                setAccordionValue(value)

                                // If we're closing the accordion, reset the state
                                if (value === '') {
                                    setSelectedTab(0)
                                    setOpenTripModal(false)
                                    setCurrentTrip(false)
                                    setSelectedTripReportSchedule(null)
                                } else {
                                    // Find the selected item from combined data
                                    const selectedItem = combinedTripData.find(
                                        (item: any) =>
                                            item.id.toString() === value,
                                    )

                                    if (selectedItem) {
                                        if (
                                            selectedItem.isScheduled &&
                                            !selectedItem.isCreated
                                        ) {
                                            // This is a scheduled trip that hasn't been created yet
                                            // Don't set currentTrip, just expand the accordion
                                            setSelectedTab(0)
                                            setOpenTripModal(false)
                                            setCurrentTrip(false)
                                            setSelectedTripReportSchedule(null)
                                        } else {
                                            // This is an existing trip or a created scheduled trip
                                            setSelectedTab(selectedItem.id)
                                            setOpenTripModal(true)
                                            setSignatureKey(uniqueId())
                                            currentTripRef.current =
                                                selectedItem
                                            setCurrentTrip(selectedItem)
                                            setSelectedTripReportSchedule(null)

                                            // Initialize signature data if available
                                            if (selectedItem.sectionSignature) {
                                                setSignature(
                                                    selectedItem
                                                        .sectionSignature
                                                        .signatureData || '',
                                                )
                                            } else {
                                                setSignature('')
                                            }
                                            setRiskBufferEvDgr(
                                                selectedItem?.dangerousGoodsChecklist,
                                            )
                                            setOpenTripStartRiskAnalysis(false)
                                            setAllDangerousGoods(false)
                                            setCurrentStopEvent(false)
                                            setCurrentEventTypeEvent(false)
                                            setSelectedRowEvent(false)
                                            setDisplayDangerousGoods(
                                                selectedItem?.enableDGR ===
                                                    true,
                                            )
                                            setDisplayDangerousGoodsSailing(
                                                selectedItem?.designatedDangerousGoodsSailing ===
                                                    true,
                                            )
                                            setDisplayDangerousGoodsPvpd(false)
                                            setDisplayDangerousGoodsPvpdSailing(
                                                null,
                                            )
                                            setAllPVPDDangerousGoods(false)
                                            setSelectedDGRPVPD(false)
                                            setTripReport_Stops(false)
                                        }
                                    }
                                }
                            }}>
                            {combinedTripData.map((item: any) => (
                                <AccordionItem
                                    key={`combined-trip-${item.id}-${item.isScheduled ? 'scheduled' : 'existing'}`}
                                    value={item.id.toString()}
                                    id={`combined-trip-${item.id}`}>
                                    <AccordionTrigger>
                                        <div className="flex flex-col xs:flex-row xs:items-center flex-wrap justify-between w-full gap-2 pe-2 sm:pe-4 py-1">
                                            <div className="flex w-fit items-center flex-wrap gap-2 sm:gap-3 flex-1">
                                                <div className="grid">
                                                    <div
                                                        className="text-sm sm:text-base text-nowrap truncate font-medium w-full leading-tight"
                                                        dangerouslySetInnerHTML={{
                                                            __html: item.displayText,
                                                        }}
                                                    />
                                                </div>

                                                {item.isScheduled &&
                                                    !item.isCreated && (
                                                        <div className="text-xs bg-blue-100 text-blue-800 px-1.5 py-0.5 rounded whitespace-nowrap leading-none">
                                                            Scheduled
                                                        </div>
                                                    )}
                                                {!item.isScheduled && (
                                                    <div className="flex items-center gap-1 xs:gap-2 sm:gap-3 text-xs text-gray-600 leading-none">
                                                        <span className="whitespace-nowrap">
                                                            {responsiveLabel(
                                                                'POB',
                                                                'People on Board',
                                                            )}
                                                            :{item.totalPOB}
                                                        </span>
                                                        <span className="whitespace-nowrap">
                                                            {responsiveLabel(
                                                                'Pax',
                                                                'Total Passengers Carried',
                                                            )}
                                                            :
                                                            {
                                                                item.totalPaxJoined
                                                            }
                                                        </span>
                                                        <span className="whitespace-nowrap">
                                                            {responsiveLabel(
                                                                'Veh',
                                                                'Total Vehicles Carried',
                                                            )}
                                                            :
                                                            {
                                                                item.totalVehiclesJoined
                                                            }
                                                        </span>
                                                    </div>
                                                )}
                                            </div>
                                            <div className="flex items-center gap-1 xs:gap-2 flex-shrink-0">
                                                {item.isScheduled &&
                                                    !item.isCreated && (
                                                        <Button
                                                            onClick={(e) => {
                                                                e.stopPropagation()
                                                                handleCreateFromSchedule(
                                                                    item,
                                                                )
                                                            }}
                                                            size="sm"
                                                            className="text-xs whitespace-nowrap h-7 px-2 py-1"
                                                            disabled={locked}>
                                                            {responsiveLabel(
                                                                'Create',
                                                                'Create Trip',
                                                            )}
                                                        </Button>
                                                    )}
                                                {!item.isScheduled &&
                                                    !locked && (
                                                        <>
                                                            <Button
                                                                onClick={(
                                                                    e,
                                                                ) => {
                                                                    e.stopPropagation()
                                                                    confirmDeleteTrip(
                                                                        item,
                                                                    )
                                                                }}
                                                                iconLeft={
                                                                    Trash2
                                                                }
                                                                size="sm"
                                                                className="text-xs whitespace-nowrap h-7 px-2 py-1"
                                                                variant="destructive"
                                                                disabled={
                                                                    locked
                                                                }>
                                                                {responsiveLabel(
                                                                    'Delete',
                                                                    'Delete Trip',
                                                                )}
                                                            </Button>
                                                            {/* <Button
                                                                onClick={(
                                                                    e,
                                                                ) => {
                                                                    e.stopPropagation()
                                                                    confirmDeleteTrip(
                                                                        item,
                                                                    )
                                                                }}
                                                                iconLeft={
                                                                    Trash2
                                                                }
                                                                size="sm"
                                                                iconOnly
                                                                className="ml-2 xs:hidden absolute h-fit right-0 top-0 p-1 rounded-none rounded-tr-md rounded-bl-md aspect-square"
                                                                variant="destructive"
                                                                disabled={
                                                                    locked
                                                                }
                                                            /> */}
                                                        </>
                                                    )}
                                            </div>
                                        </div>
                                    </AccordionTrigger>
                                    <AccordionContent className="px-5 sm:px-10">
                                        {/* Only render content for existing trips or created scheduled trips */}
                                        {!item.isScheduled || item.isCreated ? (
                                            currentTrip &&
                                            currentTrip.id === item.id && (
                                                <TripReportAccordionContent
                                                    currentTrip={currentTrip}
                                                    offline={offline}
                                                    tripReport={tripReport}
                                                    updateTripReport={
                                                        updateTripReport
                                                    }
                                                    updateTripReport_LogBookEntrySection={
                                                        updateTripReport_LogBookEntrySection
                                                    }
                                                    currentTripRef={
                                                        currentTripRef
                                                    }
                                                    locations={locations}
                                                    locked={locked}
                                                    edit_tripReport={
                                                        edit_tripReport
                                                    }
                                                    vessel={vessel}
                                                    crewMembers={crewMembers}
                                                    logBookConfig={
                                                        logBookConfig
                                                    }
                                                    client={client}
                                                    canCarryVehicles={
                                                        canCarryVehicles
                                                    }
                                                    canCarryDangerousGoods={
                                                        canCarryDangerousGoods
                                                    }
                                                    selectedDGR={selectedDGR}
                                                    displayDangerousGoods={
                                                        displayDangerousGoods
                                                    }
                                                    setDisplayDangerousGoods={
                                                        setDisplayDangerousGoods
                                                    }
                                                    displayDangerousGoodsSailing={
                                                        displayDangerousGoodsSailing
                                                    }
                                                    setDisplayDangerousGoodsSailing={
                                                        setDisplayDangerousGoodsSailing
                                                    }
                                                    allDangerousGoods={
                                                        allDangerousGoods
                                                    }
                                                    setAllDangerousGoods={
                                                        setAllDangerousGoods
                                                    }
                                                    logBookStartDate={
                                                        logBookStartDate
                                                    }
                                                    masterID={masterID}
                                                    vessels={vessels}
                                                    setSelectedRowEvent={
                                                        setSelectedRowEvent
                                                    }
                                                    setCurrentEventTypeEvent={
                                                        setCurrentEventTypeEvent
                                                    }
                                                    setCurrentStopEvent={
                                                        setCurrentStopEvent
                                                    }
                                                    currentEventTypeEvent={
                                                        currentEventTypeEvent
                                                    }
                                                    currentStopEvent={
                                                        currentStopEvent
                                                    }
                                                    tripReport_Stops={
                                                        tripReport_Stops
                                                    }
                                                    setTripReport_Stops={
                                                        setTripReport_Stops
                                                    }
                                                    displayDangerousGoodsPvpd={
                                                        displayDangerousGoodsPvpd
                                                    }
                                                    setDisplayDangerousGoodsPvpd={
                                                        setDisplayDangerousGoodsPvpd
                                                    }
                                                    displayDangerousGoodsPvpdSailing={
                                                        displayDangerousGoodsPvpdSailing
                                                    }
                                                    setDisplayDangerousGoodsPvpdSailing={
                                                        setDisplayDangerousGoodsPvpdSailing
                                                    }
                                                    allPVPDDangerousGoods={
                                                        allPVPDDangerousGoods
                                                    }
                                                    setAllPVPDDangerousGoods={
                                                        setAllPVPDDangerousGoods
                                                    }
                                                    selectedDGRPVPD={
                                                        selectedDGRPVPD
                                                    }
                                                    setSelectedDGRPVPD={
                                                        setSelectedDGRPVPD
                                                    }
                                                    fuelLogs={fuelLogs}
                                                    comment={comment}
                                                    setComment={setComment}
                                                    displayFieldTripLog={
                                                        displayFieldTripLog
                                                    }
                                                    signatureKey={signatureKey}
                                                    signature={signature}
                                                    setSignature={setSignature}
                                                    handleCancel={handleCancel}
                                                    handleSave={handleSave}
                                                />
                                            )
                                        ) : (
                                            <div className="text-center py-8 text-muted-foreground">
                                                <p>
                                                    Click the "Create Trip"
                                                    button above to create this
                                                    scheduled trip and access
                                                    the trip details.
                                                </p>
                                            </div>
                                        )}
                                    </AccordionContent>
                                </AccordionItem>
                            ))}
                        </Accordion>
                    </>
                )}
            </div>

            {!allowedVesselTypes.includes(vessel.vesselType) && (
                <div className="mt-4">
                    <Button
                        onClick={handleCustomTrip}
                        variant="primary"
                        disabled={locked}>
                        Add Trip
                    </Button>
                </div>
            )}

            {/* Implement confirmDeletetrip using  AlertNew component to confirm trip deletion */}
            <AlertDialogNew
                openDialog={deleteTripConfirmation}
                setOpenDialog={setDeleteTripConfirmation}
                // handleCreate={handleDeleteTrip}
                title="Delete Trip"
                description="Are you sure you want to delete this trip? This action cannot be undone."
                cancelText="Cancel"
                destructiveActionText="Delete"
                handleDestructiveAction={handleDeleteTrip}
                showDestructiveAction={true}
                variant="danger"
                actionText="Delete"
            />
        </>
    )
}
