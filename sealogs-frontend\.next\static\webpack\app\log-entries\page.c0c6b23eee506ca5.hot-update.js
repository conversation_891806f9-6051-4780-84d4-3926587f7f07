"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew/crew.tsx":
/*!**********************************!*\
  !*** ./src/app/ui/crew/crew.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Crew; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _daily_checks_crew_welfare__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../daily-checks/crew-welfare */ \"(app-pages-browser)/./src/app/ui/daily-checks/crew-welfare.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/helpers/dateHelper */ \"(app-pages-browser)/./src/app/helpers/dateHelper.ts\");\n/* harmony import */ var _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/seaLogsMember */ \"(app-pages-browser)/./src/app/offline/models/seaLogsMember.js\");\n/* harmony import */ var _app_offline_models_crewDuty__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/crewDuty */ \"(app-pages-browser)/./src/app/offline/models/crewDuty.js\");\n/* harmony import */ var _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/crewMembers_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/crewMembers_LogBookEntrySection.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _app_offline_models_crewWelfare_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/app/offline/models/crewWelfare_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/crewWelfare_LogBookEntrySection.js\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _vessels_actions__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../vessels/actions */ \"(app-pages-browser)/./src/app/ui/vessels/actions.tsx\");\n/* harmony import */ var _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/app/lib/logbook-configuration */ \"(app-pages-browser)/./src/app/lib/logbook-configuration/index.ts\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_19___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_19__);\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_filter_components_crew_duty_dropdown__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/filter/components/crew-duty-dropdown */ \"(app-pages-browser)/./src/components/filter/components/crew-duty-dropdown.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_InfoIcon_lucide_react__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,InfoIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_InfoIcon_lucide_react__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,InfoIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _components_DateRange__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/DateRange */ \"(app-pages-browser)/./src/components/DateRange.tsx\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! ./types */ \"(app-pages-browser)/./src/app/ui/crew/types.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! ../../../../utils/responsiveLabel */ \"(app-pages-browser)/./utils/responsiveLabel.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Import types from separate file\n\n\n\n\n\n\nfunction Crew(param) {\n    let { crewSections = false, allCrew, logBookEntryID, locked, logBookConfig = false, setCrewMembers, crewWelfareCheck, updateCrewWelfare, vessel = false, masterID = 0, logEntrySections, offline = false, crewMembersList } = param;\n    var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents, _crewMember_data_trainingStatus, _crewMember_data, _crewMember_profile, _crewMember_profile1, _crewMember_profile2, _crewMember_profile3, _crewMember_data1, _crewMember_profile4, _crewMember_data2, _crewMember_profile5, _crewMember_data3, _crewMember_profile6, _crewMember_data4, _crewMember_profile7, _crewMember_data5, _crewMember_profile8, _crewMember_data6, _crewMember_data_trainingStatus1, _crewMember_data7, _crewMember_data_trainingStatus2, _crewMember_data8, _crewMember_data9, _crewMember_data10, _crewMember_data_trainingStatus_dues, _crewMember_data_trainingStatus3, _crewMember_data11, _crewMember_data12, _crewMember_data13;\n    _s();\n    const seaLogsMemberModel = new _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\n    const crewDutyModel = new _app_offline_models_crewDuty__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\n    const lbCrewModel = new _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n    const lbWelfareModel = new _app_offline_models_crewWelfare_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_15__[\"default\"]();\n    const [allVesselCrews, setAllVesselCrews] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allDuties, setAllDuties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams)();\n    var _searchParams_get;\n    const vesselID = (_searchParams_get = searchParams.get(\"vesselID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loaded, setLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [crewMember, setCrewMember] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [duty, setDuty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loginTime, setLoginTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    // Store logoutTime as a standard Date object or null\n    const [logoutTime, setLogoutTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [duties, setDuties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [crew, setCrew] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(crewSections);\n    const [crewConfig, setCrewConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Field labels and status\n    const [punchInStatus, setPunchInStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [punchInLabel, setPunchInLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Sign In\");\n    const [punchOutStatus, setPunchOutStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [punchOutLabel, setPunchOutLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Sign Out\");\n    const [workDetailsStatus, setWorkDetailsStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [workDetailsLabel, setWorkDetailsLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Work Details\");\n    // const [editCrew, setEditCrew] = useState(false);\n    // const [editCrewMember, setEditCrewMember] = useState(null);\n    const [crewManifestEntry, setCrewManifestEntry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [openAddCrewMemberDialog, setopenAddCrewMemberDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Function removed as we're now directly using handleSave\n    const [openEditLogoutTimeDialog, setOpenEditLogoutTimeDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [crewMemberOptions, setCrewMemberOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [openCrewTrainingDueDialog, setOpenCrewTrainingDueDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openConfirmCrewDeleteDialog, setOpenConfirmCrewDeleteDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_logBookEntry, setEdit_logBookEntry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allMembers, setAllMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_34__.useBreakpoints)();\n    const init_permissions = ()=>{\n        if (permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_16__.hasPermission)(process.env.EDIT_LOGBOOKENTRY || \"EDIT_LOGBOOKENTRY\", permissions)) {\n            setEdit_logBookEntry(true);\n        } else {\n            setEdit_logBookEntry(false);\n        }\n    };\n    const createOfflineCrewWelfareCheck = async ()=>{\n        // I need to add a 2-second delay to fix ConstraintError: Key already exists in the object store.\n        const delay = (ms)=>new Promise((resolve)=>setTimeout(resolve, ms));\n        await delay(2000);\n        const id = (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)();\n        const data = await lbWelfareModel.save({\n            id: id,\n            logBookEntryID: logBookEntryID,\n            fitness: null,\n            imSafe: null,\n            safetyActions: null,\n            waterQuality: null,\n            __typename: \"CrewWelfare_LogBookEntrySection\"\n        });\n        updateCrewWelfare(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_16__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (logEntrySections && Array.isArray(logEntrySections)) {\n            const hasCrewWelfare = logEntrySections.filter((section)=>section && section.className === \"SeaLogs\\\\CrewWelfare_LogBookEntrySection\").length;\n            if (hasCrewWelfare === 0 && !crewWelfareCheck && !loaded && !createCrewWelfareCheckLoading) {\n                setLoaded(true);\n                if (offline) {\n                    createOfflineCrewWelfareCheck();\n                } else {\n                    createCrewWelfareCheck({\n                        variables: {\n                            input: {\n                                logBookEntryID: +logBookEntryID\n                            }\n                        }\n                    });\n                }\n            }\n        }\n    }, [\n        logEntrySections\n    ]);\n    const [queryCrewDetail] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.CREW_DETAIL_WITH_TRAINING_STATUS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"GetCrewDetailError\", error);\n        }\n    });\n    const [queryVesselCrews] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.CREW_LIST_WITHOUT_TRAINING_STATUS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers;\n            if (data) {\n                const allMembers = data.nodes.filter((item)=>{\n                    return +item.id !== +masterID;\n                }).map((member)=>{\n                    // const crewWithTraining = GetCrewListWithTrainingStatus(\n                    //     [member],\n                    //     [vessel],\n                    // )[0]\n                    return {\n                        label: \"\".concat(member.firstName || \"\", \" \").concat(member.surname || \"\").trim(),\n                        value: member.id,\n                        // data: crewWithTraining,\n                        profile: {\n                            firstName: member.firstName,\n                            surname: member.surname,\n                            avatar: member.profileImage\n                        }\n                    };\n                });\n                setAllMembers(allMembers);\n                const members = allMembers.filter((member)=>{\n                    if (!crewSections) {\n                        return true;\n                    }\n                    return !Array.isArray(crewSections) || !crewSections.some((section)=>section && section.crewMember && section.crewMember.id === member.value && section.punchOut === null);\n                });\n                const memberOptions = members.filter((member)=>!crewMembersList || !Array.isArray(crewMembersList) || !crewMembersList.includes(+member.value));\n                setCrewMemberOptions(memberOptions);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVesselCrews error\", error);\n        }\n    });\n    const loadVesselCrews = async ()=>{\n        if (offline) {\n            const data = await seaLogsMemberModel.getByVesselId(vesselID);\n            setAllVesselCrews(data);\n            if (data) {\n                const members = data.filter((item)=>{\n                    return +item.id !== +masterID;\n                }).map((member)=>{\n                    const crewWithTraining = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n                        member\n                    ], [\n                        vessel\n                    ])[0];\n                    return {\n                        label: \"\".concat(member.firstName || \"\", \" \").concat(member.surname || \"\").trim(),\n                        value: member.id,\n                        data: crewWithTraining,\n                        profile: {\n                            firstName: member.firstName,\n                            surname: member.surname,\n                            avatar: member.profileImage\n                        }\n                    };\n                }) // filter out members who are already in the crew list\n                .filter((member)=>{\n                    if (!crewSections) {\n                        return true;\n                    }\n                    return !Array.isArray(crewSections) || !crewSections.some((section)=>section && section.crewMember && section.crewMember.id === member.value && section.punchOut === null);\n                });\n                setCrewMemberOptions(members);\n            }\n        } else {\n            await queryVesselCrews({\n                variables: {\n                    filter: {\n                        vehicles: {\n                            id: {\n                                eq: vesselID\n                            }\n                        },\n                        isArchived: {\n                            eq: false\n                        }\n                    }\n                }\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadDuties();\n            // handleSetCrewConfig()\n            loadVesselCrews();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    // Group crew duties by crew member\n    const groupCrewDutiesByMember = (crewData)=>{\n        if (!crewData || !Array.isArray(crewData) || crewData.length === 0) return [];\n        const groupedCrew = {};\n        // First, sort the crew data by punchIn time to ensure consistent ordering\n        const sortedCrewData = [\n            ...crewData\n        ].sort((a, b)=>{\n            if (!a || !b) return 0;\n            const timeA = a.punchIn ? new Date(a.punchIn).getTime() : 0;\n            const timeB = b.punchIn ? new Date(b.punchIn).getTime() : 0;\n            return timeA - timeB // Ascending order (oldest first)\n            ;\n        });\n        // Filter out archived members first\n        const activeCrewData = sortedCrewData.filter((member)=>member && !member.archived);\n        activeCrewData.forEach((member)=>{\n            if (!member) return;\n            const crewMemberId = member.crewMemberID;\n            if (!crewMemberId) return;\n            // If this member already has duties array, preserve it\n            if (member.duties && Array.isArray(member.duties) && member.duties.length > 0) {\n                if (!groupedCrew[crewMemberId]) {\n                    // Initialize with the existing duties\n                    groupedCrew[crewMemberId] = {\n                        ...member\n                    };\n                } else {\n                    // Merge duties from this member with existing duties\n                    const existingDuties = groupedCrew[crewMemberId].duties || [];\n                    member.duties.forEach((duty)=>{\n                        if (!duty) return;\n                        // Check if this duty is already in the list (avoid duplicates by ID)\n                        const isDuplicateById = existingDuties.some((existingDuty)=>existingDuty && existingDuty.id === duty.id);\n                        // Only add if it's not a duplicate\n                        if (!isDuplicateById) {\n                            existingDuties.push(duty);\n                        }\n                    });\n                    groupedCrew[crewMemberId] = {\n                        ...groupedCrew[crewMemberId],\n                        duties: existingDuties\n                    };\n                }\n            } else {\n                // Handle members without a duties array\n                if (!groupedCrew[crewMemberId]) {\n                    // Initialize with the first duty\n                    groupedCrew[crewMemberId] = {\n                        ...member,\n                        duties: [\n                            {\n                                id: member.id,\n                                dutyPerformed: member.dutyPerformed,\n                                punchIn: member.punchIn,\n                                punchOut: member.punchOut,\n                                workDetails: member.workDetails,\n                                dutyPerformedID: member.dutyPerformedID,\n                                logBookEntryID: member.logBookEntryID\n                            }\n                        ]\n                    };\n                } else if (groupedCrew[crewMemberId].duties && Array.isArray(groupedCrew[crewMemberId].duties)) {\n                    // Check if this duty is already in the list (avoid duplicates by ID)\n                    const isDuplicateById = groupedCrew[crewMemberId].duties.some((existingDuty)=>existingDuty && existingDuty.id === member.id);\n                    // Also check if this is a duplicate duty type with the same time (which would be redundant)\n                    const isDuplicateDutyType = groupedCrew[crewMemberId].duties.some((existingDuty)=>existingDuty && existingDuty.dutyPerformedID === member.dutyPerformedID && existingDuty.punchIn === member.punchIn);\n                    // Only add if it's not a duplicate by ID or duty type\n                    if (!isDuplicateById && !isDuplicateDutyType) {\n                        groupedCrew[crewMemberId].duties.push({\n                            id: member.id,\n                            dutyPerformed: member.dutyPerformed,\n                            punchIn: member.punchIn,\n                            punchOut: member.punchOut,\n                            workDetails: member.workDetails,\n                            dutyPerformedID: member.dutyPerformedID,\n                            logBookEntryID: member.logBookEntryID\n                        });\n                    }\n                }\n            }\n        });\n        // Sort duties by punchIn time in ascending order for each crew member\n        Object.values(groupedCrew).forEach((crewMember)=>{\n            if (crewMember && crewMember.duties && Array.isArray(crewMember.duties) && crewMember.duties.length > 1) {\n                crewMember.duties.sort((a, b)=>{\n                    if (!a || !b) return 0;\n                    const timeA = a.punchIn ? new Date(a.punchIn).getTime() : 0;\n                    const timeB = b.punchIn ? new Date(b.punchIn).getTime() : 0;\n                    return timeA - timeB // Ascending order (oldest first)\n                    ;\n                });\n            }\n        });\n        return Object.values(groupedCrew);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (crewSections && Array.isArray(crewSections)) {\n            // Process each crew member's training status\n            const processedCrewSections = crewSections.map((section)=>{\n                if (section && section.crewMember) {\n                    // Apply GetCrewListWithTrainingStatus to the crewMember property\n                    const crewMemberWithTrainingStatus = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n                        section.crewMember\n                    ], [\n                        vessel\n                    ])[0];\n                    return {\n                        ...section,\n                        crewMember: crewMemberWithTrainingStatus\n                    };\n                }\n                return section;\n            });\n            // Preserve existing duties if they exist\n            let updatedData = processedCrewSections;\n            if (crew && Array.isArray(crew) && crew.length > 0) {\n                // Create a map of existing crew members with their duties\n                const existingCrewMap = crew.reduce((map, member)=>{\n                    if (member && member.crewMemberID) {\n                        map[member.crewMemberID] = member;\n                    }\n                    return map;\n                }, {});\n                // Check if any existing crew members have duties that need to be preserved\n                const hasExistingDuties = Object.values(existingCrewMap).some((member)=>member && member.duties && Array.isArray(member.duties) && member.duties.length > 0);\n                if (hasExistingDuties) {\n                    // Update processed data with existing duties where applicable\n                    updatedData = processedCrewSections.map((section)=>{\n                        if (!section || !section.crewMemberID) return section;\n                        const existingMember = existingCrewMap[section.crewMemberID];\n                        if (existingMember && existingMember.duties && Array.isArray(existingMember.duties) && existingMember.duties.length > 0) {\n                            // Check if this section's ID is already in the existing duties\n                            const dutyExists = existingMember.duties.some((duty)=>duty && duty.id === section.id);\n                            if (dutyExists) {\n                                // This section is already in the duties, so return the section with duties\n                                return {\n                                    ...section,\n                                    duties: existingMember.duties\n                                };\n                            } else {\n                                // This is a new duty for this crew member, add it to their duties\n                                const updatedDuties = [\n                                    ...existingMember.duties\n                                ];\n                                updatedDuties.push({\n                                    id: section.id,\n                                    dutyPerformed: section.dutyPerformed,\n                                    punchIn: section.punchIn,\n                                    punchOut: section.punchOut,\n                                    workDetails: section.workDetails,\n                                    dutyPerformedID: section.dutyPerformedID,\n                                    logBookEntryID: section.logBookEntryID\n                                });\n                                return {\n                                    ...section,\n                                    duties: updatedDuties\n                                };\n                            }\n                        }\n                        // No existing duties for this crew member, create a new duties array\n                        return {\n                            ...section,\n                            duties: [\n                                {\n                                    id: section.id,\n                                    dutyPerformed: section.dutyPerformed,\n                                    punchIn: section.punchIn,\n                                    punchOut: section.punchOut,\n                                    workDetails: section.workDetails,\n                                    dutyPerformedID: section.dutyPerformedID,\n                                    logBookEntryID: section.logBookEntryID\n                                }\n                            ]\n                        };\n                    });\n                }\n            }\n            // Group crew duties by crew member\n            const groupedCrewSections = groupCrewDutiesByMember(updatedData);\n            setCrew(groupedCrewSections);\n        }\n    }, [\n        crewSections,\n        vessel\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (masterID > 0) {\n            loadVesselCrews();\n        }\n    }, [\n        masterID\n    ]);\n    const loadDuties = async ()=>{\n        if (offline) {\n            const data = await crewDutyModel.getAll();\n            setAllDuties(data);\n            if (data) {\n                const activeDuties = data.filter((duty)=>!duty.archived);\n                setDuties(activeDuties);\n            }\n        } else {\n            await queryDuties();\n        }\n    };\n    const handleSetCrewConfig = ()=>{\n        if (logBookConfig && logBookConfig.customisedLogBookComponents && logBookConfig.customisedLogBookComponents.nodes && Array.isArray(logBookConfig.customisedLogBookComponents.nodes)) {\n            const crewMembersConfigs = logBookConfig.customisedLogBookComponents.nodes.filter((config)=>config && config.title === \"Crew Members\");\n            const length = crewMembersConfigs.length;\n            if (length === 1) {\n                const config = crewMembersConfigs[0];\n                if (config && config.customisedComponentFields && config.customisedComponentFields.nodes && Array.isArray(config.customisedComponentFields.nodes)) {\n                    setCrewConfig(config.customisedComponentFields.nodes.map((field)=>({\n                            title: field.fieldName,\n                            status: field.status\n                        })));\n                }\n            } else if (length > 1) {\n                const sortedConfigs = [\n                    ...crewMembersConfigs\n                ].sort((a, b)=>parseInt(b.id) - parseInt(a.id));\n                const config = sortedConfigs[0];\n                if (config && config.customisedComponentFields && config.customisedComponentFields.nodes && Array.isArray(config.customisedComponentFields.nodes)) {\n                    setCrewConfig(config.customisedComponentFields.nodes.map((field)=>({\n                            title: field.fieldName,\n                            status: field.status\n                        })));\n                }\n            }\n        } else {\n            setCrewConfig(false);\n        }\n    };\n    const handleSetStatus = ()=>{\n        if (Array.isArray(crewConfig) && crewConfig.length > 0 && logBookConfig && logBookConfig.customisedLogBookComponents && logBookConfig.customisedLogBookComponents.nodes && Array.isArray(logBookConfig.customisedLogBookComponents.nodes)) {\n            const crewMemberComponents = logBookConfig.customisedLogBookComponents.nodes.filter((config)=>config && config.title === \"Crew Members\");\n            const crewMemberComponent = crewMemberComponents.length > 0 ? crewMemberComponents[0] : null;\n            if (crewMemberComponent && crewMemberComponent.customisedComponentFields && crewMemberComponent.customisedComponentFields.nodes && Array.isArray(crewMemberComponent.customisedComponentFields.nodes)) {\n                // Crew Member\n                let title = \"CrewMemberID\";\n                const crewMemberField = crewMemberComponent.customisedComponentFields.nodes.find((item)=>item && item.fieldName === title);\n                // We already have a default value set in the useState, so we only need to update if we have a valid value\n                if (crewMemberField) {\n                // We don't need to set crew member label anymore as it's not used\n                // Keeping the code structure for future reference\n                }\n                // Primary Duty\n                title = \"DutyPerformedID\";\n                const primaryDutyField = crewMemberComponent.customisedComponentFields.nodes.find((item)=>item && item.fieldName === title);\n                // We already have a default value set in the useState, so we only need to update if we have a valid value\n                if (primaryDutyField) {\n                // We don't need to set primary duty label anymore as it's not used\n                // Keeping the code structure for future reference\n                }\n                // Punch in\n                title = \"PunchIn\";\n                const punchInConfig = crewConfig.find((config)=>config && config.title === title);\n                setPunchInStatus((punchInConfig === null || punchInConfig === void 0 ? void 0 : punchInConfig.status) || \"On\");\n                const punchInField = crewMemberComponent.customisedComponentFields.nodes.find((item)=>item && item.fieldName === title);\n                // We already have a default value set in the useState, so we only need to update if we have a valid value\n                if (punchInField) {\n                    const customTitle = punchInField.customisedFieldTitle;\n                    const fieldNameValue = (0,_vessels_actions__WEBPACK_IMPORTED_MODULE_17__.getFieldName)(punchInField, _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_18__.SLALL_LogBookFields);\n                    // Only update if we have a valid value\n                    if (customTitle && customTitle.trim() !== \"\") {\n                        setPunchInLabel(customTitle);\n                    } else if (fieldNameValue && fieldNameValue.trim() !== \"\") {\n                        setPunchInLabel(fieldNameValue);\n                    }\n                // Otherwise keep the default 'Sign In'\n                }\n                // Punch out\n                title = \"PunchOut\";\n                const punchOutConfig = crewConfig.find((config)=>config && config.title === title);\n                setPunchOutStatus((punchOutConfig === null || punchOutConfig === void 0 ? void 0 : punchOutConfig.status) || \"On\");\n                const punchOutField = crewMemberComponent.customisedComponentFields.nodes.find((item)=>item && item.fieldName === title);\n                // We already have a default value set in the useState, so we only need to update if we have a valid value\n                if (punchOutField) {\n                    const customTitle = punchOutField.customisedFieldTitle;\n                    const fieldNameValue = (0,_vessels_actions__WEBPACK_IMPORTED_MODULE_17__.getFieldName)(punchOutField, _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_18__.SLALL_LogBookFields);\n                    // Only update if we have a valid value\n                    if (customTitle && customTitle.trim() !== \"\") {\n                        setPunchOutLabel(customTitle);\n                    } else if (fieldNameValue && fieldNameValue.trim() !== \"\") {\n                        setPunchOutLabel(fieldNameValue);\n                    }\n                // Otherwise keep the default 'Sign Out'\n                }\n                // Work details\n                title = \"WorkDetails\";\n                const workDetailsConfig = crewConfig.find((config)=>config && config.title === title);\n                setWorkDetailsStatus((workDetailsConfig === null || workDetailsConfig === void 0 ? void 0 : workDetailsConfig.status) || \"On\");\n                const workDetailsField = crewMemberComponent.customisedComponentFields.nodes.find((item)=>item && item.fieldName === title);\n                // We already have a default value set in the useState, so we only need to update if we have a valid value\n                if (workDetailsField) {\n                    const customTitle = workDetailsField.customisedFieldTitle;\n                    const fieldNameValue = (0,_vessels_actions__WEBPACK_IMPORTED_MODULE_17__.getFieldName)(workDetailsField, _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_18__.SLALL_LogBookFields);\n                    // Only update if we have a valid value\n                    if (customTitle && customTitle.trim() !== \"\") {\n                        setWorkDetailsLabel(customTitle);\n                    } else if (fieldNameValue && fieldNameValue.trim() !== \"\") {\n                        setWorkDetailsLabel(fieldNameValue);\n                    }\n                // Otherwise keep the default 'Work Details'\n                }\n            }\n        } else {\n            // Set default values if crewConfig is not valid\n            setPunchInStatus(\"On\");\n            setPunchInLabel(\"Sign In\");\n            setPunchOutStatus(\"On\");\n            setPunchOutLabel(\"Sign Out\");\n            setWorkDetailsStatus(\"On\");\n            setWorkDetailsLabel(\"Work Details\");\n        }\n    };\n    const [queryDuties] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.CREW_DUTY, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readCrewDuties.nodes;\n            if (data) {\n                const activeDuties = data.filter((duty)=>!duty.archived);\n                setDuties(activeDuties);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryDutiesEntry error\", error);\n        }\n    });\n    const handleLogin = (date)=>{\n        if (!date) {\n            // Handle the case when date is null or undefined (unselected)\n            const currentTime = new Date();\n            setLoginTime(currentTime);\n            setCrewManifestEntry({\n                ...crewManifestEntry,\n                punchIn: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(currentTime)\n            });\n            return;\n        }\n        try {\n            // Ensure we have a valid date by creating a new Date object\n            const validDate = new Date(date);\n            // Check if the date is valid\n            if (isNaN(validDate.getTime())) {\n                console.error(\"Invalid date provided to handleLogin:\", date);\n                return;\n            }\n            setLoginTime(validDate);\n            setCrewManifestEntry({\n                ...crewManifestEntry,\n                punchIn: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(validDate)\n            });\n            // If logout time is set and is before the new login time, reset it\n            if (logoutTime && validDate.getTime() > logoutTime.getTime()) {\n                setLogoutTime(null);\n                setCrewManifestEntry((prev)=>({\n                        ...prev,\n                        punchOut: null\n                    }));\n            }\n        } catch (error) {\n            console.error(\"Error in handleLogin:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"An error occurred while setting the sign in time\");\n        }\n    };\n    const handleLogout = (date)=>{\n        if (!date) {\n            // Handle the case when date is null or undefined (unselected)\n            setLogoutTime(null);\n            setCrewManifestEntry({\n                ...crewManifestEntry,\n                punchOut: null\n            });\n            return;\n        }\n        try {\n            // Ensure we have a valid date by creating a new Date object\n            const validDate = new Date(date);\n            // Check if the date is valid\n            if (isNaN(validDate.getTime())) {\n                console.error(\"Invalid date provided to handleLogout:\", date);\n                return;\n            }\n            // If the date doesn't have time set (hours and minutes are 0),\n            // set the current time\n            if (validDate.getHours() === 0 && validDate.getMinutes() === 0) {\n                const now = new Date();\n                validDate.setHours(now.getHours());\n                validDate.setMinutes(now.getMinutes());\n            }\n            // Convert to dayjs for easier comparison\n            const dayjsDate = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(validDate);\n            // Ensure logout time is after login time\n            if (loginTime && dayjsDate.isBefore(loginTime)) {\n                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Sign out time must be after sign in time\");\n                return;\n            }\n            // Store the date as a standard Date object to avoid any issues with dayjs\n            setLogoutTime(validDate);\n            // Update crew manifest entry with formatted date string\n            setCrewManifestEntry({\n                ...crewManifestEntry,\n                punchOut: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(validDate)\n            });\n        } catch (error) {\n            console.error(\"Error in handleLogout:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"An error occurred while setting the sign out time\");\n        }\n    };\n    const handleAddManifest = ()=>{\n        // Check permissions first\n        if (!edit_logBookEntry) {\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"You do not have permission to edit this log entry\");\n            return;\n        }\n        // Filter crew member options to only show available crew members\n        if (allMembers && Array.isArray(allMembers)) {\n            // Get a list of crew members who are already signed in (without sign-out time)\n            const signedInCrewMemberIDs = new Set();\n            if (crew && Array.isArray(crew)) {\n                crew.forEach((member)=>{\n                    // Only consider members who are signed in without a sign-out time\n                    if (member && member.duties && Array.isArray(member.duties)) {\n                        // Check if any duty has no punch out time\n                        const hasActiveShift = member.duties.some((duty)=>duty && duty.punchOut === null);\n                        if (hasActiveShift) {\n                            signedInCrewMemberIDs.add(member.crewMemberID);\n                        }\n                    } else if (member && member.punchOut === null) {\n                        signedInCrewMemberIDs.add(member.crewMemberID);\n                    }\n                });\n            }\n            // Filter out crew members who are already signed in\n            const availableCrewMembers = allMembers.filter((member)=>{\n                if (!member) return false;\n                return !signedInCrewMemberIDs.has(member.value);\n            });\n            // Further filter out crew members who are in the crewMembersList (if applicable)\n            const filteredCrewOptions = availableCrewMembers.filter((member)=>!member || !crewMembersList || !Array.isArray(crewMembersList) || !crewMembersList.includes(+member.value));\n            setCrewMemberOptions(filteredCrewOptions);\n        } else {\n            // If allMembers is not valid, just proceed with empty options\n            setCrewMemberOptions([]);\n        }\n        // Set up the new crew manifest entry with current time\n        const currentTime = new Date();\n        const crewManifestEntry = {\n            id: 0,\n            logBookEntryID: +logBookEntryID,\n            crewMemberID: 0,\n            dutyPerformedID: 0,\n            punchIn: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(currentTime),\n            punchOut: null\n        };\n        setCrewManifestEntry(crewManifestEntry);\n        setLoginTime(currentTime);\n        setLogoutTime(null);\n        setCrewMember(null);\n        setDuty(null);\n        setopenAddCrewMemberDialog(true);\n    };\n    const handleEditManifest = (memberData)=>{\n        if (!edit_logBookEntry) {\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"You do not have permission to edit this log entry\");\n            return;\n        }\n        // If this is a grouped crew member with multiple duties, use the first duty\n        const dutyToEdit = memberData.duties && memberData.duties.length > 0 ? memberData.duties[0] : memberData;\n        setCrewManifestEntry({\n            id: dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.id,\n            logBookEntryID: dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.logBookEntryID,\n            crewMemberID: memberData === null || memberData === void 0 ? void 0 : memberData.crewMemberID,\n            dutyPerformedID: dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.dutyPerformedID,\n            punchIn: dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.punchIn,\n            punchOut: dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.punchOut,\n            workDetails: dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.workDetails\n        });\n        // Create a proper crew member object with profile details\n        const crewMemberWithProfile = {\n            label: \"\".concat(memberData.crewMember.firstName || \"\", \" \").concat(memberData.crewMember.surname !== null ? memberData.crewMember.surname : \"\").trim(),\n            value: memberData.crewMember.id,\n            data: memberData.crewMember,\n            profile: {\n                firstName: memberData.crewMember.firstName,\n                surname: memberData.crewMember.surname,\n                avatar: memberData.crewMember.profileImage\n            }\n        };\n        setCrewMember(crewMemberWithProfile);\n        // Find the correct duty in the duties array\n        const selectedDuty = duties.find((memberDuty)=>memberDuty.id === (dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.dutyPerformedID));\n        if (selectedDuty) {\n            setDuty({\n                label: selectedDuty.title,\n                value: selectedDuty.id\n            });\n        }\n        setLoginTime((dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.punchIn) ? new Date(dutyToEdit.punchIn) : new Date());\n        setLogoutTime((dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.punchOut) ? new Date(dutyToEdit.punchOut) : null);\n        setopenAddCrewMemberDialog(true);\n    };\n    const handleSignOutTime = (memberData)=>{\n        if (!edit_logBookEntry) {\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"You do not have permission to edit this log entry\");\n            return;\n        }\n        // Determine if this is a nested duty or a main crew member\n        const isNestedDuty = !memberData.crewMember;\n        if (isNestedDuty) {\n            // This is a nested duty\n            // Find the parent crew member for this duty\n            const parentMember = crew && Array.isArray(crew) ? crew.find((c)=>{\n                return c && c.duties && Array.isArray(c.duties) && c.duties.some((d)=>d && d.id === memberData.id);\n            }) : null;\n            if (parentMember) {\n                var _memberData_dutyPerformed;\n                // Set crew manifest entry with the parent crew member ID\n                setCrewManifestEntry({\n                    id: memberData === null || memberData === void 0 ? void 0 : memberData.id,\n                    logBookEntryID: memberData === null || memberData === void 0 ? void 0 : memberData.logBookEntryID,\n                    crewMemberID: parentMember.crewMemberID,\n                    dutyPerformedID: memberData === null || memberData === void 0 ? void 0 : (_memberData_dutyPerformed = memberData.dutyPerformed) === null || _memberData_dutyPerformed === void 0 ? void 0 : _memberData_dutyPerformed.id,\n                    punchIn: memberData === null || memberData === void 0 ? void 0 : memberData.punchIn,\n                    punchOut: memberData === null || memberData === void 0 ? void 0 : memberData.punchOut,\n                    workDetails: memberData === null || memberData === void 0 ? void 0 : memberData.workDetails\n                });\n                // Create a proper crew member object with profile details\n                const crewMemberWithProfile = {\n                    label: \"\".concat(parentMember.crewMember.firstName || \"\", \" \").concat(parentMember.crewMember.surname !== null ? parentMember.crewMember.surname : \"\").trim(),\n                    value: parentMember.crewMember.id,\n                    data: parentMember.crewMember,\n                    profile: {\n                        firstName: parentMember.crewMember.firstName,\n                        surname: parentMember.crewMember.surname,\n                        avatar: parentMember.crewMember.profileImage\n                    }\n                };\n                setCrewMember(crewMemberWithProfile);\n                // Set duty\n                if (memberData.dutyPerformed) {\n                    const selectedDuty = duties.find((memberDuty)=>{\n                        var _memberData_dutyPerformed;\n                        return memberDuty.id === (memberData === null || memberData === void 0 ? void 0 : (_memberData_dutyPerformed = memberData.dutyPerformed) === null || _memberData_dutyPerformed === void 0 ? void 0 : _memberData_dutyPerformed.id);\n                    });\n                    if (selectedDuty) {\n                        setDuty({\n                            label: selectedDuty.title,\n                            value: selectedDuty.id\n                        });\n                    }\n                }\n            } else {\n                // If parent member not found, show an error\n                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Could not find the associated crew member\");\n                return;\n            }\n        } else {\n            var _memberData_dutyPerformed1;\n            // This is a main crew member\n            setCrewManifestEntry({\n                id: memberData === null || memberData === void 0 ? void 0 : memberData.id,\n                logBookEntryID: memberData === null || memberData === void 0 ? void 0 : memberData.logBookEntryID,\n                crewMemberID: memberData === null || memberData === void 0 ? void 0 : memberData.crewMemberID,\n                dutyPerformedID: memberData === null || memberData === void 0 ? void 0 : (_memberData_dutyPerformed1 = memberData.dutyPerformed) === null || _memberData_dutyPerformed1 === void 0 ? void 0 : _memberData_dutyPerformed1.id,\n                punchIn: memberData === null || memberData === void 0 ? void 0 : memberData.punchIn,\n                punchOut: memberData === null || memberData === void 0 ? void 0 : memberData.punchOut,\n                workDetails: memberData === null || memberData === void 0 ? void 0 : memberData.workDetails\n            });\n            // Create a proper crew member object with profile details\n            const crewMemberWithProfile = {\n                label: \"\".concat(memberData.crewMember.firstName || \"\", \" \").concat(memberData.crewMember.surname !== null ? memberData.crewMember.surname : \"\").trim(),\n                value: memberData.crewMember.id,\n                data: memberData.crewMember,\n                profile: {\n                    firstName: memberData.crewMember.firstName,\n                    surname: memberData.crewMember.surname,\n                    avatar: memberData.crewMember.profileImage\n                }\n            };\n            setCrewMember(crewMemberWithProfile);\n            // Set duty\n            if (memberData.dutyPerformed) {\n                const selectedDuty = duties.find((memberDuty)=>{\n                    var _memberData_dutyPerformed;\n                    return memberDuty.id === (memberData === null || memberData === void 0 ? void 0 : (_memberData_dutyPerformed = memberData.dutyPerformed) === null || _memberData_dutyPerformed === void 0 ? void 0 : _memberData_dutyPerformed.id);\n                });\n                if (selectedDuty) {\n                    setDuty({\n                        label: selectedDuty.title,\n                        value: selectedDuty.id\n                    });\n                }\n            }\n        }\n        // Set times\n        setLoginTime(memberData.punchIn ? new Date(memberData.punchIn) : new Date());\n        setLogoutTime(memberData.punchOut ? new Date(memberData.punchOut) : null);\n        setOpenEditLogoutTimeDialog(true);\n    };\n    const handleCrewMember = async (selected)=>{\n        if (!selected) return;\n        setDuty({\n            label: \"-- Select Duty --\",\n            value: 0\n        });\n        const { data } = await queryCrewDetail({\n            variables: {\n                crewMemberID: selected.value\n            }\n        });\n        const member = data.readOneSeaLogsMember;\n        const crewWithTraining = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n            member\n        ], [\n            vessel\n        ])[0];\n        const value = {\n            ...selected,\n            data: crewWithTraining\n        };\n        setCrewMember(value);\n        // Check if the crew has a training due\n        if (value.data && value.data.trainingStatus && value.data.trainingStatus.label !== \"Good\") {\n            setOpenCrewTrainingDueDialog(true);\n        }\n        // Set default duty\n        if (allCrew && Array.isArray(allCrew)) {\n            const crewMember = allCrew.find((member)=>member && member.id === value.value);\n            if (crewMember && crewMember.primaryDutyID) {\n                if (duties && Array.isArray(duties)) {\n                    const crewDuty = duties.find((d)=>d && d.id === crewMember.primaryDutyID);\n                    if (crewDuty) {\n                        const newDuty = {\n                            label: crewDuty.title,\n                            value: crewDuty.id\n                        };\n                        setDuty(newDuty);\n                        setCrewManifestEntry({\n                            ...crewManifestEntry,\n                            crewMemberID: crewMember.id,\n                            dutyPerformedID: crewDuty.id\n                        });\n                    } else {\n                        setCrewManifestEntry({\n                            ...crewManifestEntry,\n                            crewMemberID: crewMember.id\n                        });\n                    }\n                } else {\n                    setCrewManifestEntry({\n                        ...crewManifestEntry,\n                        crewMemberID: crewMember.id\n                    });\n                }\n            } else if (crewMember) {\n                setCrewManifestEntry({\n                    ...crewManifestEntry,\n                    crewMemberID: crewMember.id\n                });\n            }\n        }\n    };\n    const handleDuty = (value)=>{\n        setDuty(value);\n        setCrewManifestEntry({\n            ...crewManifestEntry,\n            dutyPerformedID: (value === null || value === void 0 ? void 0 : value.value) || 0\n        });\n    };\n    const handleCancel = ()=>{\n        setCrewManifestEntry({});\n        setCrewMember(null);\n        setDuty(null);\n        setLoginTime(new Date());\n        setLogoutTime(null);\n        setopenAddCrewMemberDialog(false);\n        setOpenEditLogoutTimeDialog(false);\n    };\n    const handleSave = async (callBy)=>{\n        // Validate required fields\n        if (!crewManifestEntry.crewMemberID || crewManifestEntry.crewMemberID === 0) {\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Please select a crew member\");\n            return;\n        }\n        if (!crewManifestEntry.dutyPerformedID || crewManifestEntry.dutyPerformedID === 0) {\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Please select a duty\");\n            return;\n        }\n        // Get work details from the textarea\n        const workDetailsElement = document.getElementById(\"work-details\");\n        const workDetails = (workDetailsElement === null || workDetailsElement === void 0 ? void 0 : workDetailsElement.value) || \"\";\n        const variables = {\n            id: crewManifestEntry.id,\n            crewMemberID: crewManifestEntry.crewMemberID,\n            dutyPerformedID: +(crewManifestEntry === null || crewManifestEntry === void 0 ? void 0 : crewManifestEntry.dutyPerformedID),\n            logBookEntryID: +logBookEntryID,\n            punchIn: loginTime ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(loginTime) : null,\n            punchOut: logoutTime ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(logoutTime) : null,\n            workDetails: workDetails\n        };\n        try {\n            // Case 1: Updating an existing crew entry\n            if (crewManifestEntry.id > 0) {\n                if (offline) {\n                    // Save the updated crew member to the database\n                    await lbCrewModel.save(variables);\n                    // Get all crew IDs to fetch updated data\n                    const crewIds = crew && Array.isArray(crew) ? crew.map((c)=>c.id).filter(Boolean) : [];\n                    // Reset the form\n                    setCrewManifestEntry({});\n                    // Fetch the updated crew data\n                    let crewData = await lbCrewModel.getByIds(crewIds);\n                    if (crewData) {\n                        // Process crew members with training status\n                        const processedData = crewData.map((section)=>{\n                            if (section && section.crewMember) {\n                                const crewMemberWithTrainingStatus = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n                                    section.crewMember\n                                ], [\n                                    vessel\n                                ])[0];\n                                return {\n                                    ...section,\n                                    crewMember: crewMemberWithTrainingStatus\n                                };\n                            }\n                            return section;\n                        });\n                        // Group crew duties by crew member\n                        const groupedData = groupCrewDutiesByMember(processedData);\n                        // Update state\n                        setCrew(groupedData);\n                        setCrewMembers(groupedData);\n                    }\n                } else {\n                    // Online mode - use GraphQL mutation\n                    updateCrewMembers_LogBookEntrySection({\n                        variables: {\n                            input: variables\n                        }\n                    });\n                }\n                // Close dialogs\n                setopenAddCrewMemberDialog(false);\n                if (callBy === \"update\") {\n                    setOpenEditLogoutTimeDialog(false);\n                }\n            } else if (crewManifestEntry.crewMemberID > 0) {\n                if (offline) {\n                    // Generate a unique ID for the new entry\n                    const uniqueId = (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)();\n                    const data = {\n                        ...variables,\n                        id: uniqueId\n                    };\n                    // Save the new crew member to the database\n                    await lbCrewModel.save(data);\n                    // Get the selected crew member and duty information\n                    const selectedMember = allVesselCrews.find((c)=>c.id === crewManifestEntry.crewMemberID);\n                    const selectedDuty = allDuties.find((d)=>d.id === crewManifestEntry.dutyPerformedID);\n                    if (!selectedMember || !selectedDuty) {\n                        sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Could not find crew member or duty information\");\n                        return;\n                    }\n                    // Create a new crew entry with the necessary data for immediate display\n                    const newCrewEntry = {\n                        ...data,\n                        crewMember: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n                            selectedMember\n                        ], [\n                            vessel\n                        ])[0],\n                        dutyPerformed: selectedDuty\n                    };\n                    // Get existing crew data or initialize empty array\n                    const existingCrew = Array.isArray(crew) ? [\n                        ...crew\n                    ] : [];\n                    // Check if this crew member already exists in the list\n                    const existingCrewMemberIndex = existingCrew.findIndex((c)=>c && c.crewMemberID === data.crewMemberID);\n                    let updatedCrewData = [\n                        ...existingCrew\n                    ];\n                    if (existingCrewMemberIndex !== -1) {\n                        // If the crew member already exists, add this duty to their duties array\n                        const existingCrewMember = {\n                            ...updatedCrewData[existingCrewMemberIndex]\n                        };\n                        if (existingCrewMember.duties && Array.isArray(existingCrewMember.duties)) {\n                            // Add the new duty to the existing duties array\n                            existingCrewMember.duties.push({\n                                id: data.id,\n                                dutyPerformed: selectedDuty,\n                                punchIn: data.punchIn,\n                                punchOut: data.punchOut,\n                                workDetails: data.workDetails,\n                                dutyPerformedID: data.dutyPerformedID,\n                                logBookEntryID: data.logBookEntryID\n                            });\n                        } else {\n                            // Create a duties array if it doesn't exist\n                            existingCrewMember.duties = [\n                                {\n                                    id: data.id,\n                                    dutyPerformed: selectedDuty,\n                                    punchIn: data.punchIn,\n                                    punchOut: data.punchOut,\n                                    workDetails: data.workDetails,\n                                    dutyPerformedID: data.dutyPerformedID,\n                                    logBookEntryID: data.logBookEntryID\n                                }\n                            ];\n                        }\n                        // Update the crew member in the array\n                        updatedCrewData[existingCrewMemberIndex] = existingCrewMember;\n                    } else {\n                        // If this is a new crew member, add them to the list with their first duty\n                        updatedCrewData.push({\n                            ...newCrewEntry,\n                            duties: [\n                                {\n                                    id: data.id,\n                                    dutyPerformed: selectedDuty,\n                                    punchIn: data.punchIn,\n                                    punchOut: data.punchOut,\n                                    workDetails: data.workDetails,\n                                    dutyPerformedID: data.dutyPerformedID,\n                                    logBookEntryID: data.logBookEntryID\n                                }\n                            ]\n                        });\n                    }\n                    // Group the updated crew data\n                    const groupedData = groupCrewDutiesByMember(updatedCrewData);\n                    // Update state with the new grouped data\n                    setCrew(groupedData);\n                    setCrewMembers(groupedData);\n                    setCrewManifestEntry({});\n                    // Also fetch the latest data from the database to ensure consistency\n                    const crewIds = updatedCrewData.map((c)=>c && c.id).filter(Boolean).concat([\n                        uniqueId\n                    ]);\n                    let crewData = await lbCrewModel.getByIds(crewIds);\n                    if (crewData) {\n                        // Process crew members with training status\n                        const processedDbData = crewData.map((section)=>{\n                            if (section && section.crewMember) {\n                                const crewMemberWithTrainingStatus = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n                                    section.crewMember\n                                ], [\n                                    vessel\n                                ])[0];\n                                return {\n                                    ...section,\n                                    crewMember: crewMemberWithTrainingStatus\n                                };\n                            }\n                            return section;\n                        });\n                        // Group crew duties by crew member\n                        const groupedDbData = groupCrewDutiesByMember(processedDbData);\n                        // Update with the database data to ensure consistency\n                        setCrew(groupedDbData);\n                        setCrewMembers(groupedDbData);\n                    }\n                } else {\n                    // Online mode - use GraphQL mutation\n                    createCrewMembers_LogBookEntrySection({\n                        variables: {\n                            input: variables\n                        }\n                    });\n                }\n                // Close dialog\n                setopenAddCrewMemberDialog(false);\n            } else {\n                // No valid crew member selected, just cancel\n                handleCancel();\n            }\n        } catch (error) {\n            console.error(\"Error saving crew member:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Failed to save crew member. Please try again.\");\n        }\n    };\n    const [updateCrewMembers_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateCrewMembers_LogBookEntrySection, {\n        onCompleted: ()=>{\n            // First, update the UI immediately with the updated data\n            if (crewManifestEntry.id > 0 && crew && Array.isArray(crew)) {\n                // Create a deep copy of the current crew data\n                let updatedCrewData = JSON.parse(JSON.stringify(crew));\n                // Find the crew member and duty that was updated\n                let foundAndUpdated = false;\n                // Loop through all crew members\n                for(let i = 0; i < updatedCrewData.length; i++){\n                    const member = updatedCrewData[i];\n                    // Check if this is the main duty that was updated\n                    if (member.id === crewManifestEntry.id) {\n                        // Update the main duty\n                        updatedCrewData[i] = {\n                            ...member,\n                            punchOut: crewManifestEntry.punchOut\n                        };\n                        foundAndUpdated = true;\n                        break;\n                    }\n                    // Check if this is a nested duty that was updated\n                    if (member.duties && Array.isArray(member.duties)) {\n                        for(let j = 0; j < member.duties.length; j++){\n                            const duty = member.duties[j];\n                            if (duty.id === crewManifestEntry.id) {\n                                // Update the nested duty\n                                member.duties[j] = {\n                                    ...duty,\n                                    punchOut: crewManifestEntry.punchOut\n                                };\n                                foundAndUpdated = true;\n                                break;\n                            }\n                        }\n                        if (foundAndUpdated) {\n                            break;\n                        }\n                    }\n                }\n                if (foundAndUpdated) {\n                    // Group the updated crew data\n                    const groupedData = groupCrewDutiesByMember(updatedCrewData);\n                    // Update state with the new grouped data\n                    setCrew(groupedData);\n                    setCrewMembers(groupedData);\n                }\n            }\n            // Then, fetch the latest data from the server to ensure consistency\n            const appendData = [\n                ...crew.map((c)=>c.id)\n            ];\n            setCrewManifestEntry({});\n            const searchFilter = {};\n            searchFilter.id = {\n                in: appendData\n            };\n            searchFilter.archived = {\n                eq: false\n            };\n            getSectionCrewMembers_LogBookEntrySection({\n                variables: {\n                    filter: searchFilter\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"updateCrewMembers_LogBookEntrySection\", error);\n        }\n    });\n    const [createCrewWelfareCheck, { loading: createCrewWelfareCheckLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateCrewWelfare_LogBookEntrySection, {\n        onCompleted: (response)=>{\n            const data = response.createCrewWelfare_LogBookEntrySection;\n            updateCrewWelfare(data);\n        },\n        onError: (error)=>{\n            console.error(\"createCrewWelfareCheck\", error);\n        }\n    });\n    const [createCrewMembers_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateCrewMembers_LogBookEntrySection, {\n        onCompleted: (data)=>{\n            var _allMembers_find;\n            // First, update the UI immediately with the new crew member\n            // Get the selected crew member and duty information\n            const selectedMember = (_allMembers_find = allMembers.find((m)=>m.value === crewManifestEntry.crewMemberID)) === null || _allMembers_find === void 0 ? void 0 : _allMembers_find.data;\n            const selectedDuty = duties.find((d)=>d.id === crewManifestEntry.dutyPerformedID);\n            if (selectedMember && selectedDuty) {\n                var _document_getElementById;\n                // Create a new crew entry with the necessary data for immediate display\n                const newCrewEntry = {\n                    id: data.createCrewMembers_LogBookEntrySection.id,\n                    crewMemberID: crewManifestEntry.crewMemberID,\n                    dutyPerformedID: crewManifestEntry.dutyPerformedID,\n                    logBookEntryID: +logBookEntryID,\n                    punchIn: loginTime ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(loginTime) : null,\n                    punchOut: logoutTime ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(logoutTime) : null,\n                    workDetails: ((_document_getElementById = document.getElementById(\"work-details\")) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value) || \"\",\n                    crewMember: selectedMember,\n                    dutyPerformed: selectedDuty\n                };\n                // Create a new array with existing crew data plus the new entry\n                let updatedCrewData = crew ? [\n                    ...crew\n                ] : [];\n                // Check if this crew member already exists in the list\n                const existingCrewMemberIndex = updatedCrewData.findIndex((c)=>c.crewMemberID === crewManifestEntry.crewMemberID);\n                if (existingCrewMemberIndex >= 0) {\n                    // If the crew member already exists, add this duty to their duties array\n                    const existingCrewMember = updatedCrewData[existingCrewMemberIndex];\n                    const updatedDuties = existingCrewMember.duties ? [\n                        ...existingCrewMember.duties\n                    ] : [];\n                    updatedDuties.push({\n                        id: data.createCrewMembers_LogBookEntrySection.id,\n                        dutyPerformed: selectedDuty,\n                        punchIn: newCrewEntry.punchIn,\n                        punchOut: newCrewEntry.punchOut,\n                        workDetails: newCrewEntry.workDetails,\n                        dutyPerformedID: newCrewEntry.dutyPerformedID,\n                        logBookEntryID: newCrewEntry.logBookEntryID\n                    });\n                    // Update the crew member with the new duties array\n                    updatedCrewData[existingCrewMemberIndex] = {\n                        ...existingCrewMember,\n                        duties: updatedDuties\n                    };\n                } else {\n                    // If this is a new crew member, add them to the list with their first duty\n                    updatedCrewData.push({\n                        ...newCrewEntry,\n                        duties: [\n                            {\n                                id: data.createCrewMembers_LogBookEntrySection.id,\n                                dutyPerformed: selectedDuty,\n                                punchIn: newCrewEntry.punchIn,\n                                punchOut: newCrewEntry.punchOut,\n                                workDetails: newCrewEntry.workDetails,\n                                dutyPerformedID: newCrewEntry.dutyPerformedID,\n                                logBookEntryID: newCrewEntry.logBookEntryID\n                            }\n                        ]\n                    });\n                }\n                // Group the updated crew data\n                const groupedData = groupCrewDutiesByMember(updatedCrewData);\n                // Update state with the new grouped data\n                setCrew(groupedData);\n                setCrewMembers(groupedData);\n            }\n            // Then, fetch the latest data from the server to ensure consistency\n            const appendData = crew ? [\n                ...crew === null || crew === void 0 ? void 0 : crew.map((c)=>c.id),\n                data.createCrewMembers_LogBookEntrySection.id\n            ] : [\n                data.createCrewMembers_LogBookEntrySection.id\n            ];\n            setCrewManifestEntry({});\n            const searchFilter = {};\n            searchFilter.id = {\n                in: appendData\n            };\n            getSectionCrewMembers_LogBookEntrySection({\n                variables: {\n                    filter: searchFilter\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"createCrewMembers_LogBookEntrySection\", error);\n        }\n    });\n    const [getSectionCrewMembers_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.CrewMembers_LogBookEntrySection, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readCrewMembers_LogBookEntrySections.nodes;\n            if (data) {\n                // Process crew members with training status\n                const processedData = data.map((section)=>{\n                    if (section.crewMember) {\n                        const crewMemberWithTrainingStatus = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n                            section.crewMember\n                        ], [\n                            vessel\n                        ])[0];\n                        return {\n                            ...section,\n                            crewMember: crewMemberWithTrainingStatus\n                        };\n                    }\n                    return section;\n                });\n                // Preserve existing duties if they exist\n                let updatedData = processedData;\n                if (crew && crew.length > 0) {\n                    // Create a map of existing crew members with their duties\n                    const existingCrewMap = crew.reduce((map, member)=>{\n                        if (member.crewMemberID) {\n                            map[member.crewMemberID] = member;\n                        }\n                        return map;\n                    }, {});\n                    // Update processed data with existing duties where applicable\n                    updatedData = processedData.map((section)=>{\n                        const existingMember = existingCrewMap[section.crewMemberID];\n                        if (existingMember && existingMember.duties && existingMember.duties.length > 0) {\n                            // Find the matching duty in the existing duties array\n                            const existingDutyIndex = existingMember.duties.findIndex((duty)=>duty.id === section.id);\n                            if (existingDutyIndex >= 0) {\n                                // This section is already in the duties, update it with the latest data\n                                const updatedDuties = [\n                                    ...existingMember.duties\n                                ];\n                                updatedDuties[existingDutyIndex] = {\n                                    ...updatedDuties[existingDutyIndex],\n                                    // Update with the latest data from the server\n                                    punchIn: section.punchIn,\n                                    punchOut: section.punchOut,\n                                    workDetails: section.workDetails\n                                };\n                                return {\n                                    ...section,\n                                    duties: updatedDuties\n                                };\n                            } else {\n                                // This is a new duty for this crew member, add it to their duties\n                                const updatedDuties = [\n                                    ...existingMember.duties\n                                ];\n                                updatedDuties.push({\n                                    id: section.id,\n                                    dutyPerformed: section.dutyPerformed,\n                                    punchIn: section.punchIn,\n                                    punchOut: section.punchOut,\n                                    workDetails: section.workDetails,\n                                    dutyPerformedID: section.dutyPerformedID,\n                                    logBookEntryID: section.logBookEntryID\n                                });\n                                return {\n                                    ...section,\n                                    duties: updatedDuties\n                                };\n                            }\n                        }\n                        // No existing duties for this crew member, create a new duties array\n                        return {\n                            ...section,\n                            duties: [\n                                {\n                                    id: section.id,\n                                    dutyPerformed: section.dutyPerformed,\n                                    punchIn: section.punchIn,\n                                    punchOut: section.punchOut,\n                                    workDetails: section.workDetails,\n                                    dutyPerformedID: section.dutyPerformedID,\n                                    logBookEntryID: section.logBookEntryID\n                                }\n                            ]\n                        };\n                    });\n                }\n                // Group crew duties by crew member\n                const groupedData = groupCrewDutiesByMember(updatedData);\n                setCrew(groupedData);\n                setCrewMembers(groupedData);\n                const members = allMembers.filter((member)=>{\n                    if (!data) {\n                        return true;\n                    }\n                    return !data.some((section)=>section.crewMember.id === member.value && section.punchOut === null);\n                });\n                setCrewMemberOptions(members.filter((member)=>!crewMembersList || !crewMembersList.includes(+member.value)));\n            }\n        },\n        onError: (error)=>{\n            console.error(\"getSectionCrewMembers_LogBookEntrySection\", error);\n        }\n    });\n    const handleArchive = async ()=>{\n        setOpenConfirmCrewDeleteDialog(false);\n        if (!crewManifestEntry.id) {\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"No crew member selected to delete\");\n            return;\n        }\n        if (offline) {\n            try {\n                // First try to delete the record\n                const result = await lbCrewModel.delete({\n                    id: crewManifestEntry.id\n                });\n                if (!result) {\n                    // If delete fails, mark as archived\n                    await lbCrewModel.save({\n                        id: crewManifestEntry.id,\n                        archived: true\n                    });\n                }\n                const appendData = [\n                    ...crew.map((c)=>c.id)\n                ];\n                setCrewManifestEntry({});\n                const data = await lbCrewModel.getByIds(appendData);\n                if (data) {\n                    // Process crew members with training status\n                    const processedData = data.map((section)=>{\n                        if (section.crewMember) {\n                            const crewMemberWithTrainingStatus = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n                                section.crewMember\n                            ], [\n                                vessel\n                            ])[0];\n                            return {\n                                ...section,\n                                crewMember: crewMemberWithTrainingStatus\n                            };\n                        }\n                        return section;\n                    });\n                    // Group crew duties by crew member\n                    const groupedData = groupCrewDutiesByMember(processedData);\n                    setCrew(groupedData);\n                    setCrewMembers(groupedData);\n                }\n            } catch (error) {\n                console.error(\"Error deleting crew member:\", error);\n                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Failed to delete crew member\");\n            }\n        } else {\n            try {\n                // Use the delete mutation instead of update\n                await deleteCrewMembersLogBookEntrySections({\n                    variables: {\n                        ids: [\n                            crewManifestEntry.id\n                        ]\n                    }\n                });\n            } catch (error) {\n                console.error(\"Error deleting crew member:\", error);\n                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Failed to delete crew member\");\n            }\n        }\n        setopenAddCrewMemberDialog(false);\n    };\n    // Function removed as we're directly using setOpenConfirmCrewDeleteDialog\n    const [deleteCrewMembersLogBookEntrySections] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.DeleteCrewMembers_LogBookEntrySections, {\n        onCompleted: ()=>{\n            const appendData = [\n                ...crew.map((c)=>c.id)\n            ];\n            const searchFilter = {};\n            searchFilter.id = {\n                in: appendData\n            };\n            getSectionCrewMembers_LogBookEntrySection({\n                variables: {\n                    filter: searchFilter\n                }\n            });\n            setOpenConfirmCrewDeleteDialog(false);\n            setopenAddCrewMemberDialog(false);\n        },\n        onError: (error)=>{\n            console.error(\"deleteCrewMembersLogBookEntrySections error:\", error);\n        }\n    });\n    // Function removed as we're using handleArchive instead\n    const crewCount = ()=>{\n        if (!crew || !Array.isArray(crew)) return 0;\n        const count = crew.filter((member)=>member && member.crewMemberID > 0 && member.punchOut === null).length;\n        return count;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (crewConfig) {\n            handleSetStatus();\n        }\n    }, [\n        crewConfig\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_19___default()(logBookConfig)) {\n            handleSetCrewConfig();\n        }\n    }, [\n        logBookConfig\n    ]);\n    var _vessel_maxPOB, _vessel_maxPOB1;\n    // Removed unused overdueTextWarning variable\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid lg:grid-cols-8 gap-36 lg:gap-6 xl:gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_33__.Card, {\n                        className: \"lg:col-span-5 space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_28__.H2, {\n                                children: \"Crew\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                lineNumber: 2006,\n                                columnNumber: 21\n                            }, this),\n                            crew ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.Table, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                                            className: \"pl-2.5 text-left align-bottom standard:align-top\",\n                                                            children: \"Crew\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                            lineNumber: 2012,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                                            className: \"px-[5px] text-left align-bottom standard:align-top\",\n                                                            children: \"Duty\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                            lineNumber: 2015,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        bp.standard ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                punchInStatus !== \"Off\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                                                    className: \"px-[5px] text-right\",\n                                                                    children: punchInLabel || \"Sign In\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                    lineNumber: 2022,\n                                                                    columnNumber: 53\n                                                                }, this),\n                                                                punchOutStatus !== \"Off\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                                                    className: \"pl-[5px] pr-2.5 text-right\",\n                                                                    children: punchOutLabel || \"Sign Out\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                    lineNumber: 2029,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                                            className: \"text-wrap standard:text-nowrap pr-0 text-right\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    punchInStatus !== \"Off\" ? punchInLabel || \"Sign In\" : \"\",\n                                                                    \"/\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {\n                                                                        className: \"standard:hidden\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                        lineNumber: 2043,\n                                                                        columnNumber: 53\n                                                                    }, this),\n                                                                    punchOutStatus !== \"Off\" ? punchOutLabel || \"Sign Out\" : \"\"\n                                                                ]\n                                                            }, void 0, true)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                            lineNumber: 2036,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                    lineNumber: 2011,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                lineNumber: 2010,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableBody, {\n                                                children: crew.filter((member)=>+member.crewMemberID > 0 && member.archived === false).map((member)=>{\n                                                    var _member_crewMember_trainingStatus;\n                                                    // Check if member has multiple duties\n                                                    const hasMultipleDuties = member.duties && Array.isArray(member.duties) && member.duties.length > 1;\n                                                    // Get additional duties (if any)\n                                                    const additionalDuties = hasMultipleDuties ? member.duties.slice(1).filter((duty)=>{\n                                                        // Get the first duty's title\n                                                        const firstDutyTitle = member.duties[0].dutyPerformed && member.duties[0].dutyPerformed.title;\n                                                        // Get current duty's title\n                                                        const currentDutyTitle = duty.dutyPerformed && duty.dutyPerformed.title;\n                                                        // Only include duties with different titles\n                                                        return currentDutyTitle && firstDutyTitle !== currentDutyTitle;\n                                                    }) : [];\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                                                \"aria-disabled\": locked,\n                                                                className: \"group \".concat(hasMultipleDuties ? \"border-b-0\" : \"\"),\n                                                                onClick: (e)=>{\n                                                                    // Don't do anything if locked\n                                                                    if (locked) return;\n                                                                    // Prevent row click if the event originated from a button\n                                                                    if (e.target instanceof HTMLElement && (e.target.closest(\"button\") || e.target.closest('[role=\"button\"]'))) {\n                                                                        return;\n                                                                    }\n                                                                    handleEditManifest(member);\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-left\", additionalDuties.length > 0 && \" text-foreground\"),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__.Avatar, {\n                                                                                    size: \"sm\",\n                                                                                    variant: ((_member_crewMember_trainingStatus = member.crewMember.trainingStatus) === null || _member_crewMember_trainingStatus === void 0 ? void 0 : _member_crewMember_trainingStatus.label) !== \"Good\" ? \"destructive\" : \"success\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__.AvatarFallback, {\n                                                                                        children: (0,_components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__.getCrewInitials)(member.crewMember.firstName, member.crewMember.surname)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2150,\n                                                                                        columnNumber: 69\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                    lineNumber: 2139,\n                                                                                    columnNumber: 65\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"hidden leading-none sm:flex flex-col justify-center ml-2\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"flex gap-2.5 items-center\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"text-foreground\",\n                                                                                                children: [\n                                                                                                    member.crewMember.firstName,\n                                                                                                    \" \",\n                                                                                                    member.crewMember.surname\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                                lineNumber: 2163,\n                                                                                                columnNumber: 73\n                                                                                            }, this),\n                                                                                            member.workDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_25__.Popover, {\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_25__.PopoverTrigger, {\n                                                                                                        onClick: (e)=>{\n                                                                                                            e.stopPropagation();\n                                                                                                        },\n                                                                                                        className: \"p-0 text-muted-foreground\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_InfoIcon_lucide_react__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                                                            className: \"text-light-blue-vivid-900 fill-light-blue-vivid-50\",\n                                                                                                            size: 24\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                                            lineNumber: 2184,\n                                                                                                            columnNumber: 85\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                                        lineNumber: 2177,\n                                                                                                        columnNumber: 81\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_25__.PopoverContent, {\n                                                                                                        className: \"w-80\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"text-sm\",\n                                                                                                            children: member.workDetails\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                                            lineNumber: 2192,\n                                                                                                            columnNumber: 85\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                                        lineNumber: 2191,\n                                                                                                        columnNumber: 81\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                                lineNumber: 2176,\n                                                                                                columnNumber: 77\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2162,\n                                                                                        columnNumber: 69\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                    lineNumber: 2161,\n                                                                                    columnNumber: 65\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                            lineNumber: 2138,\n                                                                            columnNumber: 61\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                        lineNumber: 2131,\n                                                                        columnNumber: 57\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-left grid items-center\", additionalDuties.length > 0 && \"text-foreground\"),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"truncate\",\n                                                                            children: member.dutyPerformed && member.dutyPerformed.title ? member.dutyPerformed.title : \"Not assigned\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                            lineNumber: 2212,\n                                                                            columnNumber: 61\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                        lineNumber: 2205,\n                                                                        columnNumber: 57\n                                                                    }, this),\n                                                                    bp.standard ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-right\", additionalDuties.length > 0 && \"text-foreground\"),\n                                                                                children: punchInStatus !== \"Off\" && ((member === null || member === void 0 ? void 0 : member.punchIn) ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(member.punchIn) : \"Not Available\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                lineNumber: 2226,\n                                                                                columnNumber: 65\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-right phablet:pr-2.5 relaive\", additionalDuties.length > 0 && \"text-input\"),\n                                                                                children: punchOutStatus !== \"Off\" && (!member.punchOut ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                                                                    variant: \"text\",\n                                                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-fill-inherit font-normal h-fit\"),\n                                                                                    disabled: locked,\n                                                                                    onClick: (e)=>{\n                                                                                        e.stopPropagation();\n                                                                                        handleSignOutTime(member);\n                                                                                    },\n                                                                                    children: punchOutLabel || \"Sign Out\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                    lineNumber: 2252,\n                                                                                    columnNumber: 77\n                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"whitespace-nowrap h-8 flex items-center w-full justify-end\"),\n                                                                                    children: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(member.punchOut)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                    lineNumber: 2272,\n                                                                                    columnNumber: 77\n                                                                                }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                lineNumber: 2242,\n                                                                                columnNumber: 65\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-right relaive flex flex-col phablet:pr-2.5 justify-center items-end\", additionalDuties.length > 0 && \"text-input\"),\n                                                                            children: [\n                                                                                punchInStatus !== \"Off\" && ((member === null || member === void 0 ? void 0 : member.punchIn) ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(member.punchIn) : \"Not Available\"),\n                                                                                punchOutStatus !== \"Off\" && (!member.punchOut ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                                                                    variant: \"text\",\n                                                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-fill-inherit font-normal h-fit\"),\n                                                                                    disabled: locked,\n                                                                                    onClick: (e)=>{\n                                                                                        e.stopPropagation();\n                                                                                        handleSignOutTime(member);\n                                                                                    },\n                                                                                    children: punchOutLabel || \"Sign Out\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                    lineNumber: 2303,\n                                                                                    columnNumber: 77\n                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"whitespace-nowrap h-8 flex items-center w-full justify-end\"),\n                                                                                    children: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(member.punchOut)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                    lineNumber: 2323,\n                                                                                    columnNumber: 77\n                                                                                }, this))\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                            lineNumber: 2285,\n                                                                            columnNumber: 65\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                        lineNumber: 2284,\n                                                                        columnNumber: 61\n                                                                    }, this)\n                                                                ]\n                                                            }, member.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                lineNumber: 2106,\n                                                                columnNumber: 53\n                                                            }, this),\n                                                            additionalDuties.map((duty, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                                                    \"aria-disabled\": locked,\n                                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"group\", index === additionalDuties.length - 1 ? \"\" : \"border-b-0\"),\n                                                                    onClick: (e)=>{\n                                                                        // Don't do anything if locked\n                                                                        if (locked) return;\n                                                                        // Prevent row click if the event originated from a button\n                                                                        if (e.target instanceof HTMLElement && (e.target.closest(\"button\") || e.target.closest('[role=\"button\"]'))) {\n                                                                            return;\n                                                                        }\n                                                                        handleEditManifest(member);\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\" text-input tiny:px-1 phablet:px-2.5 z-[5] py-2 text-left relative\"),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex flex-col absolute -top-[42%] items-center w-8 h-full\",\n                                                                                children: [\n                                                                                    index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-full h-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2387,\n                                                                                        columnNumber: 77\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-[1px] flex-1 border-l border-dashed border-neutral-400\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2389,\n                                                                                        columnNumber: 73\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"size-[5px] rounded-full bg-background border border-neutral-400\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2390,\n                                                                                        columnNumber: 73\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                lineNumber: 2384,\n                                                                                columnNumber: 69\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                            lineNumber: 2380,\n                                                                            columnNumber: 65\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\" text-input grid items-center text-left\"),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"truncate\",\n                                                                                children: duty.dutyPerformed && duty.dutyPerformed.title ? duty.dutyPerformed.title : \"Not assigned\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                lineNumber: 2398,\n                                                                                columnNumber: 69\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                            lineNumber: 2394,\n                                                                            columnNumber: 65\n                                                                        }, this),\n                                                                        bp.standard ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\" text-input py-2 text-right\"),\n                                                                                    children: punchInStatus !== \"Off\" && ((duty === null || duty === void 0 ? void 0 : duty.punchIn) ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(duty.punchIn) : \"Not Available\")\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                    lineNumber: 2412,\n                                                                                    columnNumber: 73\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\" text-input phablet:pr-2.5 py-2 text-right\"),\n                                                                                    children: punchOutStatus !== \"Off\" && (!duty.punchOut ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                                                                        variant: \"text\",\n                                                                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-fill-inherit font-normal h-fit\"),\n                                                                                        disabled: locked,\n                                                                                        onClick: (e)=>{\n                                                                                            e.stopPropagation();\n                                                                                            handleSignOutTime(duty);\n                                                                                        },\n                                                                                        children: punchOutLabel || \"Sign Out\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2431,\n                                                                                        columnNumber: 85\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"whitespace-nowrap flex items-center w-full justify-end\"),\n                                                                                        children: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(duty.punchOut)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2451,\n                                                                                        columnNumber: 85\n                                                                                    }, this))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                    lineNumber: 2424,\n                                                                                    columnNumber: 73\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-right phablet:pr-2.5 relaive flex flex-col justify-end items-end\",\n                                                                                children: [\n                                                                                    punchInStatus !== \"Off\" && ((duty === null || duty === void 0 ? void 0 : duty.punchIn) ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(duty.punchIn) : \"Not Available\"),\n                                                                                    punchOutStatus !== \"Off\" && (!duty.punchOut ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                                                                        variant: \"text\",\n                                                                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-fill-inherit font-normal -fit\"),\n                                                                                        disabled: locked,\n                                                                                        onClick: (e)=>{\n                                                                                            e.stopPropagation();\n                                                                                            handleSignOutTime(duty);\n                                                                                        },\n                                                                                        children: punchOutLabel || \"Sign Out\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2475,\n                                                                                        columnNumber: 85\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"whitespace-nowrap flex items-center w-full justify-end\"),\n                                                                                        children: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(duty.punchOut)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2495,\n                                                                                        columnNumber: 85\n                                                                                    }, this))\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                lineNumber: 2464,\n                                                                                columnNumber: 73\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                            lineNumber: 2463,\n                                                                            columnNumber: 69\n                                                                        }, this)\n                                                                    ]\n                                                                }, \"duty-\".concat(duty.id, \"-\").concat(index), true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                    lineNumber: 2343,\n                                                                    columnNumber: 61\n                                                                }, this))\n                                                        ]\n                                                    }, \"crew-\".concat(member.id), true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                        lineNumber: 2103,\n                                                        columnNumber: 49\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                lineNumber: 2053,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                        lineNumber: 2009,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_35__.FormFooter, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_20__.Label, {\n                                                        className: \"mb-0 font-semibold\",\n                                                        children: \"Minimum crew:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                        lineNumber: 2518,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_30__.Badge, {\n                                                        variant: (0,_types__WEBPACK_IMPORTED_MODULE_32__.isVessel)(vessel) && crewCount() > ((_vessel_maxPOB = vessel === null || vessel === void 0 ? void 0 : vessel.maxPOB) !== null && _vessel_maxPOB !== void 0 ? _vessel_maxPOB : 0) ? \"destructive\" : \"success\",\n                                                        className: \"rounded-full flex items-center justify-center size-[25px]\",\n                                                        children: (0,_types__WEBPACK_IMPORTED_MODULE_32__.isVessel)(vessel) ? vessel.minCrew : 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                        lineNumber: 2521,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    (0,_types__WEBPACK_IMPORTED_MODULE_32__.isVessel)(vessel) && crewCount() > ((_vessel_maxPOB1 = vessel === null || vessel === void 0 ? void 0 : vessel.maxPOB) !== null && _vessel_maxPOB1 !== void 0 ? _vessel_maxPOB1 : 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                        className: \"text-destructive\",\n                                                        children: \"You have more people on board than your vessel is configured to carry\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                        lineNumber: 2533,\n                                                        columnNumber: 45\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                lineNumber: 2517,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                                className: \"w-full tiny:w-fit px-2.5\",\n                                                disabled: locked,\n                                                onClick: handleAddManifest,\n                                                children: \"Add crew\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                lineNumber: 2541,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                        lineNumber: 2516,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                lineNumber: 2008,\n                                columnNumber: 25\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                    disabled: locked,\n                                    onClick: handleAddManifest,\n                                    children: \"Add crew members to this trip\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2551,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                lineNumber: 2550,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                        lineNumber: 2005,\n                        columnNumber: 17\n                    }, this),\n                    crew && logBookConfig && (logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.find((config)=>config.title === \"Crew Welfare\" && config.active === true)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_33__.Card, {\n                        className: \"lg:col-span-3 space-y-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_daily_checks_crew_welfare__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            offline: offline,\n                            logBookConfig: logBookConfig,\n                            locked: locked || !edit_logBookEntry,\n                            crewWelfareCheck: crewWelfareCheck,\n                            updateCrewWelfare: updateCrewWelfare\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                            lineNumber: 2568,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                        lineNumber: 2567,\n                        columnNumber: 25\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                lineNumber: 2004,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_27__.AlertDialogNew, {\n                openDialog: openAddCrewMemberDialog,\n                setOpenDialog: setopenAddCrewMemberDialog,\n                handleCreate: handleSave,\n                handleCancel: handleCancel,\n                handleDestructiveAction: crewManifestEntry.id > 0 ? ()=>setOpenConfirmCrewDeleteDialog(true) : undefined,\n                showDestructiveAction: crewManifestEntry.id > 0,\n                destructiveActionText: \"Delete\",\n                title: crewManifestEntry.id > 0 ? \"Update crew member\" : \"Add crew member\",\n                actionText: crewManifestEntry.id > 0 ? \"Update\" : \"Add\",\n                cancelText: \"Cancel\",\n                contentClassName: \"max-w-2xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-top gap-4 mb-4\",\n                            children: [\n                                crewMember && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__.Avatar, {\n                                    variant: ((_crewMember_data = crewMember.data) === null || _crewMember_data === void 0 ? void 0 : (_crewMember_data_trainingStatus = _crewMember_data.trainingStatus) === null || _crewMember_data_trainingStatus === void 0 ? void 0 : _crewMember_data_trainingStatus.label) !== \"Good\" ? \"destructive\" : \"success\",\n                                    className: \"size-12 border-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__.AvatarImage, {\n                                            src: (_crewMember_profile = crewMember.profile) === null || _crewMember_profile === void 0 ? void 0 : _crewMember_profile.avatar,\n                                            alt: \"\".concat(((_crewMember_profile1 = crewMember.profile) === null || _crewMember_profile1 === void 0 ? void 0 : _crewMember_profile1.firstName) || \"\", \" \").concat(((_crewMember_profile2 = crewMember.profile) === null || _crewMember_profile2 === void 0 ? void 0 : _crewMember_profile2.surname) || \"\").trim()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                            lineNumber: 2610,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__.AvatarFallback, {\n                                            children: (0,_components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__.getCrewInitials)(((_crewMember_profile3 = crewMember.profile) === null || _crewMember_profile3 === void 0 ? void 0 : _crewMember_profile3.firstName) || ((_crewMember_data1 = crewMember.data) === null || _crewMember_data1 === void 0 ? void 0 : _crewMember_data1.firstName), ((_crewMember_profile4 = crewMember.profile) === null || _crewMember_profile4 === void 0 ? void 0 : _crewMember_profile4.surname) || ((_crewMember_data2 = crewMember.data) === null || _crewMember_data2 === void 0 ? void 0 : _crewMember_data2.surname))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                            lineNumber: 2614,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2602,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_28__.H3, {\n                                            className: \"text-lg\",\n                                            children: (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_profile5 = crewMember.profile) === null || _crewMember_profile5 === void 0 ? void 0 : _crewMember_profile5.firstName) || (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data3 = crewMember.data) === null || _crewMember_data3 === void 0 ? void 0 : _crewMember_data3.firstName) || (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_profile6 = crewMember.profile) === null || _crewMember_profile6 === void 0 ? void 0 : _crewMember_profile6.surname) || (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data4 = crewMember.data) === null || _crewMember_data4 === void 0 ? void 0 : _crewMember_data4.surname) ? \"\".concat(((_crewMember_profile7 = crewMember.profile) === null || _crewMember_profile7 === void 0 ? void 0 : _crewMember_profile7.firstName) || ((_crewMember_data5 = crewMember.data) === null || _crewMember_data5 === void 0 ? void 0 : _crewMember_data5.firstName) || \"\", \" \").concat(((_crewMember_profile8 = crewMember.profile) === null || _crewMember_profile8 === void 0 ? void 0 : _crewMember_profile8.surname) || ((_crewMember_data6 = crewMember.data) === null || _crewMember_data6 === void 0 ? void 0 : _crewMember_data6.surname) || \"\").trim() : \"\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                            lineNumber: 2625,\n                                            columnNumber: 29\n                                        }, this),\n                                        crewMember && ((crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data7 = crewMember.data) === null || _crewMember_data7 === void 0 ? void 0 : (_crewMember_data_trainingStatus1 = _crewMember_data7.trainingStatus) === null || _crewMember_data_trainingStatus1 === void 0 ? void 0 : _crewMember_data_trainingStatus1.label) !== \"Good\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-destructive\",\n                                                    children: \"Training is overdue\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                    lineNumber: 2637,\n                                                    columnNumber: 41\n                                                }, this),\n                                                (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data8 = crewMember.data) === null || _crewMember_data8 === void 0 ? void 0 : (_crewMember_data_trainingStatus2 = _crewMember_data8.trainingStatus) === null || _crewMember_data_trainingStatus2 === void 0 ? void 0 : _crewMember_data_trainingStatus2.dues) && crewMember.data.trainingStatus.dues.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-sm text-destructive\",\n                                                    children: crewMember.data.trainingStatus.dues.map((due, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-destructive text-lg\",\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                    lineNumber: 2653,\n                                                                    columnNumber: 65\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        due.trainingType.title,\n                                                                        \" \",\n                                                                        \"-\",\n                                                                        \" \",\n                                                                        due.status.label\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                    lineNumber: 2656,\n                                                                    columnNumber: 65\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                            lineNumber: 2650,\n                                                            columnNumber: 61\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                    lineNumber: 2644,\n                                                    columnNumber: 49\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                            lineNumber: 2636,\n                                            columnNumber: 37\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-bright-turquoise-600\",\n                                            children: \"Training up to date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                            lineNumber: 2676,\n                                            columnNumber: 37\n                                        }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2624,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                            lineNumber: 2600,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 sm:grid-cols-2 gap-[31px]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_23__.Combobox, {\n                                    label: \"Crew member\",\n                                    modal: true,\n                                    buttonClassName: \"w-full\",\n                                    options: crewMemberOptions.map((option)=>({\n                                            ...option,\n                                            value: String(option.value)\n                                        })),\n                                    value: crewMember,\n                                    onChange: handleCrewMember\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2683,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_crew_duty_dropdown__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                    label: \"Primary duty\",\n                                    crewDutyID: Number(duty === null || duty === void 0 ? void 0 : duty.value) || 0,\n                                    onChange: handleDuty,\n                                    multi: false,\n                                    modal: true,\n                                    offline: offline,\n                                    hideCreateOption: false\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2696,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                            lineNumber: 2682,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 xs:grid-cols-2 pr-px gap-[31px]\",\n                            children: [\n                                punchInStatus !== \"Off\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DateRange__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                    id: \"signin-date\",\n                                    modal: true,\n                                    value: loginTime,\n                                    onChange: handleLogin,\n                                    label: punchInLabel || \"Sign In\",\n                                    dateFormat: \"dd MMM,\",\n                                    placeholder: \"\".concat(punchInLabel || \"Sign In\", \" Time\"),\n                                    mode: \"single\",\n                                    type: \"datetime\",\n                                    closeOnSelect: false,\n                                    icon: _barrel_optimize_names_Clock_InfoIcon_lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"],\n                                    className: \"w-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2709,\n                                    columnNumber: 29\n                                }, this),\n                                punchOutStatus !== \"Off\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DateRange__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                    id: \"signout-date\",\n                                    modal: true,\n                                    value: logoutTime || undefined,\n                                    onChange: handleLogout,\n                                    label: punchOutLabel || \"Sign Out\",\n                                    placeholder: \"\".concat(punchOutLabel || \"Sign Out\", \" Time\"),\n                                    mode: \"single\",\n                                    type: \"datetime\" // Keep datetime to include time picker\n                                    ,\n                                    dateFormat: \"dd MMM,\",\n                                    timeFormat: \"HH:mm\" // Explicitly set time format\n                                    ,\n                                    closeOnSelect: false,\n                                    clearable: true,\n                                    icon: _barrel_optimize_names_Clock_InfoIcon_lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"],\n                                    className: \"w-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2726,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                            lineNumber: 2707,\n                            columnNumber: 21\n                        }, this),\n                        workDetailsStatus !== \"Off\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_20__.Label, {\n                            htmlFor: \"work-details\",\n                            label: workDetailsLabel || \"Work Details\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_21__.Textarea, {\n                                id: \"work-details\",\n                                rows: 4,\n                                className: \"w-full resize-none\",\n                                placeholder: \"Enter work details\",\n                                defaultValue: crewManifestEntry === null || crewManifestEntry === void 0 ? void 0 : crewManifestEntry.workDetails\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                lineNumber: 2749,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                            lineNumber: 2746,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                    lineNumber: 2599,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                lineNumber: 2579,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_27__.AlertDialogNew, {\n                openDialog: openEditLogoutTimeDialog,\n                setOpenDialog: setOpenEditLogoutTimeDialog,\n                handleCreate: ()=>handleSave(\"update\"),\n                handleCancel: handleCancel,\n                actionText: (0,_utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_36__.getResponsiveLabel)(bp.phablet, \"Update\", \"Update Time\"),\n                cancelText: \"Cancel\",\n                contentClassName: \"top-[38svh]\",\n                size: \"sm\",\n                title: \"Update sign out time\",\n                className: \"space-y-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full relative\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DateRange__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                        modal: true,\n                        id: \"signout-date\",\n                        name: \"signout-date\",\n                        label: \"\".concat(punchOutLabel || \"Sign Out\", \" Time\"),\n                        value: logoutTime || undefined,\n                        mode: \"single\",\n                        type: \"datetime\" // Keep datetime to include time picker\n                        ,\n                        onChange: handleLogout,\n                        dateFormat: \"dd MMM,\",\n                        timeFormat: \"HH:mm\" // Explicitly set time format\n                        ,\n                        placeholder: \"\".concat(punchOutLabel || \"Sign Out\", \" Time\"),\n                        closeOnSelect: false,\n                        clearable: true,\n                        icon: _barrel_optimize_names_Clock_InfoIcon_lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"],\n                        className: \"w-full\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                        lineNumber: 2777,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                    lineNumber: 2776,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                lineNumber: 2761,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_27__.AlertDialogNew, {\n                openDialog: openCrewTrainingDueDialog,\n                setOpenDialog: setOpenCrewTrainingDueDialog,\n                contentClassName: \"max-w-xl\",\n                className: \"space-y-4\",\n                cancelText: \"Cancel\",\n                actionText: \"Yes, Continue\",\n                handleCreate: ()=>setOpenCrewTrainingDueDialog(false),\n                handleCancel: ()=>{\n                    setOpenCrewTrainingDueDialog(false);\n                    setCrewMember(null);\n                    setDuty(null);\n                },\n                title: \"Crew member training status\",\n                variant: \"warning\",\n                showIcon: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data9 = crewMember.data) === null || _crewMember_data9 === void 0 ? void 0 : _crewMember_data9.firstName) || (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data10 = crewMember.data) === null || _crewMember_data10 === void 0 ? void 0 : _crewMember_data10.surname) ? \"\".concat(crewMember.data.firstName || \"\", \" \").concat(crewMember.data.surname || \"\").trim() : \"This crew member\",\n                                \" \",\n                                \"has overdue training sessions on this vessel. These sessions are:\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                            lineNumber: 2814,\n                            columnNumber: 21\n                        }, this),\n                        crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data11 = crewMember.data) === null || _crewMember_data11 === void 0 ? void 0 : (_crewMember_data_trainingStatus3 = _crewMember_data11.trainingStatus) === null || _crewMember_data_trainingStatus3 === void 0 ? void 0 : (_crewMember_data_trainingStatus_dues = _crewMember_data_trainingStatus3.dues) === null || _crewMember_data_trainingStatus_dues === void 0 ? void 0 : _crewMember_data_trainingStatus_dues.map((item, dueIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"\".concat(item.trainingType.title, \" - \").concat(item.status.label)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2826,\n                                    columnNumber: 33\n                                }, this)\n                            }, dueIndex, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                lineNumber: 2825,\n                                columnNumber: 29\n                            }, this)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Do you still want to add this crew member to this vessel?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                            lineNumber: 2833,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                    lineNumber: 2813,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                lineNumber: 2797,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_27__.AlertDialogNew, {\n                openDialog: openConfirmCrewDeleteDialog,\n                setOpenDialog: setOpenConfirmCrewDeleteDialog,\n                handleCreate: handleArchive,\n                handleCancel: ()=>{\n                    setOpenConfirmCrewDeleteDialog(false);\n                // Don't reset crew member here as it's needed for the parent dialog\n                },\n                actionText: \"Remove\",\n                cancelText: \"Cancel\",\n                contentClassName: \"max-w-md\",\n                variant: \"warning\",\n                showIcon: true,\n                title: \"Remove crew member\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm\",\n                    children: [\n                        \"Are you sure you want to remove\",\n                        \" \",\n                        (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data12 = crewMember.data) === null || _crewMember_data12 === void 0 ? void 0 : _crewMember_data12.firstName) || (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data13 = crewMember.data) === null || _crewMember_data13 === void 0 ? void 0 : _crewMember_data13.surname) ? \"\".concat(crewMember.data.firstName || \"\", \" \").concat(crewMember.data.surname || \"\").trim() : \"this crew member\",\n                        \" \",\n                        \"from this trip manifest?\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                    lineNumber: 2854,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                lineNumber: 2840,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Crew, \"gN5juuh1ndxpEhNnlqFFWEx5KI0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams,\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_34__.useBreakpoints,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation\n    ];\n});\n_c = Crew;\nvar _c;\n$RefreshReg$(_c, \"Crew\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew/crew.tsx\n"));

/***/ })

});