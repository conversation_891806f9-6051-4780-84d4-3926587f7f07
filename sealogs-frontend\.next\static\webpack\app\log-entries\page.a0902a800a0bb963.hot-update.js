"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/log-entry-main-content/log-entry-main-content.tsx":
/*!******************************************************************************!*\
  !*** ./src/app/ui/logbook/log-entry-main-content/log-entry-main-content.tsx ***!
  \******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LogEntryMainContent; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _engine_checks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../engine-checks */ \"(app-pages-browser)/./src/app/ui/logbook/engine-checks.tsx\");\n/* harmony import */ var _comprehensive_engine_logs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../comprehensive-engine-logs */ \"(app-pages-browser)/./src/app/ui/logbook/comprehensive-engine-logs.tsx\");\n/* harmony import */ var _crew_supernumerary__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../crew/supernumerary */ \"(app-pages-browser)/./src/app/ui/crew/supernumerary.tsx\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _crew_crew__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../crew/crew */ \"(app-pages-browser)/./src/app/ui/crew/crew.tsx\");\n/* harmony import */ var _daily_checks_checks__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../daily-checks/checks */ \"(app-pages-browser)/./src/app/ui/daily-checks/checks.tsx\");\n/* harmony import */ var _weather__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../weather */ \"(app-pages-browser)/./src/app/ui/logbook/weather.tsx\");\n/* harmony import */ var _trip_log__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../trip-log */ \"(app-pages-browser)/./src/app/ui/logbook/trip-log.tsx\");\n/* harmony import */ var _sign_off__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../sign-off */ \"(app-pages-browser)/./src/app/ui/logbook/sign-off.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.30_@ba_ed8daac48216b87d589b3ebdbcc06997/node_modules/nuqs/dist/index.js\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/logbook/log-entry-main-content/queries.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/models/vessel */ \"(app-pages-browser)/./src/app/offline/models/vessel.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction LogEntryMainContent(param) {\n    let { logentryID, offline, vessel, logbook, client, logBookConfig, loaded, locked, fuel, updateFuel, edit_logBookEntry, logEntrySections, supernumerary, setSupernumerary, crewMembers, setCrewMembers, crew, crewWelfare, updateCrewWelfare, crewMembersList, vesselDailyCheck, setVesselDailyCheck, masterID, tripReport, updateTripReport, fuelLogs, signOff, updateSignOff, prevComments, setPrevComments, logBookStartDate } = param;\n    _s();\n    const isLargeScreen = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_15__.useMediaQuery)(\"(min-width: 1280px)\");\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useSearchParams)();\n    const [imCrew, setImCrew] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [createdTab, setCreatedTab] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [currentTrip, setCurrentTrip] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const vesselModel = new _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_14__[\"default\"]();\n    // Use nuqs to manage the tab state through URL query parameters\n    const [tab, setTab] = (0,nuqs__WEBPACK_IMPORTED_MODULE_16__.useQueryState)(\"tab\", {\n        defaultValue: \"crew\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        var _searchParams_get;\n        const firstTab = (_searchParams_get = searchParams.get(\"firstTab\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n        const commentTab = firstTab + \"\";\n        if (firstTab != 0 && commentTab != \"crew\" && commentTab !== tab) {\n            setTab(commentTab);\n        }\n    }, [\n        searchParams,\n        tab\n    ]);\n    const sortedCrew = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        var _logbook_vehicle_seaLogsMembers, _logbook_vehicle;\n        var _logbook_vehicle_seaLogsMembers_nodes_filter, _crew_filter;\n        return [\n            ...(_logbook_vehicle_seaLogsMembers_nodes_filter = logbook === null || logbook === void 0 ? void 0 : (_logbook_vehicle = logbook.vehicle) === null || _logbook_vehicle === void 0 ? void 0 : (_logbook_vehicle_seaLogsMembers = _logbook_vehicle.seaLogsMembers) === null || _logbook_vehicle_seaLogsMembers === void 0 ? void 0 : _logbook_vehicle_seaLogsMembers.nodes.filter((vcrew)=>!vcrew.archived)) !== null && _logbook_vehicle_seaLogsMembers_nodes_filter !== void 0 ? _logbook_vehicle_seaLogsMembers_nodes_filter : [],\n            ...(_crew_filter = crew === null || crew === void 0 ? void 0 : crew.filter((crew)=>{\n                var _logbook_vehicle_seaLogsMembers, _logbook_vehicle;\n                return !(logbook === null || logbook === void 0 ? void 0 : (_logbook_vehicle = logbook.vehicle) === null || _logbook_vehicle === void 0 ? void 0 : (_logbook_vehicle_seaLogsMembers = _logbook_vehicle.seaLogsMembers) === null || _logbook_vehicle_seaLogsMembers === void 0 ? void 0 : _logbook_vehicle_seaLogsMembers.nodes.filter((vcrew)=>!vcrew.archived).map((vcrew)=>vcrew.id).includes(crew.id));\n            })) !== null && _crew_filter !== void 0 ? _crew_filter : []\n        ];\n    }, [\n        logbook,\n        crew\n    ]);\n    const displayWeatherField = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents;\n        const weather = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.filter((node)=>node.componentClass === \"Weather_LogBookComponent\");\n        if ((weather === null || weather === void 0 ? void 0 : weather.length) > 0) {\n            var _weather_;\n            if ((_weather_ = weather[0]) === null || _weather_ === void 0 ? void 0 : _weather_.active) {\n                return true;\n            }\n        }\n        return false;\n    }, [\n        logBookConfig\n    ]);\n    const [queryVessels] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_13__.ReadVessels, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (queryVesselResponse)=>{\n            if (queryVesselResponse.readVessels.nodes) {\n                setVessels(queryVesselResponse.readVessels.nodes);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVessels error\", error);\n        }\n    });\n    const loadVessels = async (offline)=>{\n        if (offline) {\n            const response = await vesselModel.getAll();\n            setVessels(response);\n        } else {\n            await queryVessels();\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (isLoading) {\n            setImCrew((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_6__.isCrew)() || false);\n            loadVessels(offline);\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    var _client_masterTerm;\n    // getVesselList(setVessels, offline)\n    // Define tabs as a JSON object for easier management\n    const tabItems = [\n        {\n            id: \"crew\",\n            label: \"Crew\",\n            component: crew && loaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_crew__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                offline: offline,\n                crewSections: crewMembers,\n                allCrew: sortedCrew,\n                logBookEntryID: logentryID,\n                locked: locked,\n                logBookConfig: logBookConfig,\n                setCrewMembers: setCrewMembers,\n                crewWelfareCheck: crewWelfare,\n                updateCrewWelfare: updateCrewWelfare,\n                vessel: vessel,\n                masterID: masterID,\n                logEntrySections: logEntrySections,\n                crewMembersList: crewMembersList\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry-main-content\\\\log-entry-main-content.tsx\",\n                lineNumber: 172,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            id: \"pre-departure-checks\",\n            label: \"Pre-Departure Checks\",\n            component: logBookConfig && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_daily_checks_checks__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                offline: offline,\n                vesselDailyCheck: vesselDailyCheck,\n                logBookConfig: logBookConfig,\n                setVesselDailyCheck: setVesselDailyCheck,\n                locked: locked,\n                edit_logBookEntry: edit_logBookEntry\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry-main-content\\\\log-entry-main-content.tsx\",\n                lineNumber: 193,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            id: \"weather\",\n            label: \"Weather\",\n            component: displayWeatherField && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_weather__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                offline: offline,\n                logBookConfig: logBookConfig,\n                logbook: logbook\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry-main-content\\\\log-entry-main-content.tsx\",\n                lineNumber: 207,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            id: \"trip-log\",\n            label: \"Trip Log\",\n            component: logBookConfig && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_trip_log__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                offline: offline,\n                tripReport: tripReport,\n                logBookConfig: logBookConfig,\n                updateTripReport: updateTripReport,\n                locked: locked || !edit_logBookEntry,\n                crewMembers: crewMembers,\n                masterID: masterID,\n                createdTab: createdTab,\n                setCreatedTab: setCreatedTab,\n                currentTrip: currentTrip,\n                setCurrentTrip: setCurrentTrip,\n                vessels: vessels,\n                fuelLogs: fuelLogs,\n                logBookStartDate: logBookStartDate\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry-main-content\\\\log-entry-main-content.tsx\",\n                lineNumber: 218,\n                columnNumber: 17\n            }, this)\n        },\n        {\n            id: \"complete-logbook\",\n            label: \"Complete log entry\",\n            component: logBookConfig && !imCrew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sign_off__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                offline: offline,\n                logBookConfig: logBookConfig,\n                updateTripReport: updateTripReport,\n                signOff: signOff ? signOff[0] : false,\n                updateSignOff: updateSignOff,\n                fuel: fuel,\n                locked: locked || !edit_logBookEntry,\n                crewMembers: crewMembers,\n                vessel: vessel,\n                logBook: logbook,\n                masterTerm: (_client_masterTerm = client === null || client === void 0 ? void 0 : client.masterTerm) !== null && _client_masterTerm !== void 0 ? _client_masterTerm : \"Master\",\n                prevComments: prevComments,\n                onUpdatePrevComments: (coms)=>{\n                    setPrevComments(coms);\n                },\n                screen: isLargeScreen ? \"Desktop\" : \"Mobile\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry-main-content\\\\log-entry-main-content.tsx\",\n                lineNumber: 240,\n                columnNumber: 17\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.Tabs, {\n                value: tab || \"crew\",\n                className: \"w-full grid\",\n                onValueChange: (value)=>{\n                    if (value !== tab) {\n                        setTab(value);\n                    }\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsList, {\n                        className: \" md:block hidden\",\n                        children: tabItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                value: item.id,\n                                children: item.label\n                            }, item.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry-main-content\\\\log-entry-main-content.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 25\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry-main-content\\\\log-entry-main-content.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 17\n                    }, this),\n                    tabItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                            value: item.id,\n                            children: item.component\n                        }, item.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry-main-content\\\\log-entry-main-content.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 21\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry-main-content\\\\log-entry-main-content.tsx\",\n                lineNumber: 264,\n                columnNumber: 13\n            }, this),\n            tab === \"engineLog\" && loaded && logbook && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:block\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_engine_checks__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    fuel: fuel,\n                    updateFuel: updateFuel,\n                    locked: locked || !edit_logBookEntry,\n                    logEntrySections: logEntrySections,\n                    logBookEntryID: logentryID\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry-main-content\\\\log-entry-main-content.tsx\",\n                    lineNumber: 289,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry-main-content\\\\log-entry-main-content.tsx\",\n                lineNumber: 288,\n                columnNumber: 17\n            }, this),\n            tab === \"compengineLog\" && loaded && logbook && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_comprehensive_engine_logs__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    logbookSection: logbook.logBookEntrySections.nodes\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry-main-content\\\\log-entry-main-content.tsx\",\n                    lineNumber: 300,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry-main-content\\\\log-entry-main-content.tsx\",\n                lineNumber: 299,\n                columnNumber: 17\n            }, this),\n            tab === \"supernumerary\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:block\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_supernumerary__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    logBookConfig: logBookConfig,\n                    supernumerary: supernumerary,\n                    setSupernumerary: setSupernumerary,\n                    locked: locked || !edit_logBookEntry\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry-main-content\\\\log-entry-main-content.tsx\",\n                    lineNumber: 307,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry-main-content\\\\log-entry-main-content.tsx\",\n                lineNumber: 306,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-20\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry-main-content\\\\log-entry-main-content.tsx\",\n                lineNumber: 315,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(LogEntryMainContent, \"gdwzcIenfnXMawwK1wTEozRgr2U=\", false, function() {\n    return [\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_15__.useMediaQuery,\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useSearchParams,\n        nuqs__WEBPACK_IMPORTED_MODULE_16__.useQueryState,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery\n    ];\n});\n_c = LogEntryMainContent;\nvar _c;\n$RefreshReg$(_c, \"LogEntryMainContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/log-entry-main-content/log-entry-main-content.tsx\n"));

/***/ })

});