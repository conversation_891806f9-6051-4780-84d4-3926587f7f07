"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/vessel/info/page",{

/***/ "(app-pages-browser)/./src/app/ui/vessels/view.tsx":
/*!*************************************!*\
  !*** ./src/app/ui/vessels/view.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ VesselsView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _crew_multiselect_dropdown_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../crew/multiselect-dropdown/multiselect-dropdown */ \"(app-pages-browser)/./src/app/ui/crew/multiselect-dropdown/multiselect-dropdown.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_hooks_use_mobile__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/hooks/use-mobile */ \"(app-pages-browser)/./src/components/hooks/use-mobile.tsx\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_helpers_vesselHelper__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/helpers/vesselHelper */ \"(app-pages-browser)/./src/app/helpers/vesselHelper.ts\");\n/* harmony import */ var _components_header_image__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./components/header-image */ \"(app-pages-browser)/./src/app/ui/vessels/components/header-image.tsx\");\n/* harmony import */ var _components_logbook_entries_card__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./components/logbook-entries-card */ \"(app-pages-browser)/./src/app/ui/vessels/components/logbook-entries-card.tsx\");\n/* harmony import */ var _components_maintenance_card__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./components/maintenance-card */ \"(app-pages-browser)/./src/app/ui/vessels/components/maintenance-card.tsx\");\n/* harmony import */ var _components_training_drills_card__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./components/training-drills-card */ \"(app-pages-browser)/./src/app/ui/vessels/components/training-drills-card.tsx\");\n/* harmony import */ var _components_crew_card__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./components/crew-card */ \"(app-pages-browser)/./src/app/ui/vessels/components/crew-card.tsx\");\n/* harmony import */ var _components_inventory_card__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./components/inventory-card */ \"(app-pages-browser)/./src/app/ui/vessels/components/inventory-card.tsx\");\n/* harmony import */ var _components_tabs_holder__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./components/tabs-holder */ \"(app-pages-browser)/./src/app/ui/vessels/components/tabs-holder.tsx\");\n/* harmony import */ var _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/app/offline/models/logBookEntry */ \"(app-pages-browser)/./src/app/offline/models/logBookEntry.js\");\n/* harmony import */ var _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/app/offline/models/vessel */ \"(app-pages-browser)/./src/app/offline/models/vessel.js\");\n/* harmony import */ var _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/app/offline/models/seaLogsMember */ \"(app-pages-browser)/./src/app/offline/models/seaLogsMember.js\");\n/* harmony import */ var _app_helpers_trainingHelper__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/app/helpers/trainingHelper */ \"(app-pages-browser)/./src/app/helpers/trainingHelper.ts\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_footer_wrapper__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/components/footer-wrapper */ \"(app-pages-browser)/./src/components/footer-wrapper.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_DateRange__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @/components/DateRange */ \"(app-pages-browser)/./src/components/DateRange.tsx\");\n/* harmony import */ var _editor__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ../editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.30_@ba_ed8daac48216b87d589b3ebdbcc06997/node_modules/nuqs/dist/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst vesselStatuses = [\n    {\n        label: \"On Voyage\",\n        value: \"OnVoyage\"\n    },\n    {\n        label: \"Ready For Voyage\",\n        value: \"AvailableForVoyage\"\n    },\n    {\n        label: \"Out Of Service\",\n        value: \"OutOfService\"\n    }\n];\nconst vesselStatusReason = [\n    {\n        label: \"Crew Unavailable\",\n        value: \"CrewUnavailable\"\n    },\n    {\n        label: \"Skipper/Master Unavailable\",\n        value: \"MasterUnavailable\"\n    },\n    {\n        label: \"Planned Maintenance\",\n        value: \"PlannedMaintenance\"\n    },\n    {\n        label: \"Breakdown\",\n        value: \"Breakdown\"\n    },\n    {\n        label: \"Other\",\n        value: \"Other\"\n    }\n];\nfunction VesselsView(param) {\n    let { vesselId, tab } = param;\n    var _vessel_statusHistory_nodes, _vessel_statusHistory, _vessel_departments;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams)();\n    const [vessel, setVessel] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)();\n    const [logbooks, setLogbooks] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [currentLogEntryAction, setCurrentLogEntryAction] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)();\n    const [currentLogEntry, setCurrentLogEntry] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)();\n    const [totalEntries, setTotalEntries] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [maintenanceTasks, setMaintenanceTasks] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [taskCounter, setTaskCounter] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [trainingSessions, setTrainingSessions] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)();\n    const [trainingSessionDues, setTrainingSessionDues] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [trainingSessionDuesSummary, setTrainingSessionDuesSummary] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [crewInfo, setCrewInfo] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)();\n    const [taskCrewInfo, setTaskCrewInfo] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)();\n    const [inventories, setInventories] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [engineList, setEngineList] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)();\n    const [fuelTankList, setFuelTankList] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)();\n    const [waterTankList, setWaterTankList] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)();\n    const [sewageSystemList, setSewageSystemList] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)();\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const [vesselTab, setVesselTab] = (0,nuqs__WEBPACK_IMPORTED_MODULE_31__.useQueryState)(\"tab\", {\n        defaultValue: \"logEntries\"\n    });\n    const perPage = 10;\n    const [displayAddCrew, setDisplayAddCrew] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [vesselCrewIDs, setVesselCrewIDs] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const [bannerImage, setBannerImage] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [isNewLogEntryDisabled, setIsNewLogEntryDisabled] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(true);\n    const [imCrew, setImCrew] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [edit_logBookEntry, setEdit_logBookEntry] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [edit_task, setEdit_task] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [edit_docs, setEdit_docs] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [delete_docs, setDelete_docs] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [trainingsDueCount, setTrainingsDueCount] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const [displayEditStatus, setDisplayEditStatus] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [vesselStatus, setVesselStatus] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]);\n    const isMobile = (0,_components_hooks_use_mobile__WEBPACK_IMPORTED_MODULE_12__.useIsMobile)();\n    const tabsRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\n    const lbeModel = new _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_22__[\"default\"]();\n    const vesselModel = new _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_23__[\"default\"]();\n    const seaLogsMemberModel = new _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_24__[\"default\"]();\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.hasPermission)(\"ADD_LOGBOOKENTRY\", permissions) || (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.hasPermission)(\"EDIT_LOGBOOKENTRY\", permissions)) {\n                setEdit_logBookEntry(true);\n            } else {\n                setEdit_logBookEntry(false);\n            }\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.hasPermission)(\"EDIT_TASK\", permissions)) {\n                setEdit_task(true);\n            } else {\n                setEdit_task(false);\n            }\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.hasPermission)(\"EDIT_VESSEL_DOCUMENT\", permissions)) {\n                setEdit_docs(true);\n            } else {\n                setEdit_docs(false);\n            }\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.hasPermission)(\"DELETE_VESSEL_DOCUMENT\", permissions)) {\n                setDelete_docs(true);\n            } else {\n                setDelete_docs(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        var _vessel_statusHistory, _vessel_statusHistory_nodes, _vessel_statusHistory1;\n        setVesselStatus(vessel === null || vessel === void 0 ? void 0 : (_vessel_statusHistory = vessel.statusHistory) === null || _vessel_statusHistory === void 0 ? void 0 : _vessel_statusHistory.nodes[0]);\n        if ((vessel === null || vessel === void 0 ? void 0 : (_vessel_statusHistory1 = vessel.statusHistory) === null || _vessel_statusHistory1 === void 0 ? void 0 : (_vessel_statusHistory_nodes = _vessel_statusHistory1.nodes) === null || _vessel_statusHistory_nodes === void 0 ? void 0 : _vessel_statusHistory_nodes.length) === 0) {\n            createVesselStatus({\n                variables: {\n                    input: {\n                        date: dayjs__WEBPACK_IMPORTED_MODULE_9___default()().format(\"YYYY-MM-DD\"),\n                        vesselID: +(vessel === null || vessel === void 0 ? void 0 : vessel.id)\n                    }\n                }\n            });\n        }\n    }, [\n        vessel\n    ]);\n    const handleUpdateVesselStatus = ()=>{\n        {\n            (vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.status) === \"OutOfService\" ? createVesselStatus({\n                variables: {\n                    input: {\n                        vesselID: vessel === null || vessel === void 0 ? void 0 : vessel.id,\n                        date: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.date,\n                        status: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.status,\n                        comment: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.comment,\n                        reason: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.reason,\n                        otherReason: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.otherReason,\n                        expectedReturn: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.expectedReturn\n                    }\n                }\n            }) : createVesselStatus({\n                variables: {\n                    input: {\n                        vesselID: vessel === null || vessel === void 0 ? void 0 : vessel.id,\n                        date: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.date,\n                        status: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.status\n                    }\n                }\n            });\n        }\n    };\n    const handleVesselStatusDate = (date)=>{\n        setVesselStatus({\n            ...vesselStatus,\n            date: date\n        });\n    };\n    const handleVesselStatusReturnDate = (date)=>{\n        setVesselStatus({\n            ...vesselStatus,\n            expectedReturn: date\n        });\n    };\n    const [createVesselStatus] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_32__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CREATE_VESSELSTATUS, {\n        onCompleted: (response)=>{\n            const data = response.createLogBookEntry;\n            setVesselStatus({\n                ...vesselStatus,\n                vesselID: vessel === null || vessel === void 0 ? void 0 : vessel.id,\n                date: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.date,\n                status: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.status,\n                comment: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.comment,\n                reason: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.reason,\n                otherReason: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.otherReason,\n                expectedReturn: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.expectedReturn\n            });\n            setDisplayEditStatus(false);\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(error.message);\n        }\n    });\n    const handleVesselStatusChange = (value)=>{\n        if (!hasLogbookOpen) {\n            (value === null || value === void 0 ? void 0 : value.value) === \"OnVoyage\" ? sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"There is no Open LogBook entry, Please create a Logbook entry to set the vessel on voyage\") : setVesselStatus({\n                ...vesselStatus,\n                status: value === null || value === void 0 ? void 0 : value.value\n            });\n        } else {\n            value.value !== \"OnVoyage\" ? sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"There is an Open LogBook entry, Please complete the entry in order to update the vessel status\") : setVesselStatus({\n                ...vesselStatus,\n                status: value === null || value === void 0 ? void 0 : value.value\n            });\n        }\n    };\n    const handleVesselStatusReasonChange = (value)=>{\n        setVesselStatus({\n            ...vesselStatus,\n            reason: value === null || value === void 0 ? void 0 : value.value\n        });\n    };\n    const handleSetLogbooks = (data)=>{\n        data.sort((a, b)=>b.state === \"Locked\" ? -1 : a.state === \"Locked\" ? 1 : 0);\n        let lbs = [\n            ...data.filter((entry)=>entry.state !== \"Locked\").sort((a, b)=>new Date(a.startDate).getTime() - new Date(b.startDate).getTime()),\n            ...data.filter((entry)=>entry.state === \"Locked\").sort((a, b)=>new Date(b.startDate).getTime() - new Date(a.startDate).getTime())\n        ];\n        setLogbooks(lbs);\n        setIsNewLogEntryDisabled(false);\n        {\n            data.filter((entry)=>entry.state !== \"Locked\").length > 0 && setCurrentLogEntryAction(data.filter((entry)=>entry.state !== \"Locked\")[0]);\n        }\n        setTotalEntries(data.length);\n        {\n            data.filter((entry)=>entry.state !== \"Locked\").length > 0 && loadLogEntry(data.filter((entry)=>entry.state !== \"Locked\")[0].id);\n        }\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_7__.getLogBookEntries)(vesselId, handleSetLogbooks);\n    // getInventoryByVesselId(vesselId, setInventories)\n    const [queryInventoriesByVessel] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_INVENTORIES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readInventories.nodes;\n            if (data) {\n                setInventories(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryInventories error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        loadInventories();\n    }, []);\n    const loadInventories = async ()=>{\n        await queryInventoriesByVessel({\n            variables: {\n                filter: {\n                    vesselID: {\n                        eq: +vesselId\n                    }\n                }\n            }\n        });\n    };\n    const handleSetTrainingSessionDues = (data)=>{\n        const dues = (0,_app_helpers_trainingHelper__WEBPACK_IMPORTED_MODULE_25__.mergeTrainingSessionDues)(data).slice(0, 5);\n        setTrainingsDueCount((0,_app_helpers_vesselHelper__WEBPACK_IMPORTED_MODULE_14__.getTrainingsDueCount)(vesselId));\n        setTrainingSessionDues(data);\n        setTrainingSessionDuesSummary(dues);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_7__.getTrainingSessionDuesByVesselId)(vesselId, handleSetTrainingSessionDues);\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_7__.getTrainingSessionsByVesselId)(vesselId, setTrainingSessions);\n    const [queryGetEngines] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_ENGINES, {\n        // fetchPolicy: 'no-cache',\n        onCompleted: (response)=>{\n            const data = response.readEngines.nodes;\n            setEngineList(data);\n        },\n        onError: (error)=>{\n            console.error(\"getEngines error\", error);\n        }\n    });\n    const getEngines = async (engineIds)=>{\n        await queryGetEngines({\n            variables: {\n                id: engineIds\n            }\n        });\n    };\n    const [queryGetFuelTanks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_FUELTANKS, {\n        // fetchPolicy: 'no-cache',\n        onCompleted: (response)=>{\n            const data = response.readFuelTanks.nodes;\n            setFuelTankList(data);\n        },\n        onError: (error)=>{\n            console.error(\"getFuelTanks error\", error);\n        }\n    });\n    const getFuelTanks = async (fuelTankIds)=>{\n        await queryGetFuelTanks({\n            variables: {\n                id: fuelTankIds\n            }\n        });\n    };\n    const [queryGetWaterTanks] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_WATERTANKS, {\n        // fetchPolicy: 'no-cache',\n        onCompleted: (response)=>{\n            const data = response.readWaterTanks.nodes;\n            setWaterTankList(data);\n        },\n        onError: (error)=>{\n            console.error(\"getWaterTanks error\", error);\n        }\n    });\n    const getWaterTanks = async (waterTankIds)=>{\n        await queryGetWaterTanks({\n            variables: {\n                id: waterTankIds\n            }\n        });\n    };\n    const [queryGetSewageSystems] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_SEWAGESYSTEMS, {\n        // fetchPolicy: 'no-cache',\n        onCompleted: (response)=>{\n            const data = response.readSewageSystems.nodes;\n            setSewageSystemList(data);\n        },\n        onError: (error)=>{\n            console.error(\"getSewageSystems error\", error);\n        }\n    });\n    const getSewageSystems = async (sewageSystemIds)=>{\n        await queryGetSewageSystems({\n            variables: {\n                id: sewageSystemIds\n            }\n        });\n    };\n    const handleSetVessel = (vessel)=>{\n        var _vessel_seaLogsMembers, _vessel_parentComponent_Components, _vessel_parentComponent_Components1, _vessel_parentComponent_Components2, _vessel_parentComponent_Components3, _vessel_documents_nodes, _vessel_documents;\n        setVessel(vessel);\n        setVesselCrewIDs(vessel === null || vessel === void 0 ? void 0 : (_vessel_seaLogsMembers = vessel.seaLogsMembers) === null || _vessel_seaLogsMembers === void 0 ? void 0 : _vessel_seaLogsMembers.nodes.map((crew)=>crew.id));\n        (vessel === null || vessel === void 0 ? void 0 : vessel.seaLogsMembers) && loadCrewMemberInfo(vessel.seaLogsMembers.nodes.filter((crew)=>!crew.archived).map((crew)=>+crew.id));\n        const engineIds = vessel === null || vessel === void 0 ? void 0 : (_vessel_parentComponent_Components = vessel.parentComponent_Components) === null || _vessel_parentComponent_Components === void 0 ? void 0 : _vessel_parentComponent_Components.nodes.filter((item)=>item.basicComponent.componentCategory === \"Engine\").map((item)=>{\n            return item.basicComponent.id;\n        });\n        const fuelTankIds = vessel === null || vessel === void 0 ? void 0 : (_vessel_parentComponent_Components1 = vessel.parentComponent_Components) === null || _vessel_parentComponent_Components1 === void 0 ? void 0 : _vessel_parentComponent_Components1.nodes.filter((item)=>item.basicComponent.componentCategory === \"FuelTank\").map((item)=>{\n            return item.basicComponent.id;\n        });\n        const waterTankIds = vessel === null || vessel === void 0 ? void 0 : (_vessel_parentComponent_Components2 = vessel.parentComponent_Components) === null || _vessel_parentComponent_Components2 === void 0 ? void 0 : _vessel_parentComponent_Components2.nodes.filter((item)=>item.basicComponent.componentCategory === \"WaterTank\").map((item)=>{\n            return item.basicComponent.id;\n        });\n        const sewageSystemIds = vessel === null || vessel === void 0 ? void 0 : (_vessel_parentComponent_Components3 = vessel.parentComponent_Components) === null || _vessel_parentComponent_Components3 === void 0 ? void 0 : _vessel_parentComponent_Components3.nodes.filter((item)=>item.basicComponent.componentCategory === \"SewageSystem\").map((item)=>{\n            return item.basicComponent.id;\n        });\n        (engineIds === null || engineIds === void 0 ? void 0 : engineIds.length) > 0 && getEngines(engineIds);\n        (fuelTankIds === null || fuelTankIds === void 0 ? void 0 : fuelTankIds.length) > 0 && getFuelTanks(fuelTankIds);\n        (waterTankIds === null || waterTankIds === void 0 ? void 0 : waterTankIds.length) > 0 && getWaterTanks(waterTankIds);\n        (sewageSystemIds === null || sewageSystemIds === void 0 ? void 0 : sewageSystemIds.length) > 0 && getSewageSystems(sewageSystemIds);\n        (vessel === null || vessel === void 0 ? void 0 : (_vessel_documents = vessel.documents) === null || _vessel_documents === void 0 ? void 0 : (_vessel_documents_nodes = _vessel_documents.nodes) === null || _vessel_documents_nodes === void 0 ? void 0 : _vessel_documents_nodes.length) > 0 && setDocuments(vessel.documents.nodes);\n        if ((vessel === null || vessel === void 0 ? void 0 : vessel.logBookID) == 0) {\n            createNewLogBook(vessel);\n        }\n        if ((vessel === null || vessel === void 0 ? void 0 : vessel.bannerImageID) !== \"0\" && (vessel === null || vessel === void 0 ? void 0 : vessel.bannerImageID)) {\n            getFileDetails({\n                variables: {\n                    id: [\n                        vessel.bannerImageID\n                    ]\n                }\n            });\n        }\n    };\n    const [getFileDetails, { data, loading, error }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_FILES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            var _data_;\n            const data = response.readFiles.nodes;\n            setBannerImage(\"https://api.sealogs.com/assets/\" + ((_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.fileFilename));\n        },\n        onError: (error)=>{\n            console.error(error);\n        }\n    });\n    const createNewLogBook = async (vessel)=>{\n        await createLogBook({\n            variables: {\n                input: {\n                    title: vessel.title\n                }\n            }\n        });\n    };\n    const [createLogBook] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_32__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CREATE_LOGBOOK, {\n        onCompleted: (response)=>{\n            updateVessel({\n                variables: {\n                    input: {\n                        id: vesselId,\n                        logBookID: response.createLogBook.id\n                    }\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"createLogBook error\", error);\n        }\n    });\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_7__.getVesselByID)(vesselId, handleSetVessel);\n    const handleSetMaintenanceTasks = (data)=>{\n        if (data.length === 0) {\n            setMaintenanceTasks(false);\n        }\n        const tasks = data.filter((task)=>{\n            return (task === null || task === void 0 ? void 0 : task.archived) !== 1;\n        });\n        const inventoryTasks = inventories.flatMap((inventory)=>{\n            const checks = inventory.componentMaintenanceChecks || [];\n            return checks;\n        });\n        const combinedTasks = [\n            ...tasks,\n            ...inventoryTasks\n        ];\n        const seenIds = new Set();\n        const deduplicatedTasks = combinedTasks.filter((task)=>{\n            const isDuplicate = seenIds.has(task.id);\n            seenIds.add(task.id);\n            return !isDuplicate;\n        });\n        deduplicatedTasks.sort((a, b)=>{\n            var _a_isOverDue, _b_isOverDue;\n            // Add null checks for isOverDue\n            const aStatus = (_a_isOverDue = a.isOverDue) === null || _a_isOverDue === void 0 ? void 0 : _a_isOverDue.status;\n            const bStatus = (_b_isOverDue = b.isOverDue) === null || _b_isOverDue === void 0 ? void 0 : _b_isOverDue.status;\n            if (aStatus === \"High\" && bStatus !== \"High\") {\n                return -1;\n            } else if (aStatus !== \"High\" && bStatus === \"High\") {\n                return 1;\n            } else if (aStatus === \"Medium\" && bStatus !== \"Medium\") {\n                return -1;\n            } else if (aStatus !== \"Medium\" && bStatus === \"Medium\") {\n                return 1;\n            } else if (aStatus === \"Medium\" && bStatus === \"Medium\") {\n                return dayjs__WEBPACK_IMPORTED_MODULE_9___default()(b.startDate).diff(a.startDate);\n            } else if (aStatus === \"High\" && bStatus === \"High\") {\n                var _a_isOverDue1, _a_isOverDue_days_match, _b_isOverDue1, _b_isOverDue_days_match;\n                const aDays = ((_a_isOverDue1 = a.isOverDue) === null || _a_isOverDue1 === void 0 ? void 0 : _a_isOverDue1.days) ? parseInt(((_a_isOverDue_days_match = a.isOverDue.days.match(/(\\d+)/)) === null || _a_isOverDue_days_match === void 0 ? void 0 : _a_isOverDue_days_match[0]) || \"0\") : 0;\n                const bDays = ((_b_isOverDue1 = b.isOverDue) === null || _b_isOverDue1 === void 0 ? void 0 : _b_isOverDue1.days) ? parseInt(((_b_isOverDue_days_match = b.isOverDue.days.match(/(\\d+)/)) === null || _b_isOverDue_days_match === void 0 ? void 0 : _b_isOverDue_days_match[0]) || \"0\") : 0;\n                return bDays - aDays;\n            } else {\n                // rest of the sort logic remains the same\n                if (a.isCompleted === \"1\" && b.isCompleted === \"1\") {\n                    if (a.expires === \"NA\" && b.expires !== \"NA\") {\n                        return 1;\n                    } else if (a.expires !== \"NA\" && b.expires === \"NA\") {\n                        return -1;\n                    } else {\n                        return new Date(b.expires).getTime() - new Date(a.expires).getTime();\n                    }\n                } else if (a.isCompleted === \"1\") {\n                    return 1;\n                } else if (b.isCompleted === \"1\") {\n                    return -1;\n                } else {\n                    return dayjs__WEBPACK_IMPORTED_MODULE_9___default()(a.expires).diff(b.expires);\n                }\n            }\n        });\n        setMaintenanceTasks(deduplicatedTasks);\n        // setMaintenanceTasks(\n        //     deduplicatedTasks.filter((task: any) => {\n        //         return task?.archived !== 1\n        //     }),\n        // )\n        const appendedData = Array.from(new Set(deduplicatedTasks.filter((task)=>task.assignedToID > 0).map((task)=>task.assignedToID)));\n        loadCrewMemberInfo(appendedData, true);\n        // const tomorrow = new Date()\n        // tomorrow.setDate(tomorrow.getDate() + 1)\n        // const taskCounter = deduplicatedTasks.filter(\n        //     (task: any) =>\n        //         task.status !== 'Completed' &&\n        //         task.status !== 'Save_As_Draft' &&\n        //         task.isOverDue?.ignore !== true,\n        // ).length\n        const taskCounter = (0,_app_helpers_vesselHelper__WEBPACK_IMPORTED_MODULE_14__.getTasksDueCount)(vesselId);\n        setTaskCounter(taskCounter);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_7__.getComponentMaintenanceCheckByVesselId)(vesselId, handleSetMaintenanceTasks);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        handleSetMaintenanceTasks(maintenanceTasks);\n    }, [\n        inventories\n    ]);\n    const loadLogEntry = async (logEntryId)=>{\n        await queryLogEntry({\n            variables: {\n                logbookEntryId: logEntryId\n            }\n        });\n    };\n    const loadCrewMemberInfo = async function(crewIds) {\n        let task = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (crewIds.length > 0) {\n            task ? await queryTaskMembersInfo({\n                variables: {\n                    crewMemberIDs: crewIds\n                }\n            }) : await queryCrewMemberInfo({\n                variables: {\n                    crewMemberIDs: crewIds\n                }\n            });\n        }\n    };\n    const [queryLogEntry] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_LOGBOOK_ENTRY_BY_ID, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneLogBookEntry;\n            if (data) {\n                setCurrentLogEntry(data);\n            }\n        }\n    });\n    const [queryCrewMemberInfo] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_CREW_BY_IDS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers.nodes;\n            if (data) {\n                setCrewInfo(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryCrewMemberInfo error\", error);\n        }\n    });\n    const [queryTaskMembersInfo] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GET_CREW_BY_IDS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers.nodes;\n            if (data) {\n                setTaskCrewInfo(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryTaskMembersInfo error\", error);\n        }\n    });\n    const handlePagination = (page)=>{\n        if (page < 0 || currentPage === page) {\n            return;\n        }\n        setCurrentPage(page);\n    };\n    const deleteFile = (fileId)=>{\n        const newDocuments = documents.filter((doc)=>doc.id !== fileId);\n        setDocuments(newDocuments);\n        updateVessel({\n            variables: {\n                input: {\n                    id: vesselId,\n                    documents: newDocuments.map((doc)=>doc.id).join(\",\")\n                }\n            }\n        });\n    };\n    const deleteAllFiles = (document)=>{\n        const newDocuments = [];\n        setDocuments(newDocuments);\n        updateVessel({\n            variables: {\n                input: {\n                    id: vesselId,\n                    documents: newDocuments.map((doc)=>doc.id).join(\",\")\n                }\n            }\n        });\n    };\n    const [updateVessel] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_32__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UPDATE_VESSEL, {\n        onError: (error)=>{\n            console.error(\"updateVessel error\", error);\n        }\n    });\n    const handleUpdateVesselCrew = async ()=>{\n        await updateVessel({\n            variables: {\n                input: {\n                    id: vesselId,\n                    seaLogsMembers: vesselCrewIDs.join(\",\")\n                }\n            }\n        });\n        setDisplayAddCrew(false);\n        loadCrewMemberInfo(vesselCrewIDs);\n    };\n    const handleOnChangeVesselCrew = (data)=>{\n        setVesselCrewIDs(data.map((item)=>item.value));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (vesselTab === \"documents\") {\n            updateVessel({\n                variables: {\n                    input: {\n                        id: vesselId,\n                        documents: documents.map((doc)=>doc.id).join(\",\")\n                    }\n                }\n            });\n        }\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_8___default()(tab)) {\n            setVesselTab(tab);\n        }\n    }, [\n        documents,\n        tab\n    ]);\n    const handleCreateNewLogEntry = async ()=>{\n        if (!edit_logBookEntry) {\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"You do not have permission to create a new log entry\");\n            return;\n        }\n        if (hasLogbookOpen) {\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.custom((t)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-destructive text-destructive-foreground p-4 rounded-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-semibold\",\n                            children: \"Error\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                            lineNumber: 777,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                \"Please complete the open log entry\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    href: \"/log-entries?vesselID=\".concat(vesselId, \"&logentryID=\").concat(logbooks.filter((entry)=>entry.state !== \"Locked\")[0].id),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"underline\",\n                                        children: \"here\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                        lineNumber: 786,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                    lineNumber: 780,\n                                    columnNumber: 25\n                                }, this),\n                                \" \",\n                                \"before creating a new one.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                            lineNumber: 778,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                    lineNumber: 776,\n                    columnNumber: 17\n                }, this));\n        } else if ((vessel === null || vessel === void 0 ? void 0 : vessel.logBookID) > 0) {\n            setIsNewLogEntryDisabled(true);\n            await createLogEntry({\n                variables: {\n                    input: {\n                        logBookID: vessel.logBookID,\n                        vehicleID: vesselId\n                    }\n                }\n            });\n        }\n    };\n    const [createLogEntry] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_32__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CREATE_LOGBOOK_ENTRY, {\n        onCompleted: (response)=>{\n            router.push(\"/log-entries?vesselID=\".concat(vesselId, \"&logentryID=\").concat(response.createLogBookEntry.id));\n        },\n        onError: (error)=>{\n            console.error(\"createLogEntry error\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(error.message);\n        }\n    });\n    const hasLogbookOpen = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(()=>{\n        return logbooks.filter((entry)=>entry.state !== \"Locked\").length > 0;\n    }, [\n        logbooks\n    ]);\n    const vesselStatusLabel = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(()=>{\n        if ((vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.status) === \"OutOfService\") {\n            return \"Out Of Service\";\n        }\n        return hasLogbookOpen ? \"On voyage\" : \"Ready for voyage\";\n    }, [\n        hasLogbookOpen,\n        vesselStatus\n    ]);\n    const vesselStatusVariant = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(()=>{\n        if ((vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.status) === \"OutOfService\") {\n            return \"destructive\" // Red for out of service\n            ;\n        }\n        return hasLogbookOpen ? \"warning\" : \"success\" // Orange for on voyage, green for ready\n        ;\n    }, [\n        hasLogbookOpen,\n        vesselStatus\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        setImCrew((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.isCrew)() || false);\n    }, []);\n    const scrollToTabs = ()=>{\n        if (tabsRef.current) {\n            tabsRef.current.scrollIntoView({\n                behavior: \"smooth\",\n                block: \"start\"\n            });\n        }\n    };\n    const addButton = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"hidden md:flex flex-row justify-end\",\n        children: [\n            hasLogbookOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"invisible\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                lineNumber: 855,\n                columnNumber: 17\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: vesselTab === \"logEntries\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: !imCrew && /*<SeaLogsButton\r\n                                    text=\"New log entry\"\r\n                                    type=\"secondary\"\r\n                                    icon=\"new_logbook\"\r\n                                    color=\"slblue\"\r\n                                    action={handleCreateNewLogEntry}\r\n                                    isDisabled={isNewLogEntryDisabled}\r\n                                />*/ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                        disabled: isNewLogEntryDisabled,\n                        onClick: handleCreateNewLogEntry,\n                        children: \"New log entry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                        lineNumber: 869,\n                        columnNumber: 33\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                    lineNumber: 859,\n                    columnNumber: 25\n                }, this)\n            }, void 0, false),\n            vesselTab === \"crew\" && !imCrew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                onClick: ()=>{\n                    var _vessel_seaLogsMembers;\n                    setVesselCrewIDs(vessel === null || vessel === void 0 ? void 0 : (_vessel_seaLogsMembers = vessel.seaLogsMembers) === null || _vessel_seaLogsMembers === void 0 ? void 0 : _vessel_seaLogsMembers.nodes.map((crew)=>crew.id));\n                    setDisplayAddCrew(true);\n                },\n                children: \"Add crew\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                lineNumber: 880,\n                columnNumber: 17\n            }, this),\n            vesselTab === \"maintenance\" && /*<SeaLogsButton\r\n                    text=\"Add task\"\r\n                    type=\"primary\"\r\n                    icon=\"check\"\r\n                    color=\"slblue\"\r\n                    // link={`/maintenance/new?vesselID=${vesselId}&redirect_to=${pathname}?${searchParams.toString()}%26tab=maintenance`}\r\n                    action={() => {\r\n                        router.push(\r\n                            `/maintenance/new?vesselID=${vesselId}&redirect_to=${pathname}?${searchParams.toString()}%26tab=maintenance`,\r\n                        )\r\n                    }}\r\n                />*/ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                onClick: ()=>{\n                    router.push(\"/maintenance/new?vesselID=\".concat(vesselId, \"&redirect_to=\").concat(pathname, \"?\").concat(searchParams.toString(), \"%26tab=maintenance\"));\n                },\n                children: \"Add task\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                lineNumber: 905,\n                columnNumber: 17\n            }, this),\n            permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.hasPermission)(\"RECORD_TRAINING\", permissions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: vesselTab === \"crew_training\" && /*<SeaLogsButton\r\n                                text=\"Add Training\"\r\n                                type=\"primary\"\r\n                                icon=\"check\"\r\n                                color=\"slblue\"\r\n                                // link={`/crew-training/create?vesselID=${vesselId}`}\r\n                                action={() => {\r\n                                    router.push(\r\n                                        `/crew-training/create?vesselID=${vesselId}`,\r\n                                    )\r\n                                }}\r\n                            />*/ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                    onClick: ()=>{\n                        router.push(\"/crew-training/create?vesselID=\".concat(vesselId));\n                    },\n                    children: \"Add Training\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                    lineNumber: 929,\n                    columnNumber: 25\n                }, this)\n            }, void 0, false),\n            vesselTab === \"inventory\" && !imCrew && /*<SeaLogsButton\r\n                    text=\"Add Inventory\"\r\n                    type=\"primary\"\r\n                    icon=\"check\"\r\n                    color=\"slblue\"\r\n                    // link={`/inventory/new?vesselID=${vesselId}&redirectTo=${pathname}?${searchParams.toString()}%26tab=inventory`}\r\n                    action={() => {\r\n                        router.push(\r\n                            `/inventory/new?vesselID=${vesselId}&redirectTo=${pathname}?${searchParams.toString()}%26tab=inventory`,\r\n                        )\r\n                    }}\r\n                />*/ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                onClick: ()=>{\n                    router.push(\"/inventory/new?vesselID=\".concat(vesselId, \"&redirectTo=\").concat(pathname, \"?\").concat(searchParams.toString(), \"%26tab=inventory\"));\n                },\n                children: \"Add Inventory\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                lineNumber: 953,\n                columnNumber: 17\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n        lineNumber: 853,\n        columnNumber: 9\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    vessel && (vessel === null || vessel === void 0 ? void 0 : (_vessel_statusHistory = vessel.statusHistory) === null || _vessel_statusHistory === void 0 ? void 0 : (_vessel_statusHistory_nodes = _vessel_statusHistory.nodes) === null || _vessel_statusHistory_nodes === void 0 ? void 0 : _vessel_statusHistory_nodes.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_28__.Badge, {\n                        variant: vesselStatusVariant,\n                        type: \"normal\",\n                        className: \"flex flex-wrap absolute top-2 right-2 md:flex-inline items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                className: \"\",\n                                children: \"Status:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                lineNumber: 973,\n                                columnNumber: 25\n                            }, this),\n                            vesselStatusLabel,\n                            hasLogbookOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                size: \"sm\",\n                                variant: \"outline\",\n                                className: \"py-0 h-full\",\n                                //iconLeft={PencilIcon}\n                                onClick: ()=>setDisplayEditStatus(true),\n                                children: \"Edit\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                lineNumber: 977,\n                                columnNumber: 29\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                        lineNumber: 969,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header_image__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        bannerImage: bannerImage\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                        lineNumber: 988,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                lineNumber: 967,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full flex flex-col gap-8 overflow-hidden relative px-1 phablet:px-4 -mt-20 md:-mt-24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-col-1 lg:grid-cols-3 gap-8 lg:gap-6 xl:gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_logbook_entries_card__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            vesselId: vesselId,\n                            logbooks: logbooks,\n                            imCrew: imCrew,\n                            handleCreateNewLogEntry: handleCreateNewLogEntry,\n                            isNewLogEntryDisabled: isNewLogEntryDisabled,\n                            setVesselTab: setVesselTab,\n                            vesselTitle: vessel === null || vessel === void 0 ? void 0 : vessel.title,\n                            scrollToTabs: scrollToTabs\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                            lineNumber: 992,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_maintenance_card__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            maintenanceTasks: maintenanceTasks,\n                            pathname: pathname,\n                            setVesselTab: setVesselTab,\n                            scrollToTabs: scrollToTabs\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                            lineNumber: 1003,\n                            columnNumber: 21\n                        }, this),\n                        permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_13__.hasPermission)(\"VIEW_TRAINING\", permissions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_training_drills_card__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            trainingSessionDuesSummary: trainingSessionDuesSummary,\n                            setVesselTab: setVesselTab,\n                            scrollToTabs: scrollToTabs\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                            lineNumber: 1012,\n                            columnNumber: 29\n                        }, this),\n                        isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_crew_card__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    crewInfo: crewInfo,\n                                    setVesselTab: setVesselTab,\n                                    vesselId: vesselId,\n                                    pathname: pathname\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                    lineNumber: 1023,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_inventory_card__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    inventories: inventories,\n                                    setVesselTab: setVesselTab,\n                                    vesselId: vesselId,\n                                    pathname: pathname\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                    lineNumber: 1030,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                    lineNumber: 991,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                lineNumber: 990,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-1 phablet:px-4 hidden md:block\",\n                ref: tabsRef,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_tabs_holder__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                    taskCounter: taskCounter,\n                    trainingDueCounter: trainingsDueCount,\n                    vessel: vessel,\n                    engineList: engineList,\n                    fuelTankList: fuelTankList,\n                    waterTankList: waterTankList,\n                    sewageSystemList: sewageSystemList,\n                    vesselId: vesselId,\n                    logbooks: logbooks,\n                    totalEntries: totalEntries,\n                    perPage: perPage,\n                    handlePagination: handlePagination,\n                    currentPage: currentPage,\n                    maintenanceTasks: maintenanceTasks,\n                    crewInfo: crewInfo,\n                    trainingSessions: trainingSessions,\n                    trainingSessionDues: trainingSessionDues,\n                    inventories: inventories,\n                    imCrew: imCrew,\n                    edit_docs: edit_docs,\n                    setDocuments: setDocuments,\n                    documents: documents,\n                    delete_docs: delete_docs,\n                    deleteFile: deleteFile\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                    lineNumber: 1043,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                lineNumber: 1041,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_28__.AlertDialogNew, {\n                openDialog: displayAddCrew,\n                setOpenDialog: setDisplayAddCrew,\n                handleCreate: handleUpdateVesselCrew,\n                title: \"Add Crew\",\n                actionText: \"Add Crew\",\n                children: vessel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_crew_multiselect_dropdown_multiselect_dropdown__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    value: vesselCrewIDs,\n                    onChange: handleOnChangeVesselCrew,\n                    departments: vessel === null || vessel === void 0 ? void 0 : (_vessel_departments = vessel.departments) === null || _vessel_departments === void 0 ? void 0 : _vessel_departments.nodes\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                    lineNumber: 1077,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                lineNumber: 1070,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_28__.AlertDialogNew, {\n                size: \"xl\",\n                openDialog: displayEditStatus,\n                setOpenDialog: setDisplayEditStatus,\n                handleCreate: handleUpdateVesselStatus,\n                title: \"Update Vessel Status\",\n                actionText: \"Update\",\n                className: \"space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DateRange__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                        mode: \"single\",\n                        onChange: handleVesselStatusDate,\n                        placeholder: \"Select date\",\n                        value: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.date\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                        lineNumber: 1092,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2.5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_28__.Label, {\n                                label: \"Status\",\n                                className: \"flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_28__.Combobox, {\n                                    id: \"vessel-status\",\n                                    options: vesselStatuses,\n                                    placeholder: \"Status\",\n                                    value: vesselStatuses.find((status)=>(vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.status) === status.value),\n                                    onChange: handleVesselStatusChange\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                    lineNumber: 1100,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                lineNumber: 1099,\n                                columnNumber: 21\n                            }, this),\n                            (vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.status) === \"OutOfService\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_28__.Label, {\n                                label: \"Reason for out of service\",\n                                className: \"flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_28__.Combobox, {\n                                    id: \"vessel-status-reason\",\n                                    options: vesselStatusReason,\n                                    placeholder: \"Reason\",\n                                    value: vesselStatusReason.find((status)=>(vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.reason) === status.value),\n                                    onChange: handleVesselStatusReasonChange\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                    lineNumber: 1115,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                lineNumber: 1112,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                        lineNumber: 1098,\n                        columnNumber: 17\n                    }, this),\n                    (vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.status) === \"OutOfService\" && (vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.reason) === \"Other\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_28__.Textarea, {\n                        id: \"vessel-status-other\",\n                        placeholder: \"Other description\",\n                        value: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.otherReason,\n                        onChange: (e)=>setVesselStatus({\n                                ...vesselStatus,\n                                otherReason: e.target.value\n                            })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                        lineNumber: 1131,\n                        columnNumber: 25\n                    }, this),\n                    (vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.status) === \"OutOfService\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_28__.Label, {\n                        label: \"Comments\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_editor__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                            id: \"comment\",\n                            placeholder: \"Comment\",\n                            className: \"bg-background\",\n                            content: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.comment,\n                            handleEditorChange: (content)=>setVesselStatus({\n                                    ...vesselStatus,\n                                    comment: content\n                                })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                            lineNumber: 1145,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                        lineNumber: 1144,\n                        columnNumber: 21\n                    }, this),\n                    (vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.status) === \"OutOfService\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_28__.Label, {\n                        label: \"Expected date of return\",\n                        htmlFor: \"expected-return-date\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DateRange__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                            className: \"flex w-full\",\n                            mode: \"single\",\n                            onChange: handleVesselStatusReturnDate,\n                            placeholder: \"Select date\",\n                            value: vesselStatus === null || vesselStatus === void 0 ? void 0 : vesselStatus.expectedReturn\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                            lineNumber: 1163,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                        lineNumber: 1160,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                lineNumber: 1084,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_footer_wrapper__WEBPACK_IMPORTED_MODULE_27__.FooterWrapper, {\n                className: \" justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                        lineNumber: 1175,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                variant: \"back\",\n                                size: \"sm\",\n                                onClick: ()=>router.back(),\n                                iconLeft: _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"],\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                lineNumber: 1177,\n                                columnNumber: 21\n                            }, this),\n                            !imCrew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_26__.DropdownMenu, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_26__.DropdownMenuTrigger, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            size: \"sm\",\n                                            /*iconLeft={\r\n                                        <svg\r\n                                            key={'Edit'}\r\n                                            className=\"-ml-0.5 mr-1.5 h-5 w-5 group-hover:border-white\"\r\n                                            viewBox=\"0 0 36 36\"\r\n                                            fill=\"currentColor\"\r\n                                            aria-hidden=\"true\">\r\n                                            <path d=\"M33.87,8.32,28,2.42a2.07,2.07,0,0,0-2.92,0L4.27,23.2l-1.9,8.2a2.06,2.06,0,0,0,2,2.5,2.14,2.14,0,0,0,.43,0L13.09,32,33.87,11.24A2.07,2.07,0,0,0,33.87,8.32ZM12.09,30.2,4.32,31.83l1.77-7.62L21.66,8.7l6,6ZM29,13.25l-6-6,3.48-3.46,5.9,6Z\"></path>\r\n                                        </svg>\r\n                                    }*/ variant: \"outline\",\n                                            children: \"Edit\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                            lineNumber: 1187,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                        lineNumber: 1186,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_26__.DropdownMenuContent, {\n                                        align: \"end\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_26__.DropdownMenuGroup, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_26__.DropdownMenuItem, {\n                                                    onClick: ()=>{\n                                                        router.push(\"/vessel/edit?id=\".concat(vesselId));\n                                                    },\n                                                    children: \"Edit Vessel\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                                    lineNumber: 1205,\n                                                    columnNumber: 37\n                                                }, this),\n                                                (vessel === null || vessel === void 0 ? void 0 : vessel.logBookID) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_26__.DropdownMenuItem, {\n                                                    onClick: ()=>{\n                                                        router.push(\"/vessel/logbook-configuration?logBookID=\".concat(vessel.logBookID, \"&vesselID=\").concat(vesselId));\n                                                    },\n                                                    children: \"Edit Logbook Configuration\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                                    lineNumber: 1214,\n                                                    columnNumber: 41\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                            lineNumber: 1204,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                        lineNumber: 1203,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                                lineNumber: 1185,\n                                columnNumber: 25\n                            }, this),\n                            addButton\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                        lineNumber: 1176,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\vessels\\\\view.tsx\",\n                lineNumber: 1174,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(VesselsView, \"JByq6WniPGNLPzCblYspC7Xyji4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter,\n        nuqs__WEBPACK_IMPORTED_MODULE_31__.useQueryState,\n        _components_hooks_use_mobile__WEBPACK_IMPORTED_MODULE_12__.useIsMobile,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_32__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_32__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_33__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_32__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_32__.useMutation\n    ];\n});\n_c = VesselsView;\nvar _c;\n$RefreshReg$(_c, \"VesselsView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/vessels/view.tsx\n"));

/***/ })

});