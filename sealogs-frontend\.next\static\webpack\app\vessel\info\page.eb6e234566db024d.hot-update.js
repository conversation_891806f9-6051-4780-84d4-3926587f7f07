"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/vessel/info/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/components/location.tsx":
/*!****************************************************!*\
  !*** ./src/app/ui/logbook/components/location.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LocationField; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var lodash_trim__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/trim */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/trim.js\");\n/* harmony import */ var lodash_trim__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_trim__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _app_offline_models_favoriteLocation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/offline/models/favoriteLocation */ \"(app-pages-browser)/./src/app/offline/models/favoriteLocation.js\");\n/* harmony import */ var _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/offline/models/geoLocation */ \"(app-pages-browser)/./src/app/offline/models/geoLocation.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,MapPin!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,MapPin!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./src/components/ui/radio-group.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction LocationField(param) {\n    let { setCurrentLocation, handleLocationChange, currentEvent, offline = false, showAddNewLocation = true, showUseCoordinates = true, showCurrentLocation = true } = param;\n    _s();\n    const [geoLocations, setGeoLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [newLocation, setNewLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [closestLocation, setClosestLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedLocation, setSelectedLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [favoriteLocations, setFavoriteLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedParentLocation, setSelectedParentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [openNewLocationDialog, setOpenNewLocationDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openSetLocationDialog, setOpenSetLocationDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [location, setLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        latitude: 0,\n        longitude: 0\n    });\n    const favoriteLocationModel = new _app_offline_models_favoriteLocation__WEBPACK_IMPORTED_MODULE_8__[\"default\"]();\n    const geolocationModel = new _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_9__[\"default\"]();\n    const displayNewLocation = ()=>{\n        setNewLocation(true);\n    };\n    const hideNewLocation = ()=>{\n        setNewLocation(false);\n    };\n    const handleSetLocationChange = async (selectedLocation)=>{\n        if (!selectedLocation) {\n            // Handle clearing the selection\n            setSelectedLocation(null);\n            handleLocationChange(null);\n            return;\n        }\n        if (selectedLocation.value === \"newLocation\") {\n            setSelectedParentLocation(false);\n            // Instead of opening a separate modal, switch to the \"new\" option in the current modal\n            setLocationOption(\"new\");\n        } else {\n            setSelectedLocation(selectedLocation);\n            handleLocationChange(selectedLocation);\n            const userId = localStorage.getItem(\"userId\");\n            if (userId !== null && +userId > 0) {\n                // if (+localStorage.getItem('userId')> 0) {\n                if (offline) {\n                    // Check if this location is already a favorite\n                    const existingFavorite = favoriteLocations.find((fav)=>fav.geoLocationID === selectedLocation.value);\n                    if (existingFavorite) {\n                        // Update usage count for existing favorite\n                        const updatedFavorite = {\n                            ...existingFavorite,\n                            usage: (existingFavorite.usage || 0) + 1\n                        };\n                        await favoriteLocationModel.save(updatedFavorite);\n                        // Update favoriteLocations state\n                        const updatedFavorites = favoriteLocations.map((fav)=>fav.id === existingFavorite.id ? updatedFavorite : fav);\n                        setFavoriteLocations(updatedFavorites);\n                    } else {\n                        // Create new favorite with high usage\n                        const newFavorite = {\n                            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_10__.generateUniqueId)(),\n                            memberID: +localStorage.getItem(\"userId\"),\n                            geoLocationID: +selectedLocation.value,\n                            usage: 999\n                        };\n                        await favoriteLocationModel.save(newFavorite);\n                        // Update favoriteLocations state\n                        setFavoriteLocations([\n                            newFavorite,\n                            ...favoriteLocations\n                        ]);\n                    }\n                } else {\n                    // Check if this location is already a favorite\n                    const existingFavorite = favoriteLocations.find((fav)=>fav.geoLocationID === selectedLocation.value);\n                    createFavoriteLocation({\n                        variables: {\n                            input: {\n                                memberID: +localStorage.getItem(\"userId\"),\n                                geoLocationID: +selectedLocation.value\n                            }\n                        },\n                        onCompleted: (data)=>{\n                            if (existingFavorite) {\n                                // If it was already a favorite, we've just increased its usage count\n                                // Refresh the favorites list to get updated usage counts\n                                getFavoriteLocations({\n                                    variables: {\n                                        userID: +localStorage.getItem(\"userId\")\n                                    }\n                                });\n                            } else {\n                                // Add the new favorite to the state with high usage\n                                const newFavorite = {\n                                    id: data.createFavoriteLocation.id,\n                                    geoLocationID: selectedLocation.value,\n                                    usage: 999\n                                };\n                                setFavoriteLocations([\n                                    newFavorite,\n                                    ...favoriteLocations\n                                ]);\n                            }\n                        }\n                    });\n                }\n            }\n        }\n    };\n    const [createFavoriteLocation] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateFavoriteLocation, {\n        onError: (error)=>{\n            console.error(\"Error creating favorite location:\", error);\n        }\n    });\n    const handleParentLocationChange = (selectedLocation)=>{\n        setSelectedParentLocation(selectedLocation);\n    };\n    const handleCreateNewLocation = async ()=>{\n        var _titleInput_value, _latitudeInput_value, _longitudeInput_value;\n        const titleInput = document.getElementById(\"new-location-title\");\n        const latitudeInput = document.getElementById(\"new-location-latitude\");\n        const longitudeInput = document.getElementById(\"new-location-longitude\");\n        // Validate inputs exist\n        const title = ((_titleInput_value = titleInput.value) === null || _titleInput_value === void 0 ? void 0 : _titleInput_value.trim()) || \"New location\";\n        const parentLocation = selectedParentLocation ? selectedParentLocation.value : null;\n        const latitude = ((_latitudeInput_value = latitudeInput.value) === null || _latitudeInput_value === void 0 ? void 0 : _latitudeInput_value.trim()) || \"0\";\n        const longitude = ((_longitudeInput_value = longitudeInput.value) === null || _longitudeInput_value === void 0 ? void 0 : _longitudeInput_value.trim()) || \"0\";\n        // Convert to numbers and validate\n        const latNum = parseFloat(latitude);\n        const longNum = parseFloat(longitude);\n        const variables = {\n            input: {\n                title: title,\n                lat: isNaN(latNum) ? 0 : latNum,\n                long: isNaN(longNum) ? 0 : longNum,\n                parentLocationID: parentLocation\n            }\n        };\n        if (offline) {\n            const uniqueID = (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_10__.generateUniqueId)();\n            const data = await geolocationModel.save({\n                ...variables.input,\n                id: uniqueID\n            });\n            if ((locations === null || locations === void 0 ? void 0 : locations.length) > 0) {\n                setLocations([\n                    ...locations,\n                    data\n                ]);\n            } else {\n                setLocations([\n                    data\n                ]);\n            }\n            // Create location object to use\n            const newLocation = {\n                label: data.title,\n                value: data.id,\n                latitude: data.lat,\n                longitude: data.long\n            };\n            setSelectedLocation(newLocation);\n            handleLocationChange(newLocation);\n            // Automatically mark newly created location as a favorite\n            const userId = localStorage.getItem(\"userId\");\n            if (userId !== null && +userId > 0) {\n                // Save to favorite locations with high usage to ensure it appears at the top\n                const favoriteId = (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_10__.generateUniqueId)();\n                const newFavorite = {\n                    id: favoriteId,\n                    memberID: +userId,\n                    geoLocationID: data.id,\n                    usage: 999\n                };\n                await favoriteLocationModel.save(newFavorite);\n                // Update favoriteLocations state to include the new favorite\n                setFavoriteLocations([\n                    newFavorite,\n                    ...favoriteLocations\n                ]);\n            }\n            setOpenNewLocationDialog(false);\n            setOpenSetLocationDialog(false);\n            setOpenLocationModal(false) // Also close the main modal\n            ;\n        } else {\n            createGeoLocation({\n                variables\n            });\n        }\n    };\n    const [createGeoLocation] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CREATE_GEO_LOCATION, {\n        onCompleted: (response)=>{\n            const data = response.createGeoLocation;\n            if ((locations === null || locations === void 0 ? void 0 : locations.length) > 0) {\n                setLocations([\n                    ...locations,\n                    data\n                ]);\n            } else {\n                setLocations([\n                    data\n                ]);\n            }\n            // Create location object to use\n            const newLocation = {\n                label: data.title,\n                value: data.id,\n                latitude: data.lat,\n                longitude: data.long\n            };\n            setSelectedLocation(newLocation);\n            handleLocationChange(newLocation);\n            // Automatically mark newly created location as a favorite\n            const userId = localStorage.getItem(\"userId\");\n            if (userId !== null && +userId > 0) {\n                createFavoriteLocation({\n                    variables: {\n                        input: {\n                            memberID: +userId,\n                            geoLocationID: +data.id\n                        }\n                    },\n                    onCompleted: (favData)=>{\n                        // Update favoriteLocations state to include the new favorite\n                        // with high usage to ensure it appears at the top\n                        const newFavorite = {\n                            id: favData.createFavoriteLocation.id,\n                            geoLocationID: data.id,\n                            usage: 999\n                        };\n                        setFavoriteLocations([\n                            newFavorite,\n                            ...favoriteLocations\n                        ]);\n                    }\n                });\n            }\n            setOpenNewLocationDialog(false);\n            setOpenSetLocationDialog(false);\n            setOpenLocationModal(false) // Also close the main modal\n            ;\n        },\n        onError: (error)=>{\n            setOpenNewLocationDialog(false);\n            setOpenSetLocationDialog(false);\n            setOpenLocationModal(false) // Also close the main modal\n            ;\n            console.error(\"Error creating GeoLocation\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Error creating GeoLocation\");\n        }\n    });\n    const offlineGetFavoriteLocations = async ()=>{\n        const locations = await favoriteLocationModel.getByMemberID(+localStorage.getItem(\"userId\"));\n        setFavoriteLocations(locations);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            offlineGetFavoriteLocations();\n        } else {\n            getFavoriteLocations({\n                variables: {\n                    userID: +localStorage.getItem(\"userId\")\n                }\n            });\n        }\n    }, []);\n    const [getFavoriteLocations] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_7__.GetFavoriteLocations, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            const locations = data.readFavoriteLocations.nodes;\n            setFavoriteLocations(locations);\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const allLocations = ()=>{\n        // Filter function to remove locations with empty or whitespace-only titles\n        const filterValidLocations = (locs)=>{\n            return locs.filter((loc)=>!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_6___default()(loc.label)));\n        };\n        let mappedLocations = (geoLocations === null || geoLocations === void 0 ? void 0 : geoLocations.map((location)=>({\n                label: location.title,\n                value: location.id,\n                latitude: location.lat,\n                longitude: location.long\n            }))) || [];\n        // Combine locations if needed\n        let combinedLocations = [];\n        if (geoLocations && (locations === null || locations === void 0 ? void 0 : locations.length) > 0) {\n            combinedLocations = [\n                ...locations,\n                ...mappedLocations\n            ];\n        } else {\n            combinedLocations = [\n                ...mappedLocations\n            ];\n        }\n        // Filter out locations with empty titles before sorting\n        const validLocations = filterValidLocations(combinedLocations);\n        // Sort the filtered locations\n        return sortLocations(validLocations);\n    };\n    const sortLocations = (locations)=>{\n        // Return empty array if locations is empty or undefined\n        if (!locations || locations.length === 0) {\n            return [];\n        }\n        // Create a copy of the array to avoid mutating the original\n        const locationsToSort = [\n            ...locations\n        ];\n        if (favoriteLocations.length > 0) {\n            return locationsToSort.sort((a, b)=>{\n                // Find if locations are favorites\n                const aFav = favoriteLocations.find((fav)=>fav.geoLocationID === a.value);\n                const bFav = favoriteLocations.find((fav)=>fav.geoLocationID === b.value);\n                // Both are favorites - first sort by ID (highest to lowest for newly created locations)\n                if (aFav && bFav) {\n                    return bFav.usage - aFav.usage;\n                } else if (aFav) {\n                    return -1;\n                } else if (bFav) {\n                    return 1;\n                }\n                return 0;\n            });\n        }\n        return locationsToSort;\n    };\n    const getProximity = (location, latitude, longitude)=>{\n        const distance = Math.sqrt(Math.pow(location.lat - latitude, 2) + Math.pow(location.long - longitude, 2));\n        return distance;\n    };\n    const findClosestLocation = (latitude, longitude)=>{\n        // Only try to find closest location if we have locations available\n        if (geoLocations.length === 0) {\n            setClosestLocation(null);\n            // No locations available, use coordinates directly\n            handleUseCoordinatesAsLocation(latitude, longitude);\n            return;\n        }\n        const closestLocation = geoLocations.reduce((prev, curr)=>{\n            const prevDistance = Math.sqrt(Math.pow(prev.lat - latitude, 2) + Math.pow(prev.long - longitude, 2));\n            const currDistance = Math.sqrt(Math.pow(curr.lat - latitude, 2) + Math.pow(curr.long - longitude, 2));\n            return prevDistance < currDistance ? prev : curr;\n        });\n        const proximity = getProximity(closestLocation, latitude, longitude);\n        // Check if closest location is reasonably close and valid\n        if (proximity <= 0.15 && closestLocation.lat !== 0 && closestLocation.long !== 0) {\n            // Close location found - show the dialog\n            setClosestLocation({\n                label: closestLocation.title,\n                value: closestLocation.id,\n                latitude: closestLocation.lat,\n                longitude: closestLocation.long\n            });\n            setOpenSetLocationDialog(true);\n        } else {\n            // No close location found - use coordinates directly\n            setClosestLocation(null);\n            handleUseCoordinatesAsLocation(latitude, longitude);\n        }\n    };\n    // Helper function to use coordinates when no close location is found\n    const handleUseCoordinatesAsLocation = (latitude, longitude)=>{\n        // Clear any selected location since we're using coordinates\n        setSelectedLocation(null);\n        // Pass coordinates to parent component\n        handleLocationChange({\n            latitude: +latitude,\n            longitude: +longitude\n        });\n    };\n    const handleSetCurrentLocation = ()=>{\n        if (\"geolocation\" in navigator) {\n            const options = {\n                timeout: 30000\n            };\n            navigator.geolocation.getCurrentPosition((param)=>{\n                let { coords } = param;\n                const { latitude, longitude } = coords;\n                setLocation({\n                    latitude,\n                    longitude\n                });\n                setCurrentLocation({\n                    latitude: +latitude,\n                    longitude: +longitude\n                });\n                handleLocationChange({\n                    latitude: +latitude,\n                    longitude: +longitude\n                });\n                // Find closest location first\n                findClosestLocation(latitude, longitude);\n            }, (error)=>{\n                let errorMessage = \"Failed to get current location\";\n                if (error.code === error.PERMISSION_DENIED) {\n                    errorMessage = \"Location access denied. Please enable location permissions.\";\n                } else if (error.code === error.POSITION_UNAVAILABLE) {\n                    errorMessage = \"Location information unavailable.\";\n                } else if (error.code === error.TIMEOUT) {\n                    errorMessage = \"Location request timed out.\";\n                }\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(errorMessage);\n            }, options);\n        } else {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Geolocation is not supported by your browser\");\n        }\n    };\n    const handleSetLocation = ()=>{\n        setSelectedLocation(closestLocation);\n        handleLocationChange(closestLocation);\n        setOpenLocationModal(false);\n        setOpenSetLocationDialog(false);\n    };\n    const updateLocationCoordinates = ()=>{\n        const latitude = document.getElementById(\"location-latitude\").value;\n        const longitude = document.getElementById(\"location-longitude\").value;\n        // Update local state\n        setLocation({\n            latitude: +latitude,\n            longitude: +longitude\n        });\n        // Update parent component state\n        setCurrentLocation({\n            latitude: +latitude,\n            longitude: +longitude\n        });\n        // Clear selectedLocation when manually entering coordinates\n        setSelectedLocation(null);\n    };\n    const [getGeoLocations] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_7__.GET_GEO_LOCATIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readGeoLocations.nodes;\n            if (data) {\n                setGeoLocations(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryGeoLocations error\", error);\n        }\n    });\n    const loadGeoLocations = async ()=>{\n        if (offline) {\n            const data = await geolocationModel.getAll();\n            if (data) {\n                setGeoLocations(data);\n            }\n        } else {\n            await getGeoLocations();\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadGeoLocations();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentEvent) {\n            if (+(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.geoLocationID) > 0) {\n                geoLocations === null || geoLocations === void 0 ? void 0 : geoLocations.find((location)=>{\n                    if (location.id === currentEvent.geoLocationID) {\n                        setSelectedLocation({\n                            label: location.title,\n                            value: location.id,\n                            latitude: location.lat,\n                            longitude: location.long\n                        });\n                    }\n                });\n                // Clear location coordinates when a location is selected\n                setLocation({\n                    latitude: 0,\n                    longitude: 0\n                });\n            } else {\n                if ((currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.lat) && (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.long)) {\n                    // Set coordinates and clear selected location\n                    setLocation({\n                        latitude: currentEvent.lat,\n                        longitude: currentEvent.long\n                    });\n                    setSelectedLocation(null);\n                }\n            }\n        }\n    }, [\n        currentEvent,\n        geoLocations\n    ]);\n    // Initialize form data when location changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize form data with current location coordinates\n        setNewLocationFormData((prev)=>({\n                ...prev,\n                latitude: location.latitude ? location.latitude.toString() : \"\",\n                longitude: location.longitude ? location.longitude.toString() : \"\"\n            }));\n        // Validate the form after a short delay\n        setTimeout(validateNewLocationForm, 100);\n    }, [\n        location\n    ]);\n    // Create refs for latitude and longitude inputs\n    const latInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const longInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // State for the combined location modal\n    const [openLocationModal, setOpenLocationModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [locationOption, setLocationOption] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"select\");\n    // We'll use the validateNewLocationForm function directly instead of storing the state\n    const [newLocationFormData, setNewLocationFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        latitude: \"\",\n        longitude: \"\"\n    });\n    // Handle location option change in the modal\n    const handleLocationOptionChange = (value)=>{\n        setLocationOption(value);\n        // Reset other inputs when changing options\n        if (value === \"coordinates\") {\n            // Show coordinates input\n            setNewLocation(false);\n            // Clear selectedLocation when switching to coordinates\n            setSelectedLocation(null);\n        } else {\n            if (value === \"current\") {\n                // Trigger current location detection\n                handleSetCurrentLocation();\n            } else if (value === \"new\") {\n                // Show new location form in the current modal\n                setNewLocation(true);\n            // Don't open the separate modal dialog\n            // setOpenNewLocationDialog(true)\n            }\n        }\n    };\n    // Get display text for the location field\n    const getLocationDisplayText = ()=>{\n        if (selectedLocation === null || selectedLocation === void 0 ? void 0 : selectedLocation.label) {\n            return selectedLocation.label;\n        } else if (// Check if we have valid coordinates to display\n        location.latitude !== undefined && location.latitude !== null && location.latitude !== 0 || location.longitude !== undefined && location.longitude !== null && location.longitude !== 0) {\n            // Format coordinates with fixed decimal places for better readability\n            return \"\".concat(Number(location.latitude).toFixed(6), \", \").concat(Number(location.longitude).toFixed(6));\n        } else {\n            return \"Select location\";\n        }\n    };\n    // Handle the \"Use Location\" button in the modal\n    const handleUseLocation = ()=>{\n        if (locationOption === \"coordinates\") {\n            // Make sure coordinates are properly set\n            hideNewLocation();\n            // Clear selectedLocation when using coordinates\n            setSelectedLocation(null);\n            // Pass coordinates to parent component\n            handleLocationChange({\n                latitude: location.latitude,\n                longitude: location.longitude\n            });\n        }\n        setOpenLocationModal(false);\n    };\n    // Validate the new location form\n    const validateNewLocationForm = ()=>{\n        const { title, latitude, longitude } = newLocationFormData;\n        const isValid = title.trim() !== \"\" && latitude.trim() !== \"\" && longitude.trim() !== \"\" && !isNaN(parseFloat(latitude)) && !isNaN(parseFloat(longitude));\n        return isValid;\n    };\n    // Handle input changes for the new location form\n    const handleNewLocationInputChange = (e)=>{\n        const { name, value } = e.target;\n        setNewLocationFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        // Validate after a short delay to avoid excessive validation\n        setTimeout(validateNewLocationForm, 100);\n    };\n    // Handle the \"Add Location\" button in the modal\n    const handleAddLocation = ()=>{\n        // Validate form before submitting\n        if (validateNewLocationForm()) {\n            // Set the input values to match our state before calling handleCreateNewLocation\n            const titleInput = document.getElementById(\"new-location-title\");\n            const latInput = document.getElementById(\"new-location-latitude\");\n            const longInput = document.getElementById(\"new-location-longitude\");\n            if (titleInput && latInput && longInput) {\n                titleInput.value = newLocationFormData.title;\n                latInput.value = newLocationFormData.latitude;\n                longInput.value = newLocationFormData.longitude;\n            }\n            handleCreateNewLocation();\n            setOpenLocationModal(false);\n        } else {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please fill in all required fields with valid values\");\n        }\n    };\n    // Override the existing handlers to close the modal\n    const originalHandleSetLocationChange = handleSetLocationChange;\n    const handleSetLocationChangeWithModal = (selectedLocation)=>{\n        originalHandleSetLocationChange(selectedLocation);\n        if (selectedLocation && selectedLocation.value !== \"newLocation\") {\n            setOpenLocationModal(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                iconRight: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                    className: \"text-outer-border-outer-space-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                    lineNumber: 766,\n                    columnNumber: 28\n                }, void 0),\n                className: \"w-full justify-between max-h-11 text-base\",\n                variant: \"outline\",\n                onClick: ()=>{\n                    setLocationOption(\"select\");\n                    setOpenLocationModal(true);\n                },\n                children: getLocationDisplayText()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                lineNumber: 765,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_16__.AlertDialogNew, {\n                openDialog: openLocationModal,\n                setOpenDialog: setOpenLocationModal,\n                handleCreate: ()=>{\n                    if (locationOption === \"coordinates\") {\n                        handleUseLocation();\n                    } else if (locationOption === \"current\") {\n                        // For current location, check if we have coordinates but no closest location\n                        if (!(closestLocation === null || closestLocation === void 0 ? void 0 : closestLocation.label) && (location.latitude !== 0 || location.longitude !== 0)) {\n                            // Coordinates are already set via handleUseCoordinatesAsLocation, just close modal\n                            setOpenLocationModal(false);\n                        // } else if (closestLocation?.label) {\n                        // There's a closest location available, but user needs to click the \"Use [Location]\" button\n                        // Don't close modal, let them choose\n                        // return\n                        } else {\n                            // No coordinates yet, this shouldn't happen but handle gracefully\n                            setOpenLocationModal(false);\n                        }\n                    } else if (locationOption === \"new\") {\n                        // Only call handleAddLocation if the form is valid\n                        if (validateNewLocationForm()) {\n                            handleAddLocation();\n                        } else {\n                            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Please fill in all required fields with valid values\");\n                            // Return without closing the modal\n                            return;\n                        }\n                    } else {\n                        // Default case for 'select' option\n                        setOpenLocationModal(false);\n                    }\n                },\n                actionText: locationOption === \"coordinates\" ? \"Use Location\" : locationOption === \"current\" ? !(closestLocation === null || closestLocation === void 0 ? void 0 : closestLocation.label) && (location.latitude !== 0 || location.longitude !== 0) ? \"Use GPS Coordinates\" : \"Get Location\" : locationOption === \"new\" ? \"Add Location\" : \"OK\",\n                title: \"Select location\",\n                size: \"md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col gap-5\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_15__.RadioGroup, {\n                        value: locationOption,\n                        onValueChange: handleLocationOptionChange,\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full flex gap-2.5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_15__.RadioGroupItem, {\n                                        size: \"md\",\n                                        value: \"select\",\n                                        id: \"select-location\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                        lineNumber: 839,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                        className: \"text-base w-full font-medium\",\n                                        label: \"Select from locations\",\n                                        htmlFor: \"select-location\",\n                                        children: locationOption === \"select\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-row w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_2__.Combobox, {\n                                                options: geoLocations ? [\n                                                    {\n                                                        label: \" Add New Location\",\n                                                        value: \"newLocation\"\n                                                    },\n                                                    ...allLocations()\n                                                ] : [\n                                                    {\n                                                        label: \" Add New Location\",\n                                                        value: \"newLocation\"\n                                                    }\n                                                ],\n                                                value: selectedLocation,\n                                                onChange: handleSetLocationChangeWithModal,\n                                                placeholder: \"Select location\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                                lineNumber: 850,\n                                                columnNumber: 41\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                            lineNumber: 849,\n                                            columnNumber: 37\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                        lineNumber: 844,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                lineNumber: 838,\n                                columnNumber: 25\n                            }, this),\n                            showUseCoordinates && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2.5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_15__.RadioGroupItem, {\n                                        size: \"md\",\n                                        value: \"coordinates\",\n                                        id: \"enter-coordinates\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                        lineNumber: 881,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                        label: \"Enter coordinates\",\n                                        htmlFor: \"enter-coordinates\",\n                                        className: \"text-base font-medium\",\n                                        children: locationOption === \"coordinates\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col gap-4 w-full p-4 border border-outer-space-400 border-dashed rounded-lg bg-card\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col gap-4 w-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                                                label: \"Latitude\",\n                                                                htmlFor: \"location-latitude\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_11__.Input, {\n                                                                    id: \"location-latitude\",\n                                                                    name: \"latitude\",\n                                                                    type: \"number\",\n                                                                    ref: latInputRef,\n                                                                    value: location.latitude,\n                                                                    onChange: updateLocationCoordinates,\n                                                                    placeholder: \"Enter latitude\",\n                                                                    className: \"w-full\",\n                                                                    required: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                                                    lineNumber: 897,\n                                                                    columnNumber: 57\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                                                lineNumber: 894,\n                                                                columnNumber: 53\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                                                label: \"Longitude\",\n                                                                htmlFor: \"location-longitude\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_11__.Input, {\n                                                                    id: \"location-longitude\",\n                                                                    name: \"longitude\",\n                                                                    type: \"number\",\n                                                                    ref: longInputRef,\n                                                                    value: location.longitude,\n                                                                    onChange: updateLocationCoordinates,\n                                                                    placeholder: \"Enter longitude\",\n                                                                    className: \"w-full\",\n                                                                    required: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                                                    lineNumber: 917,\n                                                                    columnNumber: 57\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                                                lineNumber: 914,\n                                                                columnNumber: 53\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                                        lineNumber: 893,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground italic\",\n                                                            children: \"Enter coordinates in decimal degrees format (e.g., 37.80255, -122.41463)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                                            lineNumber: 935,\n                                                            columnNumber: 53\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                                        lineNumber: 934,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    (location.latitude !== 0 || location.longitude !== 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 p-3 bg-muted rounded-md\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"Current coordinates:\",\n                                                                \" \",\n                                                                Number(location.latitude).toFixed(6),\n                                                                \",\",\n                                                                \" \",\n                                                                Number(location.longitude).toFixed(6)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                                            lineNumber: 946,\n                                                            columnNumber: 57\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                                        lineNumber: 945,\n                                                        columnNumber: 53\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                                lineNumber: 892,\n                                                columnNumber: 45\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                            lineNumber: 891,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                        lineNumber: 886,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                lineNumber: 880,\n                                columnNumber: 29\n                            }, this),\n                            showCurrentLocation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2.5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_15__.RadioGroupItem, {\n                                        size: \"md\",\n                                        value: \"current\",\n                                        id: \"current-location\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                        lineNumber: 968,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                        label: \"Use current location\",\n                                        htmlFor: \"current-location\",\n                                        className: \"text-base font-medium\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Automatically detects your location and uses the nearest available location or GPS coordinates.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                                    lineNumber: 978,\n                                                    columnNumber: 41\n                                                }, this),\n                                                locationOption === \"current\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2\",\n                                                    children: [\n                                                        (closestLocation === null || closestLocation === void 0 ? void 0 : closestLocation.label) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 border rounded-lg border-outer-space-400 border-dashed bg-card\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_16__.P, {\n                                                                    className: \"mb-2\",\n                                                                    children: [\n                                                                        \"Are you in\",\n                                                                        \" \",\n                                                                        closestLocation === null || closestLocation === void 0 ? void 0 : closestLocation.label,\n                                                                        \"?\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                                                    lineNumber: 988,\n                                                                    columnNumber: 57\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                                                    iconLeft: _barrel_optimize_names_ChevronDown_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                                                                    onClick: handleSetLocation,\n                                                                    className: \"w-full\",\n                                                                    children: [\n                                                                        \"Use\",\n                                                                        \" \",\n                                                                        closestLocation === null || closestLocation === void 0 ? void 0 : closestLocation.label\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                                                    lineNumber: 995,\n                                                                    columnNumber: 57\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                                            lineNumber: 987,\n                                                            columnNumber: 53\n                                                        }, this),\n                                                        !(closestLocation === null || closestLocation === void 0 ? void 0 : closestLocation.label) && (location.latitude !== 0 || location.longitude !== 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 border rounded-lg border-outer-space-400 border-dashed bg-card\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_16__.P, {\n                                                                    className: \"mb-2 font-medium\",\n                                                                    children: \"Using GPS coordinates:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                                                    lineNumber: 1015,\n                                                                    columnNumber: 61\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_16__.P, {\n                                                                    className: \"text-sm font-mono\",\n                                                                    children: [\n                                                                        Number(location.latitude).toFixed(6),\n                                                                        \",\",\n                                                                        \" \",\n                                                                        Number(location.longitude).toFixed(6)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                                                    lineNumber: 1019,\n                                                                    columnNumber: 61\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_16__.P, {\n                                                                    className: \"text-xs text-muted-foreground mt-2\",\n                                                                    children: \"No nearby locations found within range.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                                                    lineNumber: 1028,\n                                                                    columnNumber: 61\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                                            lineNumber: 1014,\n                                                            columnNumber: 57\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                                    lineNumber: 984,\n                                                    columnNumber: 45\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                            lineNumber: 977,\n                                            columnNumber: 37\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                        lineNumber: 973,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                lineNumber: 967,\n                                columnNumber: 29\n                            }, this),\n                            showAddNewLocation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full flex gap-2.5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_15__.RadioGroupItem, {\n                                        size: \"md\",\n                                        value: \"new\",\n                                        id: \"new-location\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                        lineNumber: 1045,\n                                        columnNumber: 33\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                        label: \"Add new location\",\n                                        htmlFor: \"new-location\",\n                                        className: \"text-base w-full font-medium\",\n                                        children: locationOption === \"new\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-grow flex flex-col gap-5 p-4 bg-background rounded-lg border-outer-space-400 border border-dashed\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 gap-5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                                        label: \"Location Name\",\n                                                        htmlFor: \"new-location-title\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_11__.Input, {\n                                                            id: \"new-location-title\",\n                                                            name: \"title\",\n                                                            type: \"text\",\n                                                            placeholder: \"Enter location name\",\n                                                            value: newLocationFormData.title,\n                                                            onChange: handleNewLocationInputChange,\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                                            lineNumber: 1060,\n                                                            columnNumber: 53\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                                        lineNumber: 1057,\n                                                        columnNumber: 49\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                                    lineNumber: 1056,\n                                                    columnNumber: 45\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                                            label: \"Latitude\",\n                                                            htmlFor: \"new-location-latitude\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_11__.Input, {\n                                                                id: \"new-location-latitude\",\n                                                                name: \"latitude\",\n                                                                type: \"number\",\n                                                                value: newLocationFormData.latitude || location.latitude,\n                                                                onChange: handleNewLocationInputChange,\n                                                                placeholder: \"Enter latitude\",\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                                                lineNumber: 1098,\n                                                                columnNumber: 53\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                                            lineNumber: 1095,\n                                                            columnNumber: 49\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                                            label: \"Longitude\",\n                                                            htmlFor: \"new-location-longitude\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_11__.Input, {\n                                                                id: \"new-location-longitude\",\n                                                                name: \"longitude\",\n                                                                type: \"number\",\n                                                                value: newLocationFormData.longitude || location.longitude,\n                                                                onChange: handleNewLocationInputChange,\n                                                                placeholder: \"Enter longitude\",\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                                                lineNumber: 1116,\n                                                                columnNumber: 53\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                                            lineNumber: 1113,\n                                                            columnNumber: 49\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                                    lineNumber: 1094,\n                                                    columnNumber: 45\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                            lineNumber: 1055,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                        lineNumber: 1050,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                lineNumber: 1044,\n                                columnNumber: 29\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                        lineNumber: 833,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                    lineNumber: 831,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                lineNumber: 777,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_16__.AlertDialogNew, {\n                openDialog: openNewLocationDialog,\n                setOpenDialog: setOpenNewLocationDialog,\n                handleCreate: handleCreateNewLocation,\n                actionText: \"Add Location\",\n                title: \"Add New Location\",\n                showIcon: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col gap-5\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 gap-5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                label: \"Location Name\",\n                                htmlFor: \"new-location-title\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_11__.Input, {\n                                    id: \"new-location-title\",\n                                    name: \"title\",\n                                    type: \"text\",\n                                    placeholder: \"Enter location name\",\n                                    value: newLocationFormData.title,\n                                    onChange: handleNewLocationInputChange,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                    lineNumber: 1154,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                lineNumber: 1151,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                htmlFor: \"new-location-latitude\",\n                                label: \"Latitude\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_11__.Input, {\n                                    id: \"new-location-latitude\",\n                                    name: \"latitude\",\n                                    type: \"number\",\n                                    value: newLocationFormData.latitude || location.latitude,\n                                    onChange: handleNewLocationInputChange,\n                                    placeholder: \"Enter latitude\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                    lineNumber: 1177,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                lineNumber: 1176,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                label: \"Longitude\",\n                                htmlFor: \"new-location-longitude\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_11__.Input, {\n                                    id: \"new-location-longitude\",\n                                    name: \"longitude\",\n                                    type: \"number\",\n                                    value: newLocationFormData.longitude || location.longitude,\n                                    onChange: handleNewLocationInputChange,\n                                    placeholder: \"Enter longitude\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                    lineNumber: 1193,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                lineNumber: 1190,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                        lineNumber: 1150,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                    lineNumber: 1149,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                lineNumber: 1142,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_16__.AlertDialogNew, {\n                openDialog: openSetLocationDialog,\n                setOpenDialog: setOpenSetLocationDialog,\n                handleCreate: handleCreateNewLocation,\n                actionText: \"Create Location\",\n                title: \"Current Location\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col gap-5\",\n                    children: [\n                        (closestLocation === null || closestLocation === void 0 ? void 0 : closestLocation.label) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-4\",\n                            children: (closestLocation === null || closestLocation === void 0 ? void 0 : closestLocation.label) != undefined ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_16__.P, {\n                                        className: \"font-medium\",\n                                        children: [\n                                            \"Are you in \",\n                                            closestLocation === null || closestLocation === void 0 ? void 0 : closestLocation.label,\n                                            \"?\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                        lineNumber: 1220,\n                                        columnNumber: 37\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_13__.Button, {\n                                        iconLeft: _barrel_optimize_names_ChevronDown_MapPin_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                                        onClick: handleSetLocation,\n                                        className: \"w-full\",\n                                        children: [\n                                            \"Use \",\n                                            closestLocation === null || closestLocation === void 0 ? void 0 : closestLocation.label\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                        lineNumber: 1223,\n                                        columnNumber: 37\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_14__.Separator, {\n                                        className: \"my-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                        lineNumber: 1229,\n                                        columnNumber: 37\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_16__.P, {\n                                        className: \"uppercase\",\n                                        children: \"Or create new location\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                        lineNumber: 1230,\n                                        columnNumber: 37\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                lineNumber: 1219,\n                                columnNumber: 33\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg\",\n                                children: \"Fetching current location took long, do you want to create a new location instead?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                lineNumber: 1235,\n                                columnNumber: 33\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                            lineNumber: 1217,\n                            columnNumber: 25\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg\",\n                            children: \"Failed to fetch current location. Do you want to create a new location instead?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                            lineNumber: 1242,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                    label: \"Location Name\",\n                                    htmlFor: \"new-location-title\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_11__.Input, {\n                                        id: \"new-location-title\",\n                                        name: \"title\",\n                                        type: \"text\",\n                                        placeholder: \"Enter location name\",\n                                        value: newLocationFormData.title,\n                                        onChange: handleNewLocationInputChange,\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                        lineNumber: 1252,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                    lineNumber: 1249,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                            label: \"Latitude\",\n                                            htmlFor: \"new-location-latitude\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_11__.Input, {\n                                                id: \"new-location-latitude\",\n                                                name: \"latitude\",\n                                                type: \"number\",\n                                                value: newLocationFormData.latitude || location.latitude,\n                                                onChange: handleNewLocationInputChange,\n                                                placeholder: \"Enter latitude\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                                lineNumber: 1278,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                            lineNumber: 1275,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                            label: \"Longitude\",\n                                            htmlFor: \"new-location-longitude\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_11__.Input, {\n                                                id: \"new-location-longitude\",\n                                                name: \"longitude\",\n                                                type: \"number\",\n                                                value: newLocationFormData.longitude || location.longitude,\n                                                onChange: handleNewLocationInputChange,\n                                                placeholder: \"Enter longitude\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                                lineNumber: 1294,\n                                                columnNumber: 33\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                            lineNumber: 1291,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                                    lineNumber: 1274,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                            lineNumber: 1248,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                    lineNumber: 1215,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\location.tsx\",\n                lineNumber: 1209,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(LocationField, \"3Ml+t50dYpF6Si0UIk1ckzWpcWM=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useLazyQuery\n    ];\n});\n_c = LocationField;\nvar _c;\n$RefreshReg$(_c, \"LocationField\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvdWkvbG9nYm9vay9jb21wb25lbnRzL2xvY2F0aW9uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFMEQ7QUFDUDtBQUloQjtBQUN1QjtBQUM1QjtBQUNRO0FBQUE7QUFLTjtBQUN5QztBQUNWO0FBQ0c7QUFDckI7QUFDQTtBQUNFO0FBQ0c7QUFDRztBQUNtQjtBQUNyQjtBQUVwQyxTQUFTMkIsY0FBYyxLQWdCckM7UUFoQnFDLEVBQ2xDQyxrQkFBa0IsRUFDbEJDLG9CQUFvQixFQUNwQkMsWUFBWSxFQUNaQyxVQUFVLEtBQUssRUFDZkMscUJBQXFCLElBQUksRUFDekJDLHFCQUFxQixJQUFJLEVBQ3pCQyxzQkFBc0IsSUFBSSxFQVM3QixHQWhCcUM7O0lBaUJsQyxNQUFNLENBQUNDLGNBQWNDLGdCQUFnQixHQUFHbkMsK0NBQVFBLENBQU0sRUFBRTtJQUN4RCxNQUFNLENBQUNvQyxXQUFXQyxhQUFhLEdBQUdyQywrQ0FBUUEsQ0FBVTtJQUNwRCxNQUFNLENBQUNzQyxhQUFhQyxlQUFlLEdBQUd2QywrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUN3QyxpQkFBaUJDLG1CQUFtQixHQUFHekMsK0NBQVFBLENBQU07SUFDNUQsTUFBTSxDQUFDMEMsa0JBQWtCQyxvQkFBb0IsR0FBRzNDLCtDQUFRQTtJQUN4RCxNQUFNLENBQUM0QyxtQkFBbUJDLHFCQUFxQixHQUFHN0MsK0NBQVFBLENBQU0sRUFBRTtJQUVsRSxNQUFNLENBQUM4Qyx3QkFBd0JDLDBCQUEwQixHQUNyRC9DLCtDQUFRQSxDQUFNO0lBQ2xCLE1BQU0sQ0FBQ2dELFdBQVdDLGFBQWEsR0FBR2pELCtDQUFRQTtJQUMxQyxNQUFNLENBQUNrRCx1QkFBdUJDLHlCQUF5QixHQUNuRG5ELCtDQUFRQSxDQUFVO0lBQ3RCLE1BQU0sQ0FBQ29ELHVCQUF1QkMseUJBQXlCLEdBQ25EckQsK0NBQVFBLENBQVU7SUFDdEIsTUFBTSxDQUFDc0QsVUFBVUMsWUFBWSxHQUFHdkQsK0NBQVFBLENBR3JDO1FBQUV3RCxVQUFVO1FBQUdDLFdBQVc7SUFBRTtJQUMvQixNQUFNQyx3QkFBd0IsSUFBSTdDLDRFQUFxQkE7SUFDdkQsTUFBTThDLG1CQUFtQixJQUFJN0MsdUVBQWdCQTtJQUM3QyxNQUFNOEMscUJBQXFCO1FBQ3ZCckIsZUFBZTtJQUNuQjtJQUVBLE1BQU1zQixrQkFBa0I7UUFDcEJ0QixlQUFlO0lBQ25CO0lBRUEsTUFBTXVCLDBCQUEwQixPQUFPcEI7UUFDbkMsSUFBSSxDQUFDQSxrQkFBa0I7WUFDbkIsZ0NBQWdDO1lBQ2hDQyxvQkFBb0I7WUFDcEJmLHFCQUFxQjtZQUNyQjtRQUNKO1FBRUEsSUFBSWMsaUJBQWlCcUIsS0FBSyxLQUFLLGVBQWU7WUFDMUNoQiwwQkFBMEI7WUFDMUIsdUZBQXVGO1lBQ3ZGaUIsa0JBQWtCO1FBQ3RCLE9BQU87WUFDSHJCLG9CQUFvQkQ7WUFDcEJkLHFCQUFxQmM7WUFDckIsTUFBTXVCLFNBQVNDLGFBQWFDLE9BQU8sQ0FBQztZQUNwQyxJQUFJRixXQUFXLFFBQVEsQ0FBQ0EsU0FBUyxHQUFHO2dCQUNoQyw0Q0FBNEM7Z0JBQzVDLElBQUluQyxTQUFTO29CQUNULCtDQUErQztvQkFDL0MsTUFBTXNDLG1CQUFtQnhCLGtCQUFrQnlCLElBQUksQ0FDM0MsQ0FBQ0MsTUFDR0EsSUFBSUMsYUFBYSxLQUFLN0IsaUJBQWlCcUIsS0FBSztvQkFHcEQsSUFBSUssa0JBQWtCO3dCQUNsQiwyQ0FBMkM7d0JBQzNDLE1BQU1JLGtCQUFrQjs0QkFDcEIsR0FBR0osZ0JBQWdCOzRCQUNuQkssT0FBTyxDQUFDTCxpQkFBaUJLLEtBQUssSUFBSSxLQUFLO3dCQUMzQzt3QkFDQSxNQUFNZixzQkFBc0JnQixJQUFJLENBQUNGO3dCQUVqQyxpQ0FBaUM7d0JBQ2pDLE1BQU1HLG1CQUFtQi9CLGtCQUFrQmdDLEdBQUcsQ0FDMUMsQ0FBQ04sTUFDR0EsSUFBSU8sRUFBRSxLQUFLVCxpQkFBaUJTLEVBQUUsR0FDeEJMLGtCQUNBRjt3QkFFZHpCLHFCQUFxQjhCO29CQUN6QixPQUFPO3dCQUNILHNDQUFzQzt3QkFDdEMsTUFBTUcsY0FBYzs0QkFDaEJELElBQUk5RCxpRkFBZ0JBOzRCQUNwQmdFLFVBQVUsQ0FBQ2IsYUFBYUMsT0FBTyxDQUFDOzRCQUNoQ0ksZUFBZSxDQUFDN0IsaUJBQWlCcUIsS0FBSzs0QkFDdENVLE9BQU87d0JBQ1g7d0JBQ0EsTUFBTWYsc0JBQXNCZ0IsSUFBSSxDQUFDSTt3QkFFakMsaUNBQWlDO3dCQUNqQ2pDLHFCQUFxQjs0QkFDakJpQzsrQkFDR2xDO3lCQUNOO29CQUNMO2dCQUNKLE9BQU87b0JBQ0gsK0NBQStDO29CQUMvQyxNQUFNd0IsbUJBQW1CeEIsa0JBQWtCeUIsSUFBSSxDQUMzQyxDQUFDQyxNQUNHQSxJQUFJQyxhQUFhLEtBQUs3QixpQkFBaUJxQixLQUFLO29CQUdwRGlCLHVCQUF1Qjt3QkFDbkJDLFdBQVc7NEJBQ1BDLE9BQU87Z0NBQ0hILFVBQVUsQ0FBQ2IsYUFBYUMsT0FBTyxDQUFDO2dDQUNoQ0ksZUFBZSxDQUFDN0IsaUJBQWlCcUIsS0FBSzs0QkFDMUM7d0JBQ0o7d0JBQ0FvQixhQUFhLENBQUNDOzRCQUNWLElBQUloQixrQkFBa0I7Z0NBQ2xCLHFFQUFxRTtnQ0FDckUseURBQXlEO2dDQUN6RGlCLHFCQUFxQjtvQ0FDakJKLFdBQVc7d0NBQ1BLLFFBQVEsQ0FBQ3BCLGFBQWFDLE9BQU8sQ0FDekI7b0NBRVI7Z0NBQ0o7NEJBQ0osT0FBTztnQ0FDSCxvREFBb0Q7Z0NBQ3BELE1BQU1XLGNBQWM7b0NBQ2hCRCxJQUFJTyxLQUFLSixzQkFBc0IsQ0FBQ0gsRUFBRTtvQ0FDbENOLGVBQWU3QixpQkFBaUJxQixLQUFLO29DQUNyQ1UsT0FBTztnQ0FDWDtnQ0FDQTVCLHFCQUFxQjtvQ0FDakJpQzt1Q0FDR2xDO2lDQUNOOzRCQUNMO3dCQUNKO29CQUNKO2dCQUNKO1lBQ0o7UUFDSjtJQUNKO0lBRUEsTUFBTSxDQUFDb0MsdUJBQXVCLEdBQUd6RSw0REFBV0EsQ0FBQ0YsNkVBQXNCQSxFQUFFO1FBQ2pFa0YsU0FBUyxDQUFDQztZQUNOQyxRQUFRRCxLQUFLLENBQUMscUNBQXFDQTtRQUN2RDtJQUNKO0lBRUEsTUFBTUUsNkJBQTZCLENBQUNoRDtRQUNoQ0ssMEJBQTBCTDtJQUM5QjtJQUVBLE1BQU1pRCwwQkFBMEI7WUFhZEMsbUJBSUdDLHNCQUNDQztRQWpCbEIsTUFBTUYsYUFBYUcsU0FBU0MsY0FBYyxDQUN0QztRQUVKLE1BQU1ILGdCQUFnQkUsU0FBU0MsY0FBYyxDQUN6QztRQUVKLE1BQU1GLGlCQUFpQkMsU0FBU0MsY0FBYyxDQUMxQztRQUdKLHdCQUF3QjtRQUV4QixNQUFNQyxRQUFRTCxFQUFBQSxvQkFBQUEsV0FBVzdCLEtBQUssY0FBaEI2Qix3Q0FBQUEsa0JBQWtCbEYsSUFBSSxPQUFNO1FBQzFDLE1BQU13RixpQkFBaUJwRCx5QkFDakJBLHVCQUF1QmlCLEtBQUssR0FDNUI7UUFDTixNQUFNUCxXQUFXcUMsRUFBQUEsdUJBQUFBLGNBQWM5QixLQUFLLGNBQW5COEIsMkNBQUFBLHFCQUFxQm5GLElBQUksT0FBTTtRQUNoRCxNQUFNK0MsWUFBWXFDLEVBQUFBLHdCQUFBQSxlQUFlL0IsS0FBSyxjQUFwQitCLDRDQUFBQSxzQkFBc0JwRixJQUFJLE9BQU07UUFFbEQsa0NBQWtDO1FBQ2xDLE1BQU15RixTQUFTQyxXQUFXNUM7UUFDMUIsTUFBTTZDLFVBQVVELFdBQVczQztRQUUzQixNQUFNd0IsWUFBWTtZQUNkQyxPQUFPO2dCQUNIZSxPQUFPQTtnQkFDUEssS0FBS0MsTUFBTUosVUFBVSxJQUFJQTtnQkFDekJLLE1BQU1ELE1BQU1GLFdBQVcsSUFBSUE7Z0JBQzNCSSxrQkFBa0JQO1lBQ3RCO1FBQ0o7UUFFQSxJQUFJcEUsU0FBUztZQUNULE1BQU00RSxXQUFXM0YsaUZBQWdCQTtZQUNqQyxNQUFNcUUsT0FBTyxNQUFNekIsaUJBQWlCZSxJQUFJLENBQUM7Z0JBQ3JDLEdBQUdPLFVBQVVDLEtBQUs7Z0JBQ2xCTCxJQUFJNkI7WUFDUjtZQUNBLElBQUkxRCxDQUFBQSxzQkFBQUEsZ0NBQUFBLFVBQVcyRCxNQUFNLElBQUcsR0FBRztnQkFDdkIxRCxhQUFhO3VCQUFJRDtvQkFBV29DO2lCQUFLO1lBQ3JDLE9BQU87Z0JBQ0huQyxhQUFhO29CQUFDbUM7aUJBQUs7WUFDdkI7WUFFQSxnQ0FBZ0M7WUFDaEMsTUFBTTlDLGNBQWM7Z0JBQ2hCc0UsT0FBT3hCLEtBQUthLEtBQUs7Z0JBQ2pCbEMsT0FBT3FCLEtBQUtQLEVBQUU7Z0JBQ2RyQixVQUFVNEIsS0FBS2tCLEdBQUc7Z0JBQ2xCN0MsV0FBVzJCLEtBQUtvQixJQUFJO1lBQ3hCO1lBRUE3RCxvQkFBb0JMO1lBQ3BCVixxQkFBcUJVO1lBRXJCLDBEQUEwRDtZQUMxRCxNQUFNMkIsU0FBU0MsYUFBYUMsT0FBTyxDQUFDO1lBQ3BDLElBQUlGLFdBQVcsUUFBUSxDQUFDQSxTQUFTLEdBQUc7Z0JBQ2hDLDZFQUE2RTtnQkFDN0UsTUFBTTRDLGFBQWE5RixpRkFBZ0JBO2dCQUNuQyxNQUFNK0QsY0FBYztvQkFDaEJELElBQUlnQztvQkFDSjlCLFVBQVUsQ0FBQ2Q7b0JBQ1hNLGVBQWVhLEtBQUtQLEVBQUU7b0JBQ3RCSixPQUFPO2dCQUNYO2dCQUVBLE1BQU1mLHNCQUFzQmdCLElBQUksQ0FBQ0k7Z0JBRWpDLDZEQUE2RDtnQkFDN0RqQyxxQkFBcUI7b0JBQUNpQzt1QkFBZ0JsQztpQkFBa0I7WUFDNUQ7WUFFQU8seUJBQXlCO1lBQ3pCRSx5QkFBeUI7WUFDekJ5RCxxQkFBcUIsT0FBTyw0QkFBNEI7O1FBQzVELE9BQU87WUFDSEMsa0JBQWtCO2dCQUNkOUI7WUFDSjtRQUNKO0lBQ0o7SUFFQSxNQUFNLENBQUM4QixrQkFBa0IsR0FBR3hHLDREQUFXQSxDQUFDSCwwRUFBbUJBLEVBQUU7UUFDekQrRSxhQUFhLENBQUM2QjtZQUNWLE1BQU01QixPQUFPNEIsU0FBU0QsaUJBQWlCO1lBQ3ZDLElBQUkvRCxDQUFBQSxzQkFBQUEsZ0NBQUFBLFVBQVcyRCxNQUFNLElBQUcsR0FBRztnQkFDdkIxRCxhQUFhO3VCQUFJRDtvQkFBV29DO2lCQUFLO1lBQ3JDLE9BQU87Z0JBQ0huQyxhQUFhO29CQUFDbUM7aUJBQUs7WUFDdkI7WUFFQSxnQ0FBZ0M7WUFDaEMsTUFBTTlDLGNBQWM7Z0JBQ2hCc0UsT0FBT3hCLEtBQUthLEtBQUs7Z0JBQ2pCbEMsT0FBT3FCLEtBQUtQLEVBQUU7Z0JBQ2RyQixVQUFVNEIsS0FBS2tCLEdBQUc7Z0JBQ2xCN0MsV0FBVzJCLEtBQUtvQixJQUFJO1lBQ3hCO1lBRUE3RCxvQkFBb0JMO1lBQ3BCVixxQkFBcUJVO1lBRXJCLDBEQUEwRDtZQUMxRCxNQUFNMkIsU0FBU0MsYUFBYUMsT0FBTyxDQUFDO1lBQ3BDLElBQUlGLFdBQVcsUUFBUSxDQUFDQSxTQUFTLEdBQUc7Z0JBQ2hDZSx1QkFBdUI7b0JBQ25CQyxXQUFXO3dCQUNQQyxPQUFPOzRCQUNISCxVQUFVLENBQUNkOzRCQUNYTSxlQUFlLENBQUNhLEtBQUtQLEVBQUU7d0JBQzNCO29CQUNKO29CQUNBTSxhQUFhLENBQUM4Qjt3QkFDViw2REFBNkQ7d0JBQzdELGtEQUFrRDt3QkFDbEQsTUFBTW5DLGNBQWM7NEJBQ2hCRCxJQUFJb0MsUUFBUWpDLHNCQUFzQixDQUFDSCxFQUFFOzRCQUNyQ04sZUFBZWEsS0FBS1AsRUFBRTs0QkFDdEJKLE9BQU87d0JBQ1g7d0JBQ0E1QixxQkFBcUI7NEJBQ2pCaUM7K0JBQ0dsQzt5QkFDTjtvQkFDTDtnQkFDSjtZQUNKO1lBRUFPLHlCQUF5QjtZQUN6QkUseUJBQXlCO1lBQ3pCeUQscUJBQXFCLE9BQU8sNEJBQTRCOztRQUM1RDtRQUNBdkIsU0FBUyxDQUFDQztZQUNOckMseUJBQXlCO1lBQ3pCRSx5QkFBeUI7WUFDekJ5RCxxQkFBcUIsT0FBTyw0QkFBNEI7O1lBQ3hEckIsUUFBUUQsS0FBSyxDQUFDLDhCQUE4QkE7WUFDNUNoRix5Q0FBS0EsQ0FBQ2dGLEtBQUssQ0FBQztRQUNoQjtJQUNKO0lBQ0EsTUFBTTBCLDhCQUE4QjtRQUNoQyxNQUFNbEUsWUFBWSxNQUFNVSxzQkFBc0J5RCxhQUFhLENBQ3ZELENBQUNqRCxhQUFhQyxPQUFPLENBQUM7UUFFMUJ0QixxQkFBcUJHO0lBQ3pCO0lBQ0EvQyxnREFBU0EsQ0FBQztRQUNOLElBQUk2QixTQUFTO1lBQ1RvRjtRQUNKLE9BQU87WUFDSDdCLHFCQUFxQjtnQkFDakJKLFdBQVc7b0JBQ1BLLFFBQVEsQ0FBQ3BCLGFBQWFDLE9BQU8sQ0FBQztnQkFDbEM7WUFDSjtRQUNKO0lBQ0osR0FBRyxFQUFFO0lBRUwsTUFBTSxDQUFDa0IscUJBQXFCLEdBQUcvRSw2REFBWUEsQ0FBQ00sd0VBQW9CQSxFQUFFO1FBQzlEd0csYUFBYTtRQUNiakMsYUFBYSxDQUFDQztZQUNWLE1BQU1wQyxZQUFZb0MsS0FBS2lDLHFCQUFxQixDQUFDQyxLQUFLO1lBQ2xEekUscUJBQXFCRztRQUN6QjtRQUNBdUMsU0FBUyxDQUFDQztZQUNOQyxRQUFRRCxLQUFLLENBQUMsV0FBV0E7UUFDN0I7SUFDSjtJQUVBLE1BQU0rQixlQUFlO1FBQ2pCLDJFQUEyRTtRQUMzRSxNQUFNQyx1QkFBdUIsQ0FBQ0M7WUFDMUIsT0FBT0EsS0FBS0MsTUFBTSxDQUFDLENBQUNDLE1BQWEsQ0FBQ2xILHFEQUFPQSxDQUFDQyxrREFBSUEsQ0FBQ2lILElBQUlmLEtBQUs7UUFDNUQ7UUFFQSxJQUFJZ0Isa0JBQ0ExRixDQUFBQSx5QkFBQUEsbUNBQUFBLGFBQWMwQyxHQUFHLENBQUMsQ0FBQ3RCLFdBQW1CO2dCQUNsQ3NELE9BQU90RCxTQUFTMkMsS0FBSztnQkFDckJsQyxPQUFPVCxTQUFTdUIsRUFBRTtnQkFDbEJyQixVQUFVRixTQUFTZ0QsR0FBRztnQkFDdEI3QyxXQUFXSCxTQUFTa0QsSUFBSTtZQUM1QixRQUFPLEVBQUU7UUFFYiw4QkFBOEI7UUFDOUIsSUFBSXFCLG9CQUFvQixFQUFFO1FBQzFCLElBQUkzRixnQkFBZ0JjLENBQUFBLHNCQUFBQSxnQ0FBQUEsVUFBVzJELE1BQU0sSUFBRyxHQUFHO1lBQ3ZDa0Isb0JBQW9CO21CQUFJN0U7bUJBQWM0RTthQUFnQjtRQUMxRCxPQUFPO1lBQ0hDLG9CQUFvQjttQkFBSUQ7YUFBZ0I7UUFDNUM7UUFFQSx3REFBd0Q7UUFDeEQsTUFBTUUsaUJBQWlCTixxQkFBcUJLO1FBRTVDLDhCQUE4QjtRQUM5QixPQUFPRSxjQUFjRDtJQUN6QjtJQUVBLE1BQU1DLGdCQUFnQixDQUFDL0U7UUFDbkIsd0RBQXdEO1FBQ3hELElBQUksQ0FBQ0EsYUFBYUEsVUFBVTJELE1BQU0sS0FBSyxHQUFHO1lBQ3RDLE9BQU8sRUFBRTtRQUNiO1FBRUEsNERBQTREO1FBQzVELE1BQU1xQixrQkFBa0I7ZUFBSWhGO1NBQVU7UUFFdEMsSUFBSUosa0JBQWtCK0QsTUFBTSxHQUFHLEdBQUc7WUFDOUIsT0FBT3FCLGdCQUFnQkMsSUFBSSxDQUFDLENBQUNDLEdBQVFDO2dCQUNqQyxrQ0FBa0M7Z0JBQ2xDLE1BQU1DLE9BQU94RixrQkFBa0J5QixJQUFJLENBQy9CLENBQUNDLE1BQWFBLElBQUlDLGFBQWEsS0FBSzJELEVBQUVuRSxLQUFLO2dCQUUvQyxNQUFNc0UsT0FBT3pGLGtCQUFrQnlCLElBQUksQ0FDL0IsQ0FBQ0MsTUFBYUEsSUFBSUMsYUFBYSxLQUFLNEQsRUFBRXBFLEtBQUs7Z0JBRy9DLHdGQUF3RjtnQkFDeEYsSUFBSXFFLFFBQVFDLE1BQU07b0JBQ2QsT0FBT0EsS0FBSzVELEtBQUssR0FBRzJELEtBQUszRCxLQUFLO2dCQUNsQyxPQUVLLElBQUkyRCxNQUFNO29CQUNYLE9BQU8sQ0FBQztnQkFDWixPQUVLLElBQUlDLE1BQU07b0JBQ1gsT0FBTztnQkFDWDtnQkFDQSxPQUFPO1lBQ1g7UUFDSjtRQUVBLE9BQU9MO0lBQ1g7SUFFQSxNQUFNTSxlQUFlLENBQUNoRixVQUFlRSxVQUFlQztRQUNoRCxNQUFNOEUsV0FBV0MsS0FBS0MsSUFBSSxDQUN0QkQsS0FBS0UsR0FBRyxDQUFDcEYsU0FBU2dELEdBQUcsR0FBRzlDLFVBQVUsS0FDOUJnRixLQUFLRSxHQUFHLENBQUNwRixTQUFTa0QsSUFBSSxHQUFHL0MsV0FBVztRQUU1QyxPQUFPOEU7SUFDWDtJQUVBLE1BQU1JLHNCQUFzQixDQUFDbkYsVUFBZUM7UUFDeEMsbUVBQW1FO1FBQ25FLElBQUl2QixhQUFheUUsTUFBTSxLQUFLLEdBQUc7WUFDM0JsRSxtQkFBbUI7WUFDbkIsbURBQW1EO1lBQ25EbUcsK0JBQStCcEYsVUFBVUM7WUFDekM7UUFDSjtRQUVBLE1BQU1qQixrQkFBa0JOLGFBQWEyRyxNQUFNLENBQUMsQ0FBQ0MsTUFBV0M7WUFDcEQsTUFBTUMsZUFBZVIsS0FBS0MsSUFBSSxDQUMxQkQsS0FBS0UsR0FBRyxDQUFDSSxLQUFLeEMsR0FBRyxHQUFHOUMsVUFBVSxLQUMxQmdGLEtBQUtFLEdBQUcsQ0FBQ0ksS0FBS3RDLElBQUksR0FBRy9DLFdBQVc7WUFFeEMsTUFBTXdGLGVBQWVULEtBQUtDLElBQUksQ0FDMUJELEtBQUtFLEdBQUcsQ0FBQ0ssS0FBS3pDLEdBQUcsR0FBRzlDLFVBQVUsS0FDMUJnRixLQUFLRSxHQUFHLENBQUNLLEtBQUt2QyxJQUFJLEdBQUcvQyxXQUFXO1lBRXhDLE9BQU91RixlQUFlQyxlQUFlSCxPQUFPQztRQUNoRDtRQUVBLE1BQU1HLFlBQVlaLGFBQWE5RixpQkFBaUJnQixVQUFVQztRQUMxRCwwREFBMEQ7UUFDMUQsSUFDSXlGLGFBQWEsUUFDYjFHLGdCQUFnQjhELEdBQUcsS0FBSyxLQUN4QjlELGdCQUFnQmdFLElBQUksS0FBSyxHQUMzQjtZQUNFLHlDQUF5QztZQUN6Qy9ELG1CQUFtQjtnQkFDZm1FLE9BQU9wRSxnQkFBZ0J5RCxLQUFLO2dCQUM1QmxDLE9BQU92QixnQkFBZ0JxQyxFQUFFO2dCQUN6QnJCLFVBQVVoQixnQkFBZ0I4RCxHQUFHO2dCQUM3QjdDLFdBQVdqQixnQkFBZ0JnRSxJQUFJO1lBQ25DO1lBRUFuRCx5QkFBeUI7UUFDN0IsT0FBTztZQUNILHFEQUFxRDtZQUNyRFosbUJBQW1CO1lBQ25CbUcsK0JBQStCcEYsVUFBVUM7UUFDN0M7SUFDSjtJQUVBLHFFQUFxRTtJQUNyRSxNQUFNbUYsaUNBQWlDLENBQUNwRixVQUFlQztRQUNuRCw0REFBNEQ7UUFDNURkLG9CQUFvQjtRQUVwQix1Q0FBdUM7UUFDdkNmLHFCQUFxQjtZQUNqQjRCLFVBQVUsQ0FBQ0E7WUFDWEMsV0FBVyxDQUFDQTtRQUNoQjtJQUNKO0lBRUEsTUFBTTBGLDJCQUEyQjtRQUM3QixJQUFJLGlCQUFpQkMsV0FBVztZQUM1QixNQUFNQyxVQUFVO2dCQUNaQyxTQUFTO1lBQ2I7WUFDQUYsVUFBVUcsV0FBVyxDQUFDQyxrQkFBa0IsQ0FDcEM7b0JBQUMsRUFBRUMsTUFBTSxFQUFFO2dCQUNQLE1BQU0sRUFBRWpHLFFBQVEsRUFBRUMsU0FBUyxFQUFFLEdBQUdnRztnQkFDaENsRyxZQUFZO29CQUFFQztvQkFBVUM7Z0JBQVU7Z0JBQ2xDOUIsbUJBQW1CO29CQUNmNkIsVUFBVSxDQUFDQTtvQkFDWEMsV0FBVyxDQUFDQTtnQkFDaEI7Z0JBRUE3QixxQkFBcUI7b0JBQ2pCNEIsVUFBVSxDQUFDQTtvQkFDWEMsV0FBVyxDQUFDQTtnQkFDaEI7Z0JBQ0EsOEJBQThCO2dCQUM5QmtGLG9CQUFvQm5GLFVBQVVDO1lBQ2xDLEdBQ0EsQ0FBQytCO2dCQUNHLElBQUlrRSxlQUFlO2dCQUNuQixJQUFJbEUsTUFBTW1FLElBQUksS0FBS25FLE1BQU1vRSxpQkFBaUIsRUFBRTtvQkFDeENGLGVBQ0k7Z0JBQ1IsT0FBTyxJQUFJbEUsTUFBTW1FLElBQUksS0FBS25FLE1BQU1xRSxvQkFBb0IsRUFBRTtvQkFDbERILGVBQWU7Z0JBQ25CLE9BQU8sSUFBSWxFLE1BQU1tRSxJQUFJLEtBQUtuRSxNQUFNc0UsT0FBTyxFQUFFO29CQUNyQ0osZUFBZTtnQkFDbkI7Z0JBRUFsSix5Q0FBS0EsQ0FBQ2dGLEtBQUssQ0FBQ2tFO1lBQ2hCLEdBQ0FMO1FBRVIsT0FBTztZQUNIN0kseUNBQUtBLENBQUNnRixLQUFLLENBQUM7UUFDaEI7SUFDSjtJQUVBLE1BQU11RSxvQkFBb0I7UUFDdEJwSCxvQkFBb0JIO1FBQ3BCWixxQkFBcUJZO1FBQ3JCc0UscUJBQXFCO1FBQ3JCekQseUJBQXlCO0lBQzdCO0lBRUEsTUFBTTJHLDRCQUE0QjtRQUM5QixNQUFNeEcsV0FBVyxTQUNKd0MsY0FBYyxDQUFDLHFCQUMxQmpDLEtBQUs7UUFDUCxNQUFNTixZQUFZLFNBQ0x1QyxjQUFjLENBQUMsc0JBQzFCakMsS0FBSztRQUVQLHFCQUFxQjtRQUNyQlIsWUFBWTtZQUFFQyxVQUFVLENBQUNBO1lBQVVDLFdBQVcsQ0FBQ0E7UUFBVTtRQUV6RCxnQ0FBZ0M7UUFDaEM5QixtQkFBbUI7WUFDZjZCLFVBQVUsQ0FBQ0E7WUFDWEMsV0FBVyxDQUFDQTtRQUNoQjtRQUVBLDREQUE0RDtRQUM1RGQsb0JBQW9CO0lBQ3hCO0lBRUEsTUFBTSxDQUFDc0gsZ0JBQWdCLEdBQUczSiw2REFBWUEsQ0FBQ0sscUVBQWlCQSxFQUFFO1FBQ3REeUcsYUFBYTtRQUNiakMsYUFBYSxDQUFDNkI7WUFDVixNQUFNNUIsT0FBTzRCLFNBQVNrRCxnQkFBZ0IsQ0FBQzVDLEtBQUs7WUFDNUMsSUFBSWxDLE1BQU07Z0JBQ05qRCxnQkFBZ0JpRDtZQUNwQjtRQUNKO1FBQ0FHLFNBQVMsQ0FBQ0M7WUFDTkMsUUFBUUQsS0FBSyxDQUFDLDJCQUEyQkE7UUFDN0M7SUFDSjtJQUNBLE1BQU0yRSxtQkFBbUI7UUFDckIsSUFBSXJJLFNBQVM7WUFDVCxNQUFNc0QsT0FBTyxNQUFNekIsaUJBQWlCeUcsTUFBTTtZQUMxQyxJQUFJaEYsTUFBTTtnQkFDTmpELGdCQUFnQmlEO1lBQ3BCO1FBQ0osT0FBTztZQUNILE1BQU02RTtRQUNWO0lBQ0o7SUFFQWhLLGdEQUFTQSxDQUFDO1FBQ04sSUFBSW1DLFdBQVc7WUFDWCtIO1lBQ0E5SCxhQUFhO1FBQ2pCO0lBQ0osR0FBRztRQUFDRDtLQUFVO0lBRWRuQyxnREFBU0EsQ0FBQztRQUNOLElBQUk0QixjQUFjO1lBQ2QsSUFBSSxFQUFDQSx5QkFBQUEsbUNBQUFBLGFBQWMwQyxhQUFhLElBQUcsR0FBRztnQkFDbENyQyx5QkFBQUEsbUNBQUFBLGFBQWNtQyxJQUFJLENBQUMsQ0FBQ2Y7b0JBQ2hCLElBQUlBLFNBQVN1QixFQUFFLEtBQUtoRCxhQUFhMEMsYUFBYSxFQUFFO3dCQUM1QzVCLG9CQUFvQjs0QkFDaEJpRSxPQUFPdEQsU0FBUzJDLEtBQUs7NEJBQ3JCbEMsT0FBT1QsU0FBU3VCLEVBQUU7NEJBQ2xCckIsVUFBVUYsU0FBU2dELEdBQUc7NEJBQ3RCN0MsV0FBV0gsU0FBU2tELElBQUk7d0JBQzVCO29CQUNKO2dCQUNKO2dCQUNBLHlEQUF5RDtnQkFDekRqRCxZQUFZO29CQUFFQyxVQUFVO29CQUFHQyxXQUFXO2dCQUFFO1lBQzVDLE9BQU87Z0JBQ0gsSUFBSTVCLENBQUFBLHlCQUFBQSxtQ0FBQUEsYUFBY3lFLEdBQUcsTUFBSXpFLHlCQUFBQSxtQ0FBQUEsYUFBYzJFLElBQUksR0FBRTtvQkFDekMsOENBQThDO29CQUM5Q2pELFlBQVk7d0JBQ1JDLFVBQVUzQixhQUFheUUsR0FBRzt3QkFDMUI3QyxXQUFXNUIsYUFBYTJFLElBQUk7b0JBQ2hDO29CQUNBN0Qsb0JBQW9CO2dCQUN4QjtZQUNKO1FBQ0o7SUFDSixHQUFHO1FBQUNkO1FBQWNLO0tBQWE7SUFFL0IsNkNBQTZDO0lBQzdDakMsZ0RBQVNBLENBQUM7UUFDTix5REFBeUQ7UUFDekRvSyx1QkFBdUIsQ0FBQ3ZCLE9BQVU7Z0JBQzlCLEdBQUdBLElBQUk7Z0JBQ1B0RixVQUFVRixTQUFTRSxRQUFRLEdBQUdGLFNBQVNFLFFBQVEsQ0FBQzhHLFFBQVEsS0FBSztnQkFDN0Q3RyxXQUFXSCxTQUFTRyxTQUFTLEdBQUdILFNBQVNHLFNBQVMsQ0FBQzZHLFFBQVEsS0FBSztZQUNwRTtRQUVBLHdDQUF3QztRQUN4Q0MsV0FBV0MseUJBQXlCO0lBQ3hDLEdBQUc7UUFBQ2xIO0tBQVM7SUFDYixnREFBZ0Q7SUFDaEQsTUFBTW1ILGNBQWN2Syw2Q0FBTUEsQ0FBMEI7SUFDcEQsTUFBTXdLLGVBQWV4Syw2Q0FBTUEsQ0FBMEI7SUFFckQsd0NBQXdDO0lBQ3hDLE1BQU0sQ0FBQ3lLLG1CQUFtQjdELHFCQUFxQixHQUFHOUcsK0NBQVFBLENBQVU7SUFDcEUsTUFBTSxDQUFDNEssZ0JBQWdCNUcsa0JBQWtCLEdBQUdoRSwrQ0FBUUEsQ0FBUztJQUM3RCx1RkFBdUY7SUFDdkYsTUFBTSxDQUFDNksscUJBQXFCUix1QkFBdUIsR0FBR3JLLCtDQUFRQSxDQUFDO1FBQzNEaUcsT0FBTztRQUNQekMsVUFBVTtRQUNWQyxXQUFXO0lBQ2Y7SUFFQSw2Q0FBNkM7SUFDN0MsTUFBTXFILDZCQUE2QixDQUFDL0c7UUFDaENDLGtCQUFrQkQ7UUFFbEIsMkNBQTJDO1FBQzNDLElBQUlBLFVBQVUsZUFBZTtZQUN6Qix5QkFBeUI7WUFDekJ4QixlQUFlO1lBQ2YsdURBQXVEO1lBQ3ZESSxvQkFBb0I7UUFDeEIsT0FBTztZQUNILElBQUlvQixVQUFVLFdBQVc7Z0JBQ3JCLHFDQUFxQztnQkFDckNvRjtZQUNKLE9BQU8sSUFBSXBGLFVBQVUsT0FBTztnQkFDeEIsOENBQThDO2dCQUM5Q3hCLGVBQWU7WUFDZix1Q0FBdUM7WUFDdkMsaUNBQWlDO1lBQ3JDO1FBQ0o7SUFDSjtJQUVBLDBDQUEwQztJQUMxQyxNQUFNd0kseUJBQXlCO1FBQzNCLElBQUlySSw2QkFBQUEsdUNBQUFBLGlCQUFrQmtFLEtBQUssRUFBRTtZQUN6QixPQUFPbEUsaUJBQWlCa0UsS0FBSztRQUNqQyxPQUFPLElBRUgsZ0RBRGdEO1FBQy9DdEQsU0FBU0UsUUFBUSxLQUFLd0gsYUFDbkIxSCxTQUFTRSxRQUFRLEtBQUssUUFDdEJGLFNBQVNFLFFBQVEsS0FBSyxLQUN6QkYsU0FBU0csU0FBUyxLQUFLdUgsYUFDcEIxSCxTQUFTRyxTQUFTLEtBQUssUUFDdkJILFNBQVNHLFNBQVMsS0FBSyxHQUM3QjtZQUNFLHNFQUFzRTtZQUN0RSxPQUFPLEdBQTRDd0gsT0FBekNBLE9BQU8zSCxTQUFTRSxRQUFRLEVBQUUwSCxPQUFPLENBQUMsSUFBRyxNQUEwQyxPQUF0Q0QsT0FBTzNILFNBQVNHLFNBQVMsRUFBRXlILE9BQU8sQ0FBQztRQUMxRixPQUFPO1lBQ0gsT0FBTztRQUNYO0lBQ0o7SUFFQSxnREFBZ0Q7SUFDaEQsTUFBTUMsb0JBQW9CO1FBQ3RCLElBQUlQLG1CQUFtQixlQUFlO1lBQ2xDLHlDQUF5QztZQUN6Qy9HO1lBQ0EsZ0RBQWdEO1lBQ2hEbEIsb0JBQW9CO1lBQ3BCLHVDQUF1QztZQUN2Q2YscUJBQXFCO2dCQUNqQjRCLFVBQVVGLFNBQVNFLFFBQVE7Z0JBQzNCQyxXQUFXSCxTQUFTRyxTQUFTO1lBQ2pDO1FBQ0o7UUFDQXFELHFCQUFxQjtJQUN6QjtJQUVBLGlDQUFpQztJQUNqQyxNQUFNMEQsMEJBQTBCO1FBQzVCLE1BQU0sRUFBRXZFLEtBQUssRUFBRXpDLFFBQVEsRUFBRUMsU0FBUyxFQUFFLEdBQUdvSDtRQUN2QyxNQUFNTyxVQUNGbkYsTUFBTXZGLElBQUksT0FBTyxNQUNqQjhDLFNBQVM5QyxJQUFJLE9BQU8sTUFDcEIrQyxVQUFVL0MsSUFBSSxPQUFPLE1BQ3JCLENBQUM2RixNQUFNSCxXQUFXNUMsY0FDbEIsQ0FBQytDLE1BQU1ILFdBQVczQztRQUV0QixPQUFPMkg7SUFDWDtJQUVBLGlEQUFpRDtJQUNqRCxNQUFNQywrQkFBK0IsQ0FDakNDO1FBRUEsTUFBTSxFQUFFQyxJQUFJLEVBQUV4SCxLQUFLLEVBQUUsR0FBR3VILEVBQUVFLE1BQU07UUFDaENuQix1QkFBdUIsQ0FBQ3ZCLE9BQVU7Z0JBQzlCLEdBQUdBLElBQUk7Z0JBQ1AsQ0FBQ3lDLEtBQUssRUFBRXhIO1lBQ1o7UUFFQSw2REFBNkQ7UUFDN0R3RyxXQUFXQyx5QkFBeUI7SUFDeEM7SUFFQSxnREFBZ0Q7SUFDaEQsTUFBTWlCLG9CQUFvQjtRQUN0QixrQ0FBa0M7UUFDbEMsSUFBSWpCLDJCQUEyQjtZQUMzQixpRkFBaUY7WUFDakYsTUFBTTVFLGFBQWFHLFNBQVNDLGNBQWMsQ0FDdEM7WUFFSixNQUFNMEYsV0FBVzNGLFNBQVNDLGNBQWMsQ0FDcEM7WUFFSixNQUFNMkYsWUFBWTVGLFNBQVNDLGNBQWMsQ0FDckM7WUFHSixJQUFJSixjQUFjOEYsWUFBWUMsV0FBVztnQkFDckMvRixXQUFXN0IsS0FBSyxHQUFHOEcsb0JBQW9CNUUsS0FBSztnQkFDNUN5RixTQUFTM0gsS0FBSyxHQUFHOEcsb0JBQW9CckgsUUFBUTtnQkFDN0NtSSxVQUFVNUgsS0FBSyxHQUFHOEcsb0JBQW9CcEgsU0FBUztZQUNuRDtZQUVBa0M7WUFDQW1CLHFCQUFxQjtRQUN6QixPQUFPO1lBQ0h0Ryx5Q0FBS0EsQ0FBQ2dGLEtBQUssQ0FBQztRQUNoQjtJQUNKO0lBRUEsb0RBQW9EO0lBQ3BELE1BQU1vRyxrQ0FBa0M5SDtJQUN4QyxNQUFNK0gsbUNBQW1DLENBQUNuSjtRQUN0Q2tKLGdDQUFnQ2xKO1FBQ2hDLElBQUlBLG9CQUFvQkEsaUJBQWlCcUIsS0FBSyxLQUFLLGVBQWU7WUFDOUQrQyxxQkFBcUI7UUFDekI7SUFDSjtJQUVBLHFCQUNJOzswQkFFSSw4REFBQzVGLDBEQUFNQTtnQkFDSDRLLHlCQUFXLDhEQUFDMUssK0ZBQVdBO29CQUFDMkssV0FBVTs7Ozs7O2dCQUNsQ0EsV0FBVTtnQkFDVkMsU0FBUTtnQkFDUkMsU0FBUztvQkFDTGpJLGtCQUFrQjtvQkFDbEI4QyxxQkFBcUI7Z0JBQ3pCOzBCQUNDaUU7Ozs7OzswQkFJTCw4REFBQ3ZKLDJEQUFjQTtnQkFDWDBLLFlBQVl2QjtnQkFDWndCLGVBQWVyRjtnQkFDZnNGLGNBQWM7b0JBQ1YsSUFBSXhCLG1CQUFtQixlQUFlO3dCQUNsQ087b0JBQ0osT0FBTyxJQUFJUCxtQkFBbUIsV0FBVzt3QkFDckMsNkVBQTZFO3dCQUM3RSxJQUNJLEVBQUNwSSw0QkFBQUEsc0NBQUFBLGdCQUFpQm9FLEtBQUssS0FDdEJ0RCxDQUFBQSxTQUFTRSxRQUFRLEtBQUssS0FDbkJGLFNBQVNHLFNBQVMsS0FBSyxJQUM3Qjs0QkFDRSxtRkFBbUY7NEJBQ25GcUQscUJBQXFCO3dCQUNyQix1Q0FBdUM7d0JBQ3ZDLDRGQUE0Rjt3QkFDNUYscUNBQXFDO3dCQUNyQyxTQUFTO3dCQUNiLE9BQU87NEJBQ0gsa0VBQWtFOzRCQUNsRUEscUJBQXFCO3dCQUN6QjtvQkFDSixPQUFPLElBQUk4RCxtQkFBbUIsT0FBTzt3QkFDakMsbURBQW1EO3dCQUNuRCxJQUFJSiwyQkFBMkI7NEJBQzNCaUI7d0JBQ0osT0FBTzs0QkFDSGpMLHlDQUFLQSxDQUFDZ0YsS0FBSyxDQUNQOzRCQUVKLG1DQUFtQzs0QkFDbkM7d0JBQ0o7b0JBQ0osT0FBTzt3QkFDSCxtQ0FBbUM7d0JBQ25Dc0IscUJBQXFCO29CQUN6QjtnQkFDSjtnQkFDQXVGLFlBQ0l6QixtQkFBbUIsZ0JBQ2IsaUJBQ0FBLG1CQUFtQixZQUNqQixFQUFDcEksNEJBQUFBLHNDQUFBQSxnQkFBaUJvRSxLQUFLLEtBQ3RCdEQsQ0FBQUEsU0FBU0UsUUFBUSxLQUFLLEtBQ25CRixTQUFTRyxTQUFTLEtBQUssS0FDdkIsd0JBQ0EsaUJBQ0ptSCxtQkFBbUIsUUFDakIsaUJBQ0E7Z0JBRWQzRSxPQUFNO2dCQUNOcUcsTUFBSzswQkFDTCw0RUFBQ0M7b0JBQUlSLFdBQVU7OEJBRVgsNEVBQUN6SyxtRUFBVUE7d0JBQ1B5QyxPQUFPNkc7d0JBQ1A0QixlQUFlMUI7d0JBQ2ZpQixXQUFVOzswQ0FFViw4REFBQ1E7Z0NBQUlSLFdBQVU7O2tEQUNYLDhEQUFDeEssdUVBQWNBO3dDQUNYK0ssTUFBSzt3Q0FDTHZJLE9BQU07d0NBQ05jLElBQUc7Ozs7OztrREFFUCw4REFBQzVELHdEQUFLQTt3Q0FDRjhLLFdBQVU7d0NBQ1ZuRixPQUFNO3dDQUNONkYsU0FBUTtrREFDUDdCLG1CQUFtQiwwQkFDaEIsOERBQUMyQjs0Q0FBSVIsV0FBVTtzREFDWCw0RUFBQzVMLDZEQUFRQTtnREFDTGtKLFNBQ0luSCxlQUNNO29EQUNJO3dEQUNJMEUsT0FBTzt3REFDUDdDLE9BQU87b0RBQ1g7dURBQ0d3RDtpREFDTixHQUNEO29EQUNJO3dEQUNJWCxPQUFPO3dEQUNQN0MsT0FBTztvREFDWDtpREFDSDtnREFFWEEsT0FBT3JCO2dEQUNQZ0ssVUFDSWI7Z0RBRUpjLGFBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7NEJBUS9CM0ssb0NBQ0csOERBQUN1SztnQ0FBSVIsV0FBVTs7a0RBQ1gsOERBQUN4Syx1RUFBY0E7d0NBQ1grSyxNQUFLO3dDQUNMdkksT0FBTTt3Q0FDTmMsSUFBRzs7Ozs7O2tEQUVQLDhEQUFDNUQsd0RBQUtBO3dDQUNGMkYsT0FBTTt3Q0FDTjZGLFNBQVE7d0NBQ1JWLFdBQVU7a0RBQ1RuQixtQkFBbUIsK0JBQ2hCLDhEQUFDMkI7NENBQUlSLFdBQVU7c0RBQ1gsNEVBQUNRO2dEQUFJUixXQUFVOztrRUFDWCw4REFBQ1E7d0RBQUlSLFdBQVU7OzBFQUNYLDhEQUFDOUssd0RBQUtBO2dFQUNGMkYsT0FBTTtnRUFDTjZGLFNBQVE7MEVBQ1IsNEVBQUN6TCx3REFBS0E7b0VBQ0Y2RCxJQUFHO29FQUNIMEcsTUFBSztvRUFDTHFCLE1BQUs7b0VBQ0xDLEtBQUtwQztvRUFDTDFHLE9BQ0lULFNBQVNFLFFBQVE7b0VBRXJCa0osVUFDSTFDO29FQUVKMkMsYUFBWTtvRUFDWlosV0FBVTtvRUFDVmUsUUFBUTs7Ozs7Ozs7Ozs7MEVBSWhCLDhEQUFDN0wsd0RBQUtBO2dFQUNGMkYsT0FBTTtnRUFDTjZGLFNBQVE7MEVBQ1IsNEVBQUN6TCx3REFBS0E7b0VBQ0Y2RCxJQUFHO29FQUNIMEcsTUFBSztvRUFDTHFCLE1BQUs7b0VBQ0xDLEtBQUtuQztvRUFDTDNHLE9BQ0lULFNBQVNHLFNBQVM7b0VBRXRCaUosVUFDSTFDO29FQUVKMkMsYUFBWTtvRUFDWlosV0FBVTtvRUFDVmUsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBSXBCLDhEQUFDUDt3REFBSVIsV0FBVTtrRUFDWCw0RUFBQ2dCOzREQUFFaEIsV0FBVTtzRUFBdUM7Ozs7Ozs7Ozs7O29EQU90RHpJLENBQUFBLFNBQVNFLFFBQVEsS0FBSyxLQUNwQkYsU0FBU0csU0FBUyxLQUNkLG9CQUNKLDhEQUFDOEk7d0RBQUlSLFdBQVU7a0VBQ1gsNEVBQUNnQjs7Z0VBQUU7Z0VBQ3NCO2dFQUNwQjlCLE9BQ0czSCxTQUFTRSxRQUFRLEVBQ25CMEgsT0FBTyxDQUFDO2dFQUFHO2dFQUNYO2dFQUNERCxPQUNHM0gsU0FBU0csU0FBUyxFQUNwQnlILE9BQU8sQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs0QkFZN0NqSixxQ0FDRyw4REFBQ3NLO2dDQUFJUixXQUFVOztrREFDWCw4REFBQ3hLLHVFQUFjQTt3Q0FDWCtLLE1BQUs7d0NBQ0x2SSxPQUFNO3dDQUNOYyxJQUFHOzs7Ozs7a0RBRVAsOERBQUM1RCx3REFBS0E7d0NBQ0YyRixPQUFNO3dDQUNONkYsU0FBUTt3Q0FDUlYsV0FBVTtrREFDViw0RUFBQ1E7OzhEQUNHLDhEQUFDUTtvREFBRWhCLFdBQVU7OERBQWdDOzs7Ozs7Z0RBSzVDbkIsbUJBQW1CLDJCQUNoQiw4REFBQzJCO29EQUFJUixXQUFVOzt3REFFVnZKLENBQUFBLDRCQUFBQSxzQ0FBQUEsZ0JBQWlCb0UsS0FBSyxtQkFDbkIsOERBQUMyRjs0REFBSVIsV0FBVTs7OEVBQ1gsOERBQUN0Syw4Q0FBQ0E7b0VBQUNzSyxXQUFVOzt3RUFBTzt3RUFDTDt3RUFFUHZKLDRCQUFBQSxzQ0FBQUEsZ0JBQWlCb0UsS0FBSzt3RUFDekI7Ozs7Ozs7OEVBR0wsOERBQUMxRiwwREFBTUE7b0VBQ0g4TCxVQUFVN0wsK0ZBQU1BO29FQUNoQjhLLFNBQ0lsQztvRUFFSmdDLFdBQVU7O3dFQUFTO3dFQUNmO3dFQUVBdkosNEJBQUFBLHNDQUFBQSxnQkFBaUJvRSxLQUFLOzs7Ozs7Ozs7Ozs7O3dEQU9yQyxFQUFDcEUsNEJBQUFBLHNDQUFBQSxnQkFBaUJvRSxLQUFLLEtBQ25CdEQsQ0FBQUEsU0FBU0UsUUFBUSxLQUFLLEtBQ25CRixTQUFTRyxTQUFTLEtBQ2Qsb0JBQ0osOERBQUM4STs0REFBSVIsV0FBVTs7OEVBQ1gsOERBQUN0Syw4Q0FBQ0E7b0VBQUNzSyxXQUFVOzhFQUFtQjs7Ozs7OzhFQUloQyw4REFBQ3RLLDhDQUFDQTtvRUFBQ3NLLFdBQVU7O3dFQUNSZCxPQUNHM0gsU0FBU0UsUUFBUSxFQUNuQjBILE9BQU8sQ0FBQzt3RUFBRzt3RUFDWDt3RUFDREQsT0FDRzNILFNBQVNHLFNBQVMsRUFDcEJ5SCxPQUFPLENBQUM7Ozs7Ozs7OEVBRWQsOERBQUN6Siw4Q0FBQ0E7b0VBQUNzSyxXQUFVOzhFQUFxQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7NEJBZXJGaEssb0NBQ0csOERBQUN3SztnQ0FBSVIsV0FBVTs7a0RBQ1gsOERBQUN4Syx1RUFBY0E7d0NBQ1grSyxNQUFLO3dDQUNMdkksT0FBTTt3Q0FDTmMsSUFBRzs7Ozs7O2tEQUVQLDhEQUFDNUQsd0RBQUtBO3dDQUNGMkYsT0FBTTt3Q0FDTjZGLFNBQVE7d0NBQ1JWLFdBQVU7a0RBQ1RuQixtQkFBbUIsdUJBQ2hCLDhEQUFDMkI7NENBQUlSLFdBQVU7OzhEQUNYLDhEQUFDUTtvREFBSVIsV0FBVTs4REFDWCw0RUFBQzlLLHdEQUFLQTt3REFDRjJGLE9BQU07d0RBQ042RixTQUFRO2tFQUNSLDRFQUFDekwsd0RBQUtBOzREQUNGNkQsSUFBRzs0REFDSDBHLE1BQUs7NERBQ0xxQixNQUFLOzREQUNMRCxhQUFZOzREQUNaNUksT0FDSThHLG9CQUFvQjVFLEtBQUs7NERBRTdCeUcsVUFDSXJCOzREQUVKeUIsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs4REF1QnBCLDhEQUFDUDtvREFBSVIsV0FBVTs7c0VBQ1gsOERBQUM5Syx3REFBS0E7NERBQ0YyRixPQUFNOzREQUNONkYsU0FBUTtzRUFDUiw0RUFBQ3pMLHdEQUFLQTtnRUFDRjZELElBQUc7Z0VBQ0gwRyxNQUFLO2dFQUNMcUIsTUFBSztnRUFDTDdJLE9BQ0k4RyxvQkFBb0JySCxRQUFRLElBQzVCRixTQUFTRSxRQUFRO2dFQUVyQmtKLFVBQ0lyQjtnRUFFSnNCLGFBQVk7Z0VBQ1pHLFFBQVE7Ozs7Ozs7Ozs7O3NFQUdoQiw4REFBQzdMLHdEQUFLQTs0REFDRjJGLE9BQU07NERBQ042RixTQUFRO3NFQUNSLDRFQUFDekwsd0RBQUtBO2dFQUNGNkQsSUFBRztnRUFDSDBHLE1BQUs7Z0VBQ0xxQixNQUFLO2dFQUNMN0ksT0FDSThHLG9CQUFvQnBILFNBQVMsSUFDN0JILFNBQVNHLFNBQVM7Z0VBRXRCaUosVUFDSXJCO2dFQUVKc0IsYUFBWTtnRUFDWkcsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBY3BELDhEQUFDdEwsMkRBQWNBO2dCQUNYMEssWUFBWWhKO2dCQUNaaUosZUFBZWhKO2dCQUNmaUosY0FBY3pHO2dCQUNkMEcsWUFBVztnQkFDWHBHLE9BQU07Z0JBQ05nSCxVQUFVOzBCQUNWLDRFQUFDVjtvQkFBSVIsV0FBVTs4QkFDWCw0RUFBQ1E7d0JBQUlSLFdBQVU7OzBDQUNYLDhEQUFDOUssd0RBQUtBO2dDQUNGMkYsT0FBTTtnQ0FDTjZGLFNBQVE7MENBQ1IsNEVBQUN6TCx3REFBS0E7b0NBQ0Y2RCxJQUFHO29DQUNIMEcsTUFBSztvQ0FDTHFCLE1BQUs7b0NBQ0xELGFBQVk7b0NBQ1o1SSxPQUFPOEcsb0JBQW9CNUUsS0FBSztvQ0FDaEN5RyxVQUFVckI7b0NBQ1Z5QixRQUFROzs7Ozs7Ozs7OzswQ0FlaEIsOERBQUM3TCx3REFBS0E7Z0NBQUN3TCxTQUFRO2dDQUF3QjdGLE9BQU07MENBQ3pDLDRFQUFDNUYsd0RBQUtBO29DQUNGNkQsSUFBRztvQ0FDSDBHLE1BQUs7b0NBQ0xxQixNQUFLO29DQUNMN0ksT0FDSThHLG9CQUFvQnJILFFBQVEsSUFDNUJGLFNBQVNFLFFBQVE7b0NBRXJCa0osVUFBVXJCO29DQUNWc0IsYUFBWTtvQ0FDWkcsUUFBUTs7Ozs7Ozs7Ozs7MENBR2hCLDhEQUFDN0wsd0RBQUtBO2dDQUNGMkYsT0FBTTtnQ0FDTjZGLFNBQVE7MENBQ1IsNEVBQUN6TCx3REFBS0E7b0NBQ0Y2RCxJQUFHO29DQUNIMEcsTUFBSztvQ0FDTHFCLE1BQUs7b0NBQ0w3SSxPQUNJOEcsb0JBQW9CcEgsU0FBUyxJQUM3QkgsU0FBU0csU0FBUztvQ0FFdEJpSixVQUFVckI7b0NBQ1ZzQixhQUFZO29DQUNaRyxRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTTVCLDhEQUFDdEwsMkRBQWNBO2dCQUNYMEssWUFBWTlJO2dCQUNaK0ksZUFBZTlJO2dCQUNmK0ksY0FBY3pHO2dCQUNkMEcsWUFBVztnQkFDWHBHLE9BQU07MEJBQ04sNEVBQUNzRztvQkFBSVIsV0FBVTs7d0JBQ1Z2SixDQUFBQSw0QkFBQUEsc0NBQUFBLGdCQUFpQm9FLEtBQUssa0JBQ25CLDhEQUFDMkY7NEJBQUlSLFdBQVU7c0NBQ1Z2SixDQUFBQSw0QkFBQUEsc0NBQUFBLGdCQUFpQm9FLEtBQUssS0FBSW9FLDBCQUN2Qiw4REFBQ3VCO2dDQUFJUixXQUFVOztrREFDWCw4REFBQ3RLLDhDQUFDQTt3Q0FBQ3NLLFdBQVU7OzRDQUFjOzRDQUNYdkosNEJBQUFBLHNDQUFBQSxnQkFBaUJvRSxLQUFLOzRDQUFDOzs7Ozs7O2tEQUV2Qyw4REFBQzFGLDBEQUFNQTt3Q0FDSDhMLFVBQVU3TCwrRkFBTUE7d0NBQ2hCOEssU0FBU2xDO3dDQUNUZ0MsV0FBVTs7NENBQVM7NENBQ2R2Siw0QkFBQUEsc0NBQUFBLGdCQUFpQm9FLEtBQUs7Ozs7Ozs7a0RBRS9CLDhEQUFDdkYsZ0VBQVNBO3dDQUFDMEssV0FBVTs7Ozs7O2tEQUNyQiw4REFBQ3RLLDhDQUFDQTt3Q0FBQ3NLLFdBQVU7a0RBQVk7Ozs7Ozs7Ozs7O3FEQUs3Qiw4REFBQ2dCO2dDQUFFaEIsV0FBVTswQ0FBVTs7Ozs7Ozs7OztpREFPL0IsOERBQUNnQjs0QkFBRWhCLFdBQVU7c0NBQVU7Ozs7OztzQ0FNM0IsOERBQUNROzRCQUFJUixXQUFVOzs4Q0FDWCw4REFBQzlLLHdEQUFLQTtvQ0FDRjJGLE9BQU07b0NBQ042RixTQUFROzhDQUNSLDRFQUFDekwsd0RBQUtBO3dDQUNGNkQsSUFBRzt3Q0FDSDBHLE1BQUs7d0NBQ0xxQixNQUFLO3dDQUNMRCxhQUFZO3dDQUNaNUksT0FBTzhHLG9CQUFvQjVFLEtBQUs7d0NBQ2hDeUcsVUFBVXJCO3dDQUNWeUIsUUFBUTs7Ozs7Ozs7Ozs7OENBZWhCLDhEQUFDUDtvQ0FBSVIsV0FBVTs7c0RBQ1gsOERBQUM5Syx3REFBS0E7NENBQ0YyRixPQUFNOzRDQUNONkYsU0FBUTtzREFDUiw0RUFBQ3pMLHdEQUFLQTtnREFDRjZELElBQUc7Z0RBQ0gwRyxNQUFLO2dEQUNMcUIsTUFBSztnREFDTDdJLE9BQ0k4RyxvQkFBb0JySCxRQUFRLElBQzVCRixTQUFTRSxRQUFRO2dEQUVyQmtKLFVBQVVyQjtnREFDVnNCLGFBQVk7Z0RBQ1pHLFFBQVE7Ozs7Ozs7Ozs7O3NEQUdoQiw4REFBQzdMLHdEQUFLQTs0Q0FDRjJGLE9BQU07NENBQ042RixTQUFRO3NEQUNSLDRFQUFDekwsd0RBQUtBO2dEQUNGNkQsSUFBRztnREFDSDBHLE1BQUs7Z0RBQ0xxQixNQUFLO2dEQUNMN0ksT0FDSThHLG9CQUFvQnBILFNBQVMsSUFDN0JILFNBQVNHLFNBQVM7Z0RBRXRCaUosVUFBVXJCO2dEQUNWc0IsYUFBWTtnREFDWkcsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUzVDO0dBcndDd0JwTDs7UUFrSmFuQix3REFBV0E7UUE4RmhCQSx3REFBV0E7UUE0RVJELHlEQUFZQTtRQW1OakJBLHlEQUFZQTs7O0tBL2dCbEJvQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL3VpL2xvZ2Jvb2svY29tcG9uZW50cy9sb2NhdGlvbi50c3g/NzVkNSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcclxuXHJcbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VSZWYgfSBmcm9tICdyZWFjdCdcclxuaW1wb3J0IHsgQ29tYm9ib3ggfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvY29tYm9Cb3gnXHJcbmltcG9ydCB7XHJcbiAgICBDUkVBVEVfR0VPX0xPQ0FUSU9OLFxyXG4gICAgQ3JlYXRlRmF2b3JpdGVMb2NhdGlvbixcclxufSBmcm9tICdAL2FwcC9saWIvZ3JhcGhRTC9tdXRhdGlvbidcclxuaW1wb3J0IHsgdXNlTGF6eVF1ZXJ5LCB1c2VNdXRhdGlvbiB9IGZyb20gJ0BhcG9sbG8vY2xpZW50J1xyXG5pbXBvcnQgeyB0b2FzdCB9IGZyb20gJ3Nvbm5lcidcclxuaW1wb3J0IHsgaXNFbXB0eSwgdHJpbSB9IGZyb20gJ2xvZGFzaCdcclxuXHJcbmltcG9ydCB7XHJcbiAgICBHRVRfR0VPX0xPQ0FUSU9OUyxcclxuICAgIEdldEZhdm9yaXRlTG9jYXRpb25zLFxyXG59IGZyb20gJ0AvYXBwL2xpYi9ncmFwaFFML3F1ZXJ5J1xyXG5pbXBvcnQgRmF2b3JpdGVMb2NhdGlvbk1vZGVsIGZyb20gJ0AvYXBwL29mZmxpbmUvbW9kZWxzL2Zhdm9yaXRlTG9jYXRpb24nXHJcbmltcG9ydCBHZW9Mb2NhdGlvbk1vZGVsIGZyb20gJ0AvYXBwL29mZmxpbmUvbW9kZWxzL2dlb0xvY2F0aW9uJ1xyXG5pbXBvcnQgeyBnZW5lcmF0ZVVuaXF1ZUlkIH0gZnJvbSAnQC9hcHAvb2ZmbGluZS9oZWxwZXJzL2Z1bmN0aW9ucydcclxuaW1wb3J0IHsgSW5wdXQgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvaW5wdXQnXHJcbmltcG9ydCB7IExhYmVsIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2xhYmVsJ1xyXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJ1xyXG5pbXBvcnQgeyBNYXBQaW4sIENoZXZyb25Eb3duIH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xyXG5pbXBvcnQgeyBTZXBhcmF0b3IgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvc2VwYXJhdG9yJ1xyXG5pbXBvcnQgeyBSYWRpb0dyb3VwLCBSYWRpb0dyb3VwSXRlbSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9yYWRpby1ncm91cCdcclxuaW1wb3J0IHsgQWxlcnREaWFsb2dOZXcsIFAgfSBmcm9tICdAL2NvbXBvbmVudHMvdWknXHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBMb2NhdGlvbkZpZWxkKHtcclxuICAgIHNldEN1cnJlbnRMb2NhdGlvbixcclxuICAgIGhhbmRsZUxvY2F0aW9uQ2hhbmdlLFxyXG4gICAgY3VycmVudEV2ZW50LFxyXG4gICAgb2ZmbGluZSA9IGZhbHNlLFxyXG4gICAgc2hvd0FkZE5ld0xvY2F0aW9uID0gdHJ1ZSxcclxuICAgIHNob3dVc2VDb29yZGluYXRlcyA9IHRydWUsXHJcbiAgICBzaG93Q3VycmVudExvY2F0aW9uID0gdHJ1ZSxcclxufToge1xyXG4gICAgc2V0Q3VycmVudExvY2F0aW9uOiBhbnlcclxuICAgIGhhbmRsZUxvY2F0aW9uQ2hhbmdlOiBhbnlcclxuICAgIGN1cnJlbnRFdmVudD86IGFueVxyXG4gICAgb2ZmbGluZT86IGJvb2xlYW5cclxuICAgIHNob3dBZGROZXdMb2NhdGlvbj86IGJvb2xlYW5cclxuICAgIHNob3dVc2VDb29yZGluYXRlcz86IGJvb2xlYW5cclxuICAgIHNob3dDdXJyZW50TG9jYXRpb24/OiBib29sZWFuXHJcbn0pIHtcclxuICAgIGNvbnN0IFtnZW9Mb2NhdGlvbnMsIHNldEdlb0xvY2F0aW9uc10gPSB1c2VTdGF0ZTxhbnk+KFtdKVxyXG4gICAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlPGJvb2xlYW4+KHRydWUpXHJcbiAgICBjb25zdCBbbmV3TG9jYXRpb24sIHNldE5ld0xvY2F0aW9uXSA9IHVzZVN0YXRlKGZhbHNlKVxyXG4gICAgY29uc3QgW2Nsb3Nlc3RMb2NhdGlvbiwgc2V0Q2xvc2VzdExvY2F0aW9uXSA9IHVzZVN0YXRlPGFueT4oZmFsc2UpXHJcbiAgICBjb25zdCBbc2VsZWN0ZWRMb2NhdGlvbiwgc2V0U2VsZWN0ZWRMb2NhdGlvbl0gPSB1c2VTdGF0ZTxhbnk+KClcclxuICAgIGNvbnN0IFtmYXZvcml0ZUxvY2F0aW9ucywgc2V0RmF2b3JpdGVMb2NhdGlvbnNdID0gdXNlU3RhdGU8YW55PihbXSlcclxuXHJcbiAgICBjb25zdCBbc2VsZWN0ZWRQYXJlbnRMb2NhdGlvbiwgc2V0U2VsZWN0ZWRQYXJlbnRMb2NhdGlvbl0gPVxyXG4gICAgICAgIHVzZVN0YXRlPGFueT4oZmFsc2UpXHJcbiAgICBjb25zdCBbbG9jYXRpb25zLCBzZXRMb2NhdGlvbnNdID0gdXNlU3RhdGU8YW55PigpXHJcbiAgICBjb25zdCBbb3Blbk5ld0xvY2F0aW9uRGlhbG9nLCBzZXRPcGVuTmV3TG9jYXRpb25EaWFsb2ddID1cclxuICAgICAgICB1c2VTdGF0ZTxib29sZWFuPihmYWxzZSlcclxuICAgIGNvbnN0IFtvcGVuU2V0TG9jYXRpb25EaWFsb2csIHNldE9wZW5TZXRMb2NhdGlvbkRpYWxvZ10gPVxyXG4gICAgICAgIHVzZVN0YXRlPGJvb2xlYW4+KGZhbHNlKVxyXG4gICAgY29uc3QgW2xvY2F0aW9uLCBzZXRMb2NhdGlvbl0gPSB1c2VTdGF0ZTx7XHJcbiAgICAgICAgbGF0aXR1ZGU6IGFueVxyXG4gICAgICAgIGxvbmdpdHVkZTogYW55XHJcbiAgICB9Pih7IGxhdGl0dWRlOiAwLCBsb25naXR1ZGU6IDAgfSlcclxuICAgIGNvbnN0IGZhdm9yaXRlTG9jYXRpb25Nb2RlbCA9IG5ldyBGYXZvcml0ZUxvY2F0aW9uTW9kZWwoKVxyXG4gICAgY29uc3QgZ2VvbG9jYXRpb25Nb2RlbCA9IG5ldyBHZW9Mb2NhdGlvbk1vZGVsKClcclxuICAgIGNvbnN0IGRpc3BsYXlOZXdMb2NhdGlvbiA9ICgpID0+IHtcclxuICAgICAgICBzZXROZXdMb2NhdGlvbih0cnVlKVxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGhpZGVOZXdMb2NhdGlvbiA9ICgpID0+IHtcclxuICAgICAgICBzZXROZXdMb2NhdGlvbihmYWxzZSlcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBoYW5kbGVTZXRMb2NhdGlvbkNoYW5nZSA9IGFzeW5jIChzZWxlY3RlZExvY2F0aW9uOiBhbnkpID0+IHtcclxuICAgICAgICBpZiAoIXNlbGVjdGVkTG9jYXRpb24pIHtcclxuICAgICAgICAgICAgLy8gSGFuZGxlIGNsZWFyaW5nIHRoZSBzZWxlY3Rpb25cclxuICAgICAgICAgICAgc2V0U2VsZWN0ZWRMb2NhdGlvbihudWxsKVxyXG4gICAgICAgICAgICBoYW5kbGVMb2NhdGlvbkNoYW5nZShudWxsKVxyXG4gICAgICAgICAgICByZXR1cm5cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGlmIChzZWxlY3RlZExvY2F0aW9uLnZhbHVlID09PSAnbmV3TG9jYXRpb24nKSB7XHJcbiAgICAgICAgICAgIHNldFNlbGVjdGVkUGFyZW50TG9jYXRpb24oZmFsc2UpXHJcbiAgICAgICAgICAgIC8vIEluc3RlYWQgb2Ygb3BlbmluZyBhIHNlcGFyYXRlIG1vZGFsLCBzd2l0Y2ggdG8gdGhlIFwibmV3XCIgb3B0aW9uIGluIHRoZSBjdXJyZW50IG1vZGFsXHJcbiAgICAgICAgICAgIHNldExvY2F0aW9uT3B0aW9uKCduZXcnKVxyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIHNldFNlbGVjdGVkTG9jYXRpb24oc2VsZWN0ZWRMb2NhdGlvbilcclxuICAgICAgICAgICAgaGFuZGxlTG9jYXRpb25DaGFuZ2Uoc2VsZWN0ZWRMb2NhdGlvbilcclxuICAgICAgICAgICAgY29uc3QgdXNlcklkID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3VzZXJJZCcpXHJcbiAgICAgICAgICAgIGlmICh1c2VySWQgIT09IG51bGwgJiYgK3VzZXJJZCA+IDApIHtcclxuICAgICAgICAgICAgICAgIC8vIGlmICgrbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3VzZXJJZCcpPiAwKSB7XHJcbiAgICAgICAgICAgICAgICBpZiAob2ZmbGluZSkge1xyXG4gICAgICAgICAgICAgICAgICAgIC8vIENoZWNrIGlmIHRoaXMgbG9jYXRpb24gaXMgYWxyZWFkeSBhIGZhdm9yaXRlXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZXhpc3RpbmdGYXZvcml0ZSA9IGZhdm9yaXRlTG9jYXRpb25zLmZpbmQoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIChmYXY6IGFueSkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZhdi5nZW9Mb2NhdGlvbklEID09PSBzZWxlY3RlZExvY2F0aW9uLnZhbHVlLFxyXG4gICAgICAgICAgICAgICAgICAgIClcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKGV4aXN0aW5nRmF2b3JpdGUpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gVXBkYXRlIHVzYWdlIGNvdW50IGZvciBleGlzdGluZyBmYXZvcml0ZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB1cGRhdGVkRmF2b3JpdGUgPSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5leGlzdGluZ0Zhdm9yaXRlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdXNhZ2U6IChleGlzdGluZ0Zhdm9yaXRlLnVzYWdlIHx8IDApICsgMSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBhd2FpdCBmYXZvcml0ZUxvY2F0aW9uTW9kZWwuc2F2ZSh1cGRhdGVkRmF2b3JpdGUpXHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBVcGRhdGUgZmF2b3JpdGVMb2NhdGlvbnMgc3RhdGVcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdXBkYXRlZEZhdm9yaXRlcyA9IGZhdm9yaXRlTG9jYXRpb25zLm1hcChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIChmYXY6IGFueSkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmYXYuaWQgPT09IGV4aXN0aW5nRmF2b3JpdGUuaWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyB1cGRhdGVkRmF2b3JpdGVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBmYXYsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICAgICAgc2V0RmF2b3JpdGVMb2NhdGlvbnModXBkYXRlZEZhdm9yaXRlcylcclxuICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBDcmVhdGUgbmV3IGZhdm9yaXRlIHdpdGggaGlnaCB1c2FnZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBuZXdGYXZvcml0ZSA9IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkOiBnZW5lcmF0ZVVuaXF1ZUlkKCksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZW1iZXJJRDogK2xvY2FsU3RvcmFnZS5nZXRJdGVtKCd1c2VySWQnKSEsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBnZW9Mb2NhdGlvbklEOiArc2VsZWN0ZWRMb2NhdGlvbi52YWx1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHVzYWdlOiA5OTksIC8vIEhpZ2ggdXNhZ2UgdG8gZW5zdXJlIGl0IGFwcGVhcnMgYXQgdGhlIHRvcFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGF3YWl0IGZhdm9yaXRlTG9jYXRpb25Nb2RlbC5zYXZlKG5ld0Zhdm9yaXRlKVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gVXBkYXRlIGZhdm9yaXRlTG9jYXRpb25zIHN0YXRlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldEZhdm9yaXRlTG9jYXRpb25zKFtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5ld0Zhdm9yaXRlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4uZmF2b3JpdGVMb2NhdGlvbnMsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIF0pXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICAvLyBDaGVjayBpZiB0aGlzIGxvY2F0aW9uIGlzIGFscmVhZHkgYSBmYXZvcml0ZVxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGV4aXN0aW5nRmF2b3JpdGUgPSBmYXZvcml0ZUxvY2F0aW9ucy5maW5kKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAoZmF2OiBhbnkpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmYXYuZ2VvTG9jYXRpb25JRCA9PT0gc2VsZWN0ZWRMb2NhdGlvbi52YWx1ZSxcclxuICAgICAgICAgICAgICAgICAgICApXHJcblxyXG4gICAgICAgICAgICAgICAgICAgIGNyZWF0ZUZhdm9yaXRlTG9jYXRpb24oe1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YXJpYWJsZXM6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlucHV0OiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVtYmVySUQ6ICtsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndXNlcklkJykhLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGdlb0xvY2F0aW9uSUQ6ICtzZWxlY3RlZExvY2F0aW9uLnZhbHVlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgb25Db21wbGV0ZWQ6IChkYXRhKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoZXhpc3RpbmdGYXZvcml0ZSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIElmIGl0IHdhcyBhbHJlYWR5IGEgZmF2b3JpdGUsIHdlJ3ZlIGp1c3QgaW5jcmVhc2VkIGl0cyB1c2FnZSBjb3VudFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIFJlZnJlc2ggdGhlIGZhdm9yaXRlcyBsaXN0IHRvIGdldCB1cGRhdGVkIHVzYWdlIGNvdW50c1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGdldEZhdm9yaXRlTG9jYXRpb25zKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFibGVzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB1c2VySUQ6ICtsb2NhbFN0b3JhZ2UuZ2V0SXRlbShcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAndXNlcklkJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkhLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIEFkZCB0aGUgbmV3IGZhdm9yaXRlIHRvIHRoZSBzdGF0ZSB3aXRoIGhpZ2ggdXNhZ2VcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBuZXdGYXZvcml0ZSA9IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ6IGRhdGEuY3JlYXRlRmF2b3JpdGVMb2NhdGlvbi5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZ2VvTG9jYXRpb25JRDogc2VsZWN0ZWRMb2NhdGlvbi52YWx1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdXNhZ2U6IDk5OSwgLy8gSGlnaCB1c2FnZSB0byBlbnN1cmUgaXQgYXBwZWFycyBhdCB0aGUgdG9wXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldEZhdm9yaXRlTG9jYXRpb25zKFtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbmV3RmF2b3JpdGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLmZhdm9yaXRlTG9jYXRpb25zLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF0pXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBbY3JlYXRlRmF2b3JpdGVMb2NhdGlvbl0gPSB1c2VNdXRhdGlvbihDcmVhdGVGYXZvcml0ZUxvY2F0aW9uLCB7XHJcbiAgICAgICAgb25FcnJvcjogKGVycm9yKSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNyZWF0aW5nIGZhdm9yaXRlIGxvY2F0aW9uOicsIGVycm9yKVxyXG4gICAgICAgIH0sXHJcbiAgICB9KVxyXG5cclxuICAgIGNvbnN0IGhhbmRsZVBhcmVudExvY2F0aW9uQ2hhbmdlID0gKHNlbGVjdGVkTG9jYXRpb246IGFueSkgPT4ge1xyXG4gICAgICAgIHNldFNlbGVjdGVkUGFyZW50TG9jYXRpb24oc2VsZWN0ZWRMb2NhdGlvbilcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBoYW5kbGVDcmVhdGVOZXdMb2NhdGlvbiA9IGFzeW5jICgpID0+IHtcclxuICAgICAgICBjb25zdCB0aXRsZUlucHV0ID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoXHJcbiAgICAgICAgICAgICduZXctbG9jYXRpb24tdGl0bGUnLFxyXG4gICAgICAgICkgYXMgSFRNTElucHV0RWxlbWVudFxyXG4gICAgICAgIGNvbnN0IGxhdGl0dWRlSW5wdXQgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZChcclxuICAgICAgICAgICAgJ25ldy1sb2NhdGlvbi1sYXRpdHVkZScsXHJcbiAgICAgICAgKSBhcyBIVE1MSW5wdXRFbGVtZW50XHJcbiAgICAgICAgY29uc3QgbG9uZ2l0dWRlSW5wdXQgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZChcclxuICAgICAgICAgICAgJ25ldy1sb2NhdGlvbi1sb25naXR1ZGUnLFxyXG4gICAgICAgICkgYXMgSFRNTElucHV0RWxlbWVudFxyXG5cclxuICAgICAgICAvLyBWYWxpZGF0ZSBpbnB1dHMgZXhpc3RcclxuXHJcbiAgICAgICAgY29uc3QgdGl0bGUgPSB0aXRsZUlucHV0LnZhbHVlPy50cmltKCkgfHwgJ05ldyBsb2NhdGlvbidcclxuICAgICAgICBjb25zdCBwYXJlbnRMb2NhdGlvbiA9IHNlbGVjdGVkUGFyZW50TG9jYXRpb25cclxuICAgICAgICAgICAgPyBzZWxlY3RlZFBhcmVudExvY2F0aW9uLnZhbHVlXHJcbiAgICAgICAgICAgIDogbnVsbFxyXG4gICAgICAgIGNvbnN0IGxhdGl0dWRlID0gbGF0aXR1ZGVJbnB1dC52YWx1ZT8udHJpbSgpIHx8ICcwJ1xyXG4gICAgICAgIGNvbnN0IGxvbmdpdHVkZSA9IGxvbmdpdHVkZUlucHV0LnZhbHVlPy50cmltKCkgfHwgJzAnXHJcblxyXG4gICAgICAgIC8vIENvbnZlcnQgdG8gbnVtYmVycyBhbmQgdmFsaWRhdGVcclxuICAgICAgICBjb25zdCBsYXROdW0gPSBwYXJzZUZsb2F0KGxhdGl0dWRlKVxyXG4gICAgICAgIGNvbnN0IGxvbmdOdW0gPSBwYXJzZUZsb2F0KGxvbmdpdHVkZSlcclxuXHJcbiAgICAgICAgY29uc3QgdmFyaWFibGVzID0ge1xyXG4gICAgICAgICAgICBpbnB1dDoge1xyXG4gICAgICAgICAgICAgICAgdGl0bGU6IHRpdGxlLFxyXG4gICAgICAgICAgICAgICAgbGF0OiBpc05hTihsYXROdW0pID8gMCA6IGxhdE51bSxcclxuICAgICAgICAgICAgICAgIGxvbmc6IGlzTmFOKGxvbmdOdW0pID8gMCA6IGxvbmdOdW0sXHJcbiAgICAgICAgICAgICAgICBwYXJlbnRMb2NhdGlvbklEOiBwYXJlbnRMb2NhdGlvbixcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGlmIChvZmZsaW5lKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHVuaXF1ZUlEID0gZ2VuZXJhdGVVbmlxdWVJZCgpXHJcbiAgICAgICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCBnZW9sb2NhdGlvbk1vZGVsLnNhdmUoe1xyXG4gICAgICAgICAgICAgICAgLi4udmFyaWFibGVzLmlucHV0LFxyXG4gICAgICAgICAgICAgICAgaWQ6IHVuaXF1ZUlELFxyXG4gICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICBpZiAobG9jYXRpb25zPy5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICAgICAgICBzZXRMb2NhdGlvbnMoWy4uLmxvY2F0aW9ucywgZGF0YV0pXHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICBzZXRMb2NhdGlvbnMoW2RhdGFdKVxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAvLyBDcmVhdGUgbG9jYXRpb24gb2JqZWN0IHRvIHVzZVxyXG4gICAgICAgICAgICBjb25zdCBuZXdMb2NhdGlvbiA9IHtcclxuICAgICAgICAgICAgICAgIGxhYmVsOiBkYXRhLnRpdGxlLFxyXG4gICAgICAgICAgICAgICAgdmFsdWU6IGRhdGEuaWQsXHJcbiAgICAgICAgICAgICAgICBsYXRpdHVkZTogZGF0YS5sYXQsXHJcbiAgICAgICAgICAgICAgICBsb25naXR1ZGU6IGRhdGEubG9uZyxcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgc2V0U2VsZWN0ZWRMb2NhdGlvbihuZXdMb2NhdGlvbilcclxuICAgICAgICAgICAgaGFuZGxlTG9jYXRpb25DaGFuZ2UobmV3TG9jYXRpb24pXHJcblxyXG4gICAgICAgICAgICAvLyBBdXRvbWF0aWNhbGx5IG1hcmsgbmV3bHkgY3JlYXRlZCBsb2NhdGlvbiBhcyBhIGZhdm9yaXRlXHJcbiAgICAgICAgICAgIGNvbnN0IHVzZXJJZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd1c2VySWQnKVxyXG4gICAgICAgICAgICBpZiAodXNlcklkICE9PSBudWxsICYmICt1c2VySWQgPiAwKSB7XHJcbiAgICAgICAgICAgICAgICAvLyBTYXZlIHRvIGZhdm9yaXRlIGxvY2F0aW9ucyB3aXRoIGhpZ2ggdXNhZ2UgdG8gZW5zdXJlIGl0IGFwcGVhcnMgYXQgdGhlIHRvcFxyXG4gICAgICAgICAgICAgICAgY29uc3QgZmF2b3JpdGVJZCA9IGdlbmVyYXRlVW5pcXVlSWQoKVxyXG4gICAgICAgICAgICAgICAgY29uc3QgbmV3RmF2b3JpdGUgPSB7XHJcbiAgICAgICAgICAgICAgICAgICAgaWQ6IGZhdm9yaXRlSWQsXHJcbiAgICAgICAgICAgICAgICAgICAgbWVtYmVySUQ6ICt1c2VySWQsXHJcbiAgICAgICAgICAgICAgICAgICAgZ2VvTG9jYXRpb25JRDogZGF0YS5pZCxcclxuICAgICAgICAgICAgICAgICAgICB1c2FnZTogOTk5LCAvLyBIaWdoIHVzYWdlIHRvIGVuc3VyZSBpdCBhcHBlYXJzIGF0IHRoZSB0b3BcclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICBhd2FpdCBmYXZvcml0ZUxvY2F0aW9uTW9kZWwuc2F2ZShuZXdGYXZvcml0ZSlcclxuXHJcbiAgICAgICAgICAgICAgICAvLyBVcGRhdGUgZmF2b3JpdGVMb2NhdGlvbnMgc3RhdGUgdG8gaW5jbHVkZSB0aGUgbmV3IGZhdm9yaXRlXHJcbiAgICAgICAgICAgICAgICBzZXRGYXZvcml0ZUxvY2F0aW9ucyhbbmV3RmF2b3JpdGUsIC4uLmZhdm9yaXRlTG9jYXRpb25zXSlcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgc2V0T3Blbk5ld0xvY2F0aW9uRGlhbG9nKGZhbHNlKVxyXG4gICAgICAgICAgICBzZXRPcGVuU2V0TG9jYXRpb25EaWFsb2coZmFsc2UpXHJcbiAgICAgICAgICAgIHNldE9wZW5Mb2NhdGlvbk1vZGFsKGZhbHNlKSAvLyBBbHNvIGNsb3NlIHRoZSBtYWluIG1vZGFsXHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgY3JlYXRlR2VvTG9jYXRpb24oe1xyXG4gICAgICAgICAgICAgICAgdmFyaWFibGVzLFxyXG4gICAgICAgICAgICB9KVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBbY3JlYXRlR2VvTG9jYXRpb25dID0gdXNlTXV0YXRpb24oQ1JFQVRFX0dFT19MT0NBVElPTiwge1xyXG4gICAgICAgIG9uQ29tcGxldGVkOiAocmVzcG9uc2UpID0+IHtcclxuICAgICAgICAgICAgY29uc3QgZGF0YSA9IHJlc3BvbnNlLmNyZWF0ZUdlb0xvY2F0aW9uXHJcbiAgICAgICAgICAgIGlmIChsb2NhdGlvbnM/Lmxlbmd0aCA+IDApIHtcclxuICAgICAgICAgICAgICAgIHNldExvY2F0aW9ucyhbLi4ubG9jYXRpb25zLCBkYXRhXSlcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIHNldExvY2F0aW9ucyhbZGF0YV0pXHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIC8vIENyZWF0ZSBsb2NhdGlvbiBvYmplY3QgdG8gdXNlXHJcbiAgICAgICAgICAgIGNvbnN0IG5ld0xvY2F0aW9uID0ge1xyXG4gICAgICAgICAgICAgICAgbGFiZWw6IGRhdGEudGl0bGUsXHJcbiAgICAgICAgICAgICAgICB2YWx1ZTogZGF0YS5pZCxcclxuICAgICAgICAgICAgICAgIGxhdGl0dWRlOiBkYXRhLmxhdCxcclxuICAgICAgICAgICAgICAgIGxvbmdpdHVkZTogZGF0YS5sb25nLFxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICBzZXRTZWxlY3RlZExvY2F0aW9uKG5ld0xvY2F0aW9uKVxyXG4gICAgICAgICAgICBoYW5kbGVMb2NhdGlvbkNoYW5nZShuZXdMb2NhdGlvbilcclxuXHJcbiAgICAgICAgICAgIC8vIEF1dG9tYXRpY2FsbHkgbWFyayBuZXdseSBjcmVhdGVkIGxvY2F0aW9uIGFzIGEgZmF2b3JpdGVcclxuICAgICAgICAgICAgY29uc3QgdXNlcklkID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3VzZXJJZCcpXHJcbiAgICAgICAgICAgIGlmICh1c2VySWQgIT09IG51bGwgJiYgK3VzZXJJZCA+IDApIHtcclxuICAgICAgICAgICAgICAgIGNyZWF0ZUZhdm9yaXRlTG9jYXRpb24oe1xyXG4gICAgICAgICAgICAgICAgICAgIHZhcmlhYmxlczoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBpbnB1dDoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVtYmVySUQ6ICt1c2VySWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBnZW9Mb2NhdGlvbklEOiArZGF0YS5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ29tcGxldGVkOiAoZmF2RGF0YSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBVcGRhdGUgZmF2b3JpdGVMb2NhdGlvbnMgc3RhdGUgdG8gaW5jbHVkZSB0aGUgbmV3IGZhdm9yaXRlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIHdpdGggaGlnaCB1c2FnZSB0byBlbnN1cmUgaXQgYXBwZWFycyBhdCB0aGUgdG9wXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IG5ld0Zhdm9yaXRlID0ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ6IGZhdkRhdGEuY3JlYXRlRmF2b3JpdGVMb2NhdGlvbi5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGdlb0xvY2F0aW9uSUQ6IGRhdGEuaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB1c2FnZTogOTk5LCAvLyBIaWdoIHVzYWdlIHRvIGVuc3VyZSBpdCBhcHBlYXJzIGF0IHRoZSB0b3BcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzZXRGYXZvcml0ZUxvY2F0aW9ucyhbXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBuZXdGYXZvcml0ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLmZhdm9yaXRlTG9jYXRpb25zLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBdKVxyXG4gICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICBzZXRPcGVuTmV3TG9jYXRpb25EaWFsb2coZmFsc2UpXHJcbiAgICAgICAgICAgIHNldE9wZW5TZXRMb2NhdGlvbkRpYWxvZyhmYWxzZSlcclxuICAgICAgICAgICAgc2V0T3BlbkxvY2F0aW9uTW9kYWwoZmFsc2UpIC8vIEFsc28gY2xvc2UgdGhlIG1haW4gbW9kYWxcclxuICAgICAgICB9LFxyXG4gICAgICAgIG9uRXJyb3I6IChlcnJvcikgPT4ge1xyXG4gICAgICAgICAgICBzZXRPcGVuTmV3TG9jYXRpb25EaWFsb2coZmFsc2UpXHJcbiAgICAgICAgICAgIHNldE9wZW5TZXRMb2NhdGlvbkRpYWxvZyhmYWxzZSlcclxuICAgICAgICAgICAgc2V0T3BlbkxvY2F0aW9uTW9kYWwoZmFsc2UpIC8vIEFsc28gY2xvc2UgdGhlIG1haW4gbW9kYWxcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY3JlYXRpbmcgR2VvTG9jYXRpb24nLCBlcnJvcilcclxuICAgICAgICAgICAgdG9hc3QuZXJyb3IoJ0Vycm9yIGNyZWF0aW5nIEdlb0xvY2F0aW9uJylcclxuICAgICAgICB9LFxyXG4gICAgfSlcclxuICAgIGNvbnN0IG9mZmxpbmVHZXRGYXZvcml0ZUxvY2F0aW9ucyA9IGFzeW5jICgpID0+IHtcclxuICAgICAgICBjb25zdCBsb2NhdGlvbnMgPSBhd2FpdCBmYXZvcml0ZUxvY2F0aW9uTW9kZWwuZ2V0QnlNZW1iZXJJRChcclxuICAgICAgICAgICAgK2xvY2FsU3RvcmFnZS5nZXRJdGVtKCd1c2VySWQnKSEsXHJcbiAgICAgICAgKVxyXG4gICAgICAgIHNldEZhdm9yaXRlTG9jYXRpb25zKGxvY2F0aW9ucylcclxuICAgIH1cclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgaWYgKG9mZmxpbmUpIHtcclxuICAgICAgICAgICAgb2ZmbGluZUdldEZhdm9yaXRlTG9jYXRpb25zKClcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBnZXRGYXZvcml0ZUxvY2F0aW9ucyh7XHJcbiAgICAgICAgICAgICAgICB2YXJpYWJsZXM6IHtcclxuICAgICAgICAgICAgICAgICAgICB1c2VySUQ6ICtsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndXNlcklkJykhLFxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgfSlcclxuICAgICAgICB9XHJcbiAgICB9LCBbXSlcclxuXHJcbiAgICBjb25zdCBbZ2V0RmF2b3JpdGVMb2NhdGlvbnNdID0gdXNlTGF6eVF1ZXJ5KEdldEZhdm9yaXRlTG9jYXRpb25zLCB7XHJcbiAgICAgICAgZmV0Y2hQb2xpY3k6ICdjYWNoZS1hbmQtbmV0d29yaycsXHJcbiAgICAgICAgb25Db21wbGV0ZWQ6IChkYXRhKSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IGxvY2F0aW9ucyA9IGRhdGEucmVhZEZhdm9yaXRlTG9jYXRpb25zLm5vZGVzXHJcbiAgICAgICAgICAgIHNldEZhdm9yaXRlTG9jYXRpb25zKGxvY2F0aW9ucylcclxuICAgICAgICB9LFxyXG4gICAgICAgIG9uRXJyb3I6IChlcnJvcikgPT4ge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdvbkVycm9yJywgZXJyb3IpXHJcbiAgICAgICAgfSxcclxuICAgIH0pXHJcblxyXG4gICAgY29uc3QgYWxsTG9jYXRpb25zID0gKCkgPT4ge1xyXG4gICAgICAgIC8vIEZpbHRlciBmdW5jdGlvbiB0byByZW1vdmUgbG9jYXRpb25zIHdpdGggZW1wdHkgb3Igd2hpdGVzcGFjZS1vbmx5IHRpdGxlc1xyXG4gICAgICAgIGNvbnN0IGZpbHRlclZhbGlkTG9jYXRpb25zID0gKGxvY3M6IGFueVtdKSA9PiB7XHJcbiAgICAgICAgICAgIHJldHVybiBsb2NzLmZpbHRlcigobG9jOiBhbnkpID0+ICFpc0VtcHR5KHRyaW0obG9jLmxhYmVsKSkpXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBsZXQgbWFwcGVkTG9jYXRpb25zID1cclxuICAgICAgICAgICAgZ2VvTG9jYXRpb25zPy5tYXAoKGxvY2F0aW9uOiBhbnkpID0+ICh7XHJcbiAgICAgICAgICAgICAgICBsYWJlbDogbG9jYXRpb24udGl0bGUsXHJcbiAgICAgICAgICAgICAgICB2YWx1ZTogbG9jYXRpb24uaWQsXHJcbiAgICAgICAgICAgICAgICBsYXRpdHVkZTogbG9jYXRpb24ubGF0LFxyXG4gICAgICAgICAgICAgICAgbG9uZ2l0dWRlOiBsb2NhdGlvbi5sb25nLFxyXG4gICAgICAgICAgICB9KSkgfHwgW11cclxuXHJcbiAgICAgICAgLy8gQ29tYmluZSBsb2NhdGlvbnMgaWYgbmVlZGVkXHJcbiAgICAgICAgbGV0IGNvbWJpbmVkTG9jYXRpb25zID0gW11cclxuICAgICAgICBpZiAoZ2VvTG9jYXRpb25zICYmIGxvY2F0aW9ucz8ubGVuZ3RoID4gMCkge1xyXG4gICAgICAgICAgICBjb21iaW5lZExvY2F0aW9ucyA9IFsuLi5sb2NhdGlvbnMsIC4uLm1hcHBlZExvY2F0aW9uc11cclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBjb21iaW5lZExvY2F0aW9ucyA9IFsuLi5tYXBwZWRMb2NhdGlvbnNdXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBGaWx0ZXIgb3V0IGxvY2F0aW9ucyB3aXRoIGVtcHR5IHRpdGxlcyBiZWZvcmUgc29ydGluZ1xyXG4gICAgICAgIGNvbnN0IHZhbGlkTG9jYXRpb25zID0gZmlsdGVyVmFsaWRMb2NhdGlvbnMoY29tYmluZWRMb2NhdGlvbnMpXHJcblxyXG4gICAgICAgIC8vIFNvcnQgdGhlIGZpbHRlcmVkIGxvY2F0aW9uc1xyXG4gICAgICAgIHJldHVybiBzb3J0TG9jYXRpb25zKHZhbGlkTG9jYXRpb25zKVxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IHNvcnRMb2NhdGlvbnMgPSAobG9jYXRpb25zOiBhbnkpID0+IHtcclxuICAgICAgICAvLyBSZXR1cm4gZW1wdHkgYXJyYXkgaWYgbG9jYXRpb25zIGlzIGVtcHR5IG9yIHVuZGVmaW5lZFxyXG4gICAgICAgIGlmICghbG9jYXRpb25zIHx8IGxvY2F0aW9ucy5sZW5ndGggPT09IDApIHtcclxuICAgICAgICAgICAgcmV0dXJuIFtdXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBDcmVhdGUgYSBjb3B5IG9mIHRoZSBhcnJheSB0byBhdm9pZCBtdXRhdGluZyB0aGUgb3JpZ2luYWxcclxuICAgICAgICBjb25zdCBsb2NhdGlvbnNUb1NvcnQgPSBbLi4ubG9jYXRpb25zXVxyXG5cclxuICAgICAgICBpZiAoZmF2b3JpdGVMb2NhdGlvbnMubGVuZ3RoID4gMCkge1xyXG4gICAgICAgICAgICByZXR1cm4gbG9jYXRpb25zVG9Tb3J0LnNvcnQoKGE6IGFueSwgYjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAvLyBGaW5kIGlmIGxvY2F0aW9ucyBhcmUgZmF2b3JpdGVzXHJcbiAgICAgICAgICAgICAgICBjb25zdCBhRmF2ID0gZmF2b3JpdGVMb2NhdGlvbnMuZmluZChcclxuICAgICAgICAgICAgICAgICAgICAoZmF2OiBhbnkpID0+IGZhdi5nZW9Mb2NhdGlvbklEID09PSBhLnZhbHVlLFxyXG4gICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgY29uc3QgYkZhdiA9IGZhdm9yaXRlTG9jYXRpb25zLmZpbmQoXHJcbiAgICAgICAgICAgICAgICAgICAgKGZhdjogYW55KSA9PiBmYXYuZ2VvTG9jYXRpb25JRCA9PT0gYi52YWx1ZSxcclxuICAgICAgICAgICAgICAgIClcclxuXHJcbiAgICAgICAgICAgICAgICAvLyBCb3RoIGFyZSBmYXZvcml0ZXMgLSBmaXJzdCBzb3J0IGJ5IElEIChoaWdoZXN0IHRvIGxvd2VzdCBmb3IgbmV3bHkgY3JlYXRlZCBsb2NhdGlvbnMpXHJcbiAgICAgICAgICAgICAgICBpZiAoYUZhdiAmJiBiRmF2KSB7XHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGJGYXYudXNhZ2UgLSBhRmF2LnVzYWdlXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAvLyBPbmx5IGEgaXMgYSBmYXZvcml0ZSAtIGEgY29tZXMgZmlyc3RcclxuICAgICAgICAgICAgICAgIGVsc2UgaWYgKGFGYXYpIHtcclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gLTFcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIC8vIE9ubHkgYiBpcyBhIGZhdm9yaXRlIC0gYiBjb21lcyBmaXJzdFxyXG4gICAgICAgICAgICAgICAgZWxzZSBpZiAoYkZhdikge1xyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiAxXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gMFxyXG4gICAgICAgICAgICB9KVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgcmV0dXJuIGxvY2F0aW9uc1RvU29ydFxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGdldFByb3hpbWl0eSA9IChsb2NhdGlvbjogYW55LCBsYXRpdHVkZTogYW55LCBsb25naXR1ZGU6IGFueSkgPT4ge1xyXG4gICAgICAgIGNvbnN0IGRpc3RhbmNlID0gTWF0aC5zcXJ0KFxyXG4gICAgICAgICAgICBNYXRoLnBvdyhsb2NhdGlvbi5sYXQgLSBsYXRpdHVkZSwgMikgK1xyXG4gICAgICAgICAgICAgICAgTWF0aC5wb3cobG9jYXRpb24ubG9uZyAtIGxvbmdpdHVkZSwgMiksXHJcbiAgICAgICAgKVxyXG4gICAgICAgIHJldHVybiBkaXN0YW5jZVxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGZpbmRDbG9zZXN0TG9jYXRpb24gPSAobGF0aXR1ZGU6IGFueSwgbG9uZ2l0dWRlOiBhbnkpID0+IHtcclxuICAgICAgICAvLyBPbmx5IHRyeSB0byBmaW5kIGNsb3Nlc3QgbG9jYXRpb24gaWYgd2UgaGF2ZSBsb2NhdGlvbnMgYXZhaWxhYmxlXHJcbiAgICAgICAgaWYgKGdlb0xvY2F0aW9ucy5sZW5ndGggPT09IDApIHtcclxuICAgICAgICAgICAgc2V0Q2xvc2VzdExvY2F0aW9uKG51bGwpXHJcbiAgICAgICAgICAgIC8vIE5vIGxvY2F0aW9ucyBhdmFpbGFibGUsIHVzZSBjb29yZGluYXRlcyBkaXJlY3RseVxyXG4gICAgICAgICAgICBoYW5kbGVVc2VDb29yZGluYXRlc0FzTG9jYXRpb24obGF0aXR1ZGUsIGxvbmdpdHVkZSlcclxuICAgICAgICAgICAgcmV0dXJuXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBjb25zdCBjbG9zZXN0TG9jYXRpb24gPSBnZW9Mb2NhdGlvbnMucmVkdWNlKChwcmV2OiBhbnksIGN1cnI6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICBjb25zdCBwcmV2RGlzdGFuY2UgPSBNYXRoLnNxcnQoXHJcbiAgICAgICAgICAgICAgICBNYXRoLnBvdyhwcmV2LmxhdCAtIGxhdGl0dWRlLCAyKSArXHJcbiAgICAgICAgICAgICAgICAgICAgTWF0aC5wb3cocHJldi5sb25nIC0gbG9uZ2l0dWRlLCAyKSxcclxuICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICBjb25zdCBjdXJyRGlzdGFuY2UgPSBNYXRoLnNxcnQoXHJcbiAgICAgICAgICAgICAgICBNYXRoLnBvdyhjdXJyLmxhdCAtIGxhdGl0dWRlLCAyKSArXHJcbiAgICAgICAgICAgICAgICAgICAgTWF0aC5wb3coY3Vyci5sb25nIC0gbG9uZ2l0dWRlLCAyKSxcclxuICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICByZXR1cm4gcHJldkRpc3RhbmNlIDwgY3VyckRpc3RhbmNlID8gcHJldiA6IGN1cnJcclxuICAgICAgICB9KVxyXG5cclxuICAgICAgICBjb25zdCBwcm94aW1pdHkgPSBnZXRQcm94aW1pdHkoY2xvc2VzdExvY2F0aW9uLCBsYXRpdHVkZSwgbG9uZ2l0dWRlKVxyXG4gICAgICAgIC8vIENoZWNrIGlmIGNsb3Nlc3QgbG9jYXRpb24gaXMgcmVhc29uYWJseSBjbG9zZSBhbmQgdmFsaWRcclxuICAgICAgICBpZiAoXHJcbiAgICAgICAgICAgIHByb3hpbWl0eSA8PSAwLjE1ICYmXHJcbiAgICAgICAgICAgIGNsb3Nlc3RMb2NhdGlvbi5sYXQgIT09IDAgJiZcclxuICAgICAgICAgICAgY2xvc2VzdExvY2F0aW9uLmxvbmcgIT09IDBcclxuICAgICAgICApIHtcclxuICAgICAgICAgICAgLy8gQ2xvc2UgbG9jYXRpb24gZm91bmQgLSBzaG93IHRoZSBkaWFsb2dcclxuICAgICAgICAgICAgc2V0Q2xvc2VzdExvY2F0aW9uKHtcclxuICAgICAgICAgICAgICAgIGxhYmVsOiBjbG9zZXN0TG9jYXRpb24udGl0bGUsXHJcbiAgICAgICAgICAgICAgICB2YWx1ZTogY2xvc2VzdExvY2F0aW9uLmlkLFxyXG4gICAgICAgICAgICAgICAgbGF0aXR1ZGU6IGNsb3Nlc3RMb2NhdGlvbi5sYXQsXHJcbiAgICAgICAgICAgICAgICBsb25naXR1ZGU6IGNsb3Nlc3RMb2NhdGlvbi5sb25nLFxyXG4gICAgICAgICAgICB9KVxyXG5cclxuICAgICAgICAgICAgc2V0T3BlblNldExvY2F0aW9uRGlhbG9nKHRydWUpXHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgLy8gTm8gY2xvc2UgbG9jYXRpb24gZm91bmQgLSB1c2UgY29vcmRpbmF0ZXMgZGlyZWN0bHlcclxuICAgICAgICAgICAgc2V0Q2xvc2VzdExvY2F0aW9uKG51bGwpXHJcbiAgICAgICAgICAgIGhhbmRsZVVzZUNvb3JkaW5hdGVzQXNMb2NhdGlvbihsYXRpdHVkZSwgbG9uZ2l0dWRlKVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAvLyBIZWxwZXIgZnVuY3Rpb24gdG8gdXNlIGNvb3JkaW5hdGVzIHdoZW4gbm8gY2xvc2UgbG9jYXRpb24gaXMgZm91bmRcclxuICAgIGNvbnN0IGhhbmRsZVVzZUNvb3JkaW5hdGVzQXNMb2NhdGlvbiA9IChsYXRpdHVkZTogYW55LCBsb25naXR1ZGU6IGFueSkgPT4ge1xyXG4gICAgICAgIC8vIENsZWFyIGFueSBzZWxlY3RlZCBsb2NhdGlvbiBzaW5jZSB3ZSdyZSB1c2luZyBjb29yZGluYXRlc1xyXG4gICAgICAgIHNldFNlbGVjdGVkTG9jYXRpb24obnVsbClcclxuXHJcbiAgICAgICAgLy8gUGFzcyBjb29yZGluYXRlcyB0byBwYXJlbnQgY29tcG9uZW50XHJcbiAgICAgICAgaGFuZGxlTG9jYXRpb25DaGFuZ2Uoe1xyXG4gICAgICAgICAgICBsYXRpdHVkZTogK2xhdGl0dWRlLFxyXG4gICAgICAgICAgICBsb25naXR1ZGU6ICtsb25naXR1ZGUsXHJcbiAgICAgICAgfSlcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBoYW5kbGVTZXRDdXJyZW50TG9jYXRpb24gPSAoKSA9PiB7XHJcbiAgICAgICAgaWYgKCdnZW9sb2NhdGlvbicgaW4gbmF2aWdhdG9yKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IG9wdGlvbnMgPSB7XHJcbiAgICAgICAgICAgICAgICB0aW1lb3V0OiAzMDAwMCwgLy8gMzAgc2Vjb25kc1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIG5hdmlnYXRvci5nZW9sb2NhdGlvbi5nZXRDdXJyZW50UG9zaXRpb24oXHJcbiAgICAgICAgICAgICAgICAoeyBjb29yZHMgfSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHsgbGF0aXR1ZGUsIGxvbmdpdHVkZSB9ID0gY29vcmRzXHJcbiAgICAgICAgICAgICAgICAgICAgc2V0TG9jYXRpb24oeyBsYXRpdHVkZSwgbG9uZ2l0dWRlIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgc2V0Q3VycmVudExvY2F0aW9uKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgbGF0aXR1ZGU6ICtsYXRpdHVkZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgbG9uZ2l0dWRlOiArbG9uZ2l0dWRlLFxyXG4gICAgICAgICAgICAgICAgICAgIH0pXHJcblxyXG4gICAgICAgICAgICAgICAgICAgIGhhbmRsZUxvY2F0aW9uQ2hhbmdlKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgbGF0aXR1ZGU6ICtsYXRpdHVkZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgbG9uZ2l0dWRlOiArbG9uZ2l0dWRlLFxyXG4gICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgLy8gRmluZCBjbG9zZXN0IGxvY2F0aW9uIGZpcnN0XHJcbiAgICAgICAgICAgICAgICAgICAgZmluZENsb3Nlc3RMb2NhdGlvbihsYXRpdHVkZSwgbG9uZ2l0dWRlKVxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgIChlcnJvcikgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIGxldCBlcnJvck1lc3NhZ2UgPSAnRmFpbGVkIHRvIGdldCBjdXJyZW50IGxvY2F0aW9uJ1xyXG4gICAgICAgICAgICAgICAgICAgIGlmIChlcnJvci5jb2RlID09PSBlcnJvci5QRVJNSVNTSU9OX0RFTklFRCkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBlcnJvck1lc3NhZ2UgPVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgJ0xvY2F0aW9uIGFjY2VzcyBkZW5pZWQuIFBsZWFzZSBlbmFibGUgbG9jYXRpb24gcGVybWlzc2lvbnMuJ1xyXG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoZXJyb3IuY29kZSA9PT0gZXJyb3IuUE9TSVRJT05fVU5BVkFJTEFCTEUpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgZXJyb3JNZXNzYWdlID0gJ0xvY2F0aW9uIGluZm9ybWF0aW9uIHVuYXZhaWxhYmxlLidcclxuICAgICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKGVycm9yLmNvZGUgPT09IGVycm9yLlRJTUVPVVQpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgZXJyb3JNZXNzYWdlID0gJ0xvY2F0aW9uIHJlcXVlc3QgdGltZWQgb3V0LidcclxuICAgICAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIHRvYXN0LmVycm9yKGVycm9yTWVzc2FnZSlcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICBvcHRpb25zLFxyXG4gICAgICAgICAgICApXHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgdG9hc3QuZXJyb3IoJ0dlb2xvY2F0aW9uIGlzIG5vdCBzdXBwb3J0ZWQgYnkgeW91ciBicm93c2VyJylcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgaGFuZGxlU2V0TG9jYXRpb24gPSAoKSA9PiB7XHJcbiAgICAgICAgc2V0U2VsZWN0ZWRMb2NhdGlvbihjbG9zZXN0TG9jYXRpb24pXHJcbiAgICAgICAgaGFuZGxlTG9jYXRpb25DaGFuZ2UoY2xvc2VzdExvY2F0aW9uKVxyXG4gICAgICAgIHNldE9wZW5Mb2NhdGlvbk1vZGFsKGZhbHNlKVxyXG4gICAgICAgIHNldE9wZW5TZXRMb2NhdGlvbkRpYWxvZyhmYWxzZSlcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCB1cGRhdGVMb2NhdGlvbkNvb3JkaW5hdGVzID0gKCkgPT4ge1xyXG4gICAgICAgIGNvbnN0IGxhdGl0dWRlID0gKFxyXG4gICAgICAgICAgICBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnbG9jYXRpb24tbGF0aXR1ZGUnKSBhcyBIVE1MSW5wdXRFbGVtZW50XHJcbiAgICAgICAgKS52YWx1ZVxyXG4gICAgICAgIGNvbnN0IGxvbmdpdHVkZSA9IChcclxuICAgICAgICAgICAgZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ2xvY2F0aW9uLWxvbmdpdHVkZScpIGFzIEhUTUxJbnB1dEVsZW1lbnRcclxuICAgICAgICApLnZhbHVlXHJcblxyXG4gICAgICAgIC8vIFVwZGF0ZSBsb2NhbCBzdGF0ZVxyXG4gICAgICAgIHNldExvY2F0aW9uKHsgbGF0aXR1ZGU6ICtsYXRpdHVkZSwgbG9uZ2l0dWRlOiArbG9uZ2l0dWRlIH0pXHJcblxyXG4gICAgICAgIC8vIFVwZGF0ZSBwYXJlbnQgY29tcG9uZW50IHN0YXRlXHJcbiAgICAgICAgc2V0Q3VycmVudExvY2F0aW9uKHtcclxuICAgICAgICAgICAgbGF0aXR1ZGU6ICtsYXRpdHVkZSxcclxuICAgICAgICAgICAgbG9uZ2l0dWRlOiArbG9uZ2l0dWRlLFxyXG4gICAgICAgIH0pXHJcblxyXG4gICAgICAgIC8vIENsZWFyIHNlbGVjdGVkTG9jYXRpb24gd2hlbiBtYW51YWxseSBlbnRlcmluZyBjb29yZGluYXRlc1xyXG4gICAgICAgIHNldFNlbGVjdGVkTG9jYXRpb24obnVsbClcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBbZ2V0R2VvTG9jYXRpb25zXSA9IHVzZUxhenlRdWVyeShHRVRfR0VPX0xPQ0FUSU9OUywge1xyXG4gICAgICAgIGZldGNoUG9saWN5OiAnY2FjaGUtYW5kLW5ldHdvcmsnLFxyXG4gICAgICAgIG9uQ29tcGxldGVkOiAocmVzcG9uc2U6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UucmVhZEdlb0xvY2F0aW9ucy5ub2Rlc1xyXG4gICAgICAgICAgICBpZiAoZGF0YSkge1xyXG4gICAgICAgICAgICAgICAgc2V0R2VvTG9jYXRpb25zKGRhdGEpXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9LFxyXG4gICAgICAgIG9uRXJyb3I6IChlcnJvcjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ3F1ZXJ5R2VvTG9jYXRpb25zIGVycm9yJywgZXJyb3IpXHJcbiAgICAgICAgfSxcclxuICAgIH0pXHJcbiAgICBjb25zdCBsb2FkR2VvTG9jYXRpb25zID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICAgIGlmIChvZmZsaW5lKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCBnZW9sb2NhdGlvbk1vZGVsLmdldEFsbCgpXHJcbiAgICAgICAgICAgIGlmIChkYXRhKSB7XHJcbiAgICAgICAgICAgICAgICBzZXRHZW9Mb2NhdGlvbnMoZGF0YSlcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgIGF3YWl0IGdldEdlb0xvY2F0aW9ucygpXHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgaWYgKGlzTG9hZGluZykge1xyXG4gICAgICAgICAgICBsb2FkR2VvTG9jYXRpb25zKClcclxuICAgICAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKVxyXG4gICAgICAgIH1cclxuICAgIH0sIFtpc0xvYWRpbmddKVxyXG5cclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgaWYgKGN1cnJlbnRFdmVudCkge1xyXG4gICAgICAgICAgICBpZiAoK2N1cnJlbnRFdmVudD8uZ2VvTG9jYXRpb25JRCA+IDApIHtcclxuICAgICAgICAgICAgICAgIGdlb0xvY2F0aW9ucz8uZmluZCgobG9jYXRpb246IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIGlmIChsb2NhdGlvbi5pZCA9PT0gY3VycmVudEV2ZW50Lmdlb0xvY2F0aW9uSUQpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRMb2NhdGlvbih7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogbG9jYXRpb24udGl0bGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogbG9jYXRpb24uaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYXRpdHVkZTogbG9jYXRpb24ubGF0LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbG9uZ2l0dWRlOiBsb2NhdGlvbi5sb25nLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAvLyBDbGVhciBsb2NhdGlvbiBjb29yZGluYXRlcyB3aGVuIGEgbG9jYXRpb24gaXMgc2VsZWN0ZWRcclxuICAgICAgICAgICAgICAgIHNldExvY2F0aW9uKHsgbGF0aXR1ZGU6IDAsIGxvbmdpdHVkZTogMCB9KVxyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgaWYgKGN1cnJlbnRFdmVudD8ubGF0ICYmIGN1cnJlbnRFdmVudD8ubG9uZykge1xyXG4gICAgICAgICAgICAgICAgICAgIC8vIFNldCBjb29yZGluYXRlcyBhbmQgY2xlYXIgc2VsZWN0ZWQgbG9jYXRpb25cclxuICAgICAgICAgICAgICAgICAgICBzZXRMb2NhdGlvbih7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGxhdGl0dWRlOiBjdXJyZW50RXZlbnQubGF0LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBsb25naXR1ZGU6IGN1cnJlbnRFdmVudC5sb25nLFxyXG4gICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRMb2NhdGlvbihudWxsKVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfSwgW2N1cnJlbnRFdmVudCwgZ2VvTG9jYXRpb25zXSlcclxuXHJcbiAgICAvLyBJbml0aWFsaXplIGZvcm0gZGF0YSB3aGVuIGxvY2F0aW9uIGNoYW5nZXNcclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgLy8gSW5pdGlhbGl6ZSBmb3JtIGRhdGEgd2l0aCBjdXJyZW50IGxvY2F0aW9uIGNvb3JkaW5hdGVzXHJcbiAgICAgICAgc2V0TmV3TG9jYXRpb25Gb3JtRGF0YSgocHJldikgPT4gKHtcclxuICAgICAgICAgICAgLi4ucHJldixcclxuICAgICAgICAgICAgbGF0aXR1ZGU6IGxvY2F0aW9uLmxhdGl0dWRlID8gbG9jYXRpb24ubGF0aXR1ZGUudG9TdHJpbmcoKSA6ICcnLFxyXG4gICAgICAgICAgICBsb25naXR1ZGU6IGxvY2F0aW9uLmxvbmdpdHVkZSA/IGxvY2F0aW9uLmxvbmdpdHVkZS50b1N0cmluZygpIDogJycsXHJcbiAgICAgICAgfSkpXHJcblxyXG4gICAgICAgIC8vIFZhbGlkYXRlIHRoZSBmb3JtIGFmdGVyIGEgc2hvcnQgZGVsYXlcclxuICAgICAgICBzZXRUaW1lb3V0KHZhbGlkYXRlTmV3TG9jYXRpb25Gb3JtLCAxMDApXHJcbiAgICB9LCBbbG9jYXRpb25dKVxyXG4gICAgLy8gQ3JlYXRlIHJlZnMgZm9yIGxhdGl0dWRlIGFuZCBsb25naXR1ZGUgaW5wdXRzXHJcbiAgICBjb25zdCBsYXRJbnB1dFJlZiA9IHVzZVJlZjxIVE1MSW5wdXRFbGVtZW50IHwgbnVsbD4obnVsbClcclxuICAgIGNvbnN0IGxvbmdJbnB1dFJlZiA9IHVzZVJlZjxIVE1MSW5wdXRFbGVtZW50IHwgbnVsbD4obnVsbClcclxuXHJcbiAgICAvLyBTdGF0ZSBmb3IgdGhlIGNvbWJpbmVkIGxvY2F0aW9uIG1vZGFsXHJcbiAgICBjb25zdCBbb3BlbkxvY2F0aW9uTW9kYWwsIHNldE9wZW5Mb2NhdGlvbk1vZGFsXSA9IHVzZVN0YXRlPGJvb2xlYW4+KGZhbHNlKVxyXG4gICAgY29uc3QgW2xvY2F0aW9uT3B0aW9uLCBzZXRMb2NhdGlvbk9wdGlvbl0gPSB1c2VTdGF0ZTxzdHJpbmc+KCdzZWxlY3QnKVxyXG4gICAgLy8gV2UnbGwgdXNlIHRoZSB2YWxpZGF0ZU5ld0xvY2F0aW9uRm9ybSBmdW5jdGlvbiBkaXJlY3RseSBpbnN0ZWFkIG9mIHN0b3JpbmcgdGhlIHN0YXRlXHJcbiAgICBjb25zdCBbbmV3TG9jYXRpb25Gb3JtRGF0YSwgc2V0TmV3TG9jYXRpb25Gb3JtRGF0YV0gPSB1c2VTdGF0ZSh7XHJcbiAgICAgICAgdGl0bGU6ICcnLFxyXG4gICAgICAgIGxhdGl0dWRlOiAnJyxcclxuICAgICAgICBsb25naXR1ZGU6ICcnLFxyXG4gICAgfSlcclxuXHJcbiAgICAvLyBIYW5kbGUgbG9jYXRpb24gb3B0aW9uIGNoYW5nZSBpbiB0aGUgbW9kYWxcclxuICAgIGNvbnN0IGhhbmRsZUxvY2F0aW9uT3B0aW9uQ2hhbmdlID0gKHZhbHVlOiBzdHJpbmcpID0+IHtcclxuICAgICAgICBzZXRMb2NhdGlvbk9wdGlvbih2YWx1ZSlcclxuXHJcbiAgICAgICAgLy8gUmVzZXQgb3RoZXIgaW5wdXRzIHdoZW4gY2hhbmdpbmcgb3B0aW9uc1xyXG4gICAgICAgIGlmICh2YWx1ZSA9PT0gJ2Nvb3JkaW5hdGVzJykge1xyXG4gICAgICAgICAgICAvLyBTaG93IGNvb3JkaW5hdGVzIGlucHV0XHJcbiAgICAgICAgICAgIHNldE5ld0xvY2F0aW9uKGZhbHNlKVxyXG4gICAgICAgICAgICAvLyBDbGVhciBzZWxlY3RlZExvY2F0aW9uIHdoZW4gc3dpdGNoaW5nIHRvIGNvb3JkaW5hdGVzXHJcbiAgICAgICAgICAgIHNldFNlbGVjdGVkTG9jYXRpb24obnVsbClcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBpZiAodmFsdWUgPT09ICdjdXJyZW50Jykge1xyXG4gICAgICAgICAgICAgICAgLy8gVHJpZ2dlciBjdXJyZW50IGxvY2F0aW9uIGRldGVjdGlvblxyXG4gICAgICAgICAgICAgICAgaGFuZGxlU2V0Q3VycmVudExvY2F0aW9uKClcclxuICAgICAgICAgICAgfSBlbHNlIGlmICh2YWx1ZSA9PT0gJ25ldycpIHtcclxuICAgICAgICAgICAgICAgIC8vIFNob3cgbmV3IGxvY2F0aW9uIGZvcm0gaW4gdGhlIGN1cnJlbnQgbW9kYWxcclxuICAgICAgICAgICAgICAgIHNldE5ld0xvY2F0aW9uKHRydWUpXHJcbiAgICAgICAgICAgICAgICAvLyBEb24ndCBvcGVuIHRoZSBzZXBhcmF0ZSBtb2RhbCBkaWFsb2dcclxuICAgICAgICAgICAgICAgIC8vIHNldE9wZW5OZXdMb2NhdGlvbkRpYWxvZyh0cnVlKVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vIEdldCBkaXNwbGF5IHRleHQgZm9yIHRoZSBsb2NhdGlvbiBmaWVsZFxyXG4gICAgY29uc3QgZ2V0TG9jYXRpb25EaXNwbGF5VGV4dCA9ICgpID0+IHtcclxuICAgICAgICBpZiAoc2VsZWN0ZWRMb2NhdGlvbj8ubGFiZWwpIHtcclxuICAgICAgICAgICAgcmV0dXJuIHNlbGVjdGVkTG9jYXRpb24ubGFiZWxcclxuICAgICAgICB9IGVsc2UgaWYgKFxyXG4gICAgICAgICAgICAvLyBDaGVjayBpZiB3ZSBoYXZlIHZhbGlkIGNvb3JkaW5hdGVzIHRvIGRpc3BsYXlcclxuICAgICAgICAgICAgKGxvY2F0aW9uLmxhdGl0dWRlICE9PSB1bmRlZmluZWQgJiZcclxuICAgICAgICAgICAgICAgIGxvY2F0aW9uLmxhdGl0dWRlICE9PSBudWxsICYmXHJcbiAgICAgICAgICAgICAgICBsb2NhdGlvbi5sYXRpdHVkZSAhPT0gMCkgfHxcclxuICAgICAgICAgICAgKGxvY2F0aW9uLmxvbmdpdHVkZSAhPT0gdW5kZWZpbmVkICYmXHJcbiAgICAgICAgICAgICAgICBsb2NhdGlvbi5sb25naXR1ZGUgIT09IG51bGwgJiZcclxuICAgICAgICAgICAgICAgIGxvY2F0aW9uLmxvbmdpdHVkZSAhPT0gMClcclxuICAgICAgICApIHtcclxuICAgICAgICAgICAgLy8gRm9ybWF0IGNvb3JkaW5hdGVzIHdpdGggZml4ZWQgZGVjaW1hbCBwbGFjZXMgZm9yIGJldHRlciByZWFkYWJpbGl0eVxyXG4gICAgICAgICAgICByZXR1cm4gYCR7TnVtYmVyKGxvY2F0aW9uLmxhdGl0dWRlKS50b0ZpeGVkKDYpfSwgJHtOdW1iZXIobG9jYXRpb24ubG9uZ2l0dWRlKS50b0ZpeGVkKDYpfWBcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICByZXR1cm4gJ1NlbGVjdCBsb2NhdGlvbidcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLy8gSGFuZGxlIHRoZSBcIlVzZSBMb2NhdGlvblwiIGJ1dHRvbiBpbiB0aGUgbW9kYWxcclxuICAgIGNvbnN0IGhhbmRsZVVzZUxvY2F0aW9uID0gKCkgPT4ge1xyXG4gICAgICAgIGlmIChsb2NhdGlvbk9wdGlvbiA9PT0gJ2Nvb3JkaW5hdGVzJykge1xyXG4gICAgICAgICAgICAvLyBNYWtlIHN1cmUgY29vcmRpbmF0ZXMgYXJlIHByb3Blcmx5IHNldFxyXG4gICAgICAgICAgICBoaWRlTmV3TG9jYXRpb24oKVxyXG4gICAgICAgICAgICAvLyBDbGVhciBzZWxlY3RlZExvY2F0aW9uIHdoZW4gdXNpbmcgY29vcmRpbmF0ZXNcclxuICAgICAgICAgICAgc2V0U2VsZWN0ZWRMb2NhdGlvbihudWxsKVxyXG4gICAgICAgICAgICAvLyBQYXNzIGNvb3JkaW5hdGVzIHRvIHBhcmVudCBjb21wb25lbnRcclxuICAgICAgICAgICAgaGFuZGxlTG9jYXRpb25DaGFuZ2Uoe1xyXG4gICAgICAgICAgICAgICAgbGF0aXR1ZGU6IGxvY2F0aW9uLmxhdGl0dWRlLFxyXG4gICAgICAgICAgICAgICAgbG9uZ2l0dWRlOiBsb2NhdGlvbi5sb25naXR1ZGUsXHJcbiAgICAgICAgICAgIH0pXHJcbiAgICAgICAgfVxyXG4gICAgICAgIHNldE9wZW5Mb2NhdGlvbk1vZGFsKGZhbHNlKVxyXG4gICAgfVxyXG5cclxuICAgIC8vIFZhbGlkYXRlIHRoZSBuZXcgbG9jYXRpb24gZm9ybVxyXG4gICAgY29uc3QgdmFsaWRhdGVOZXdMb2NhdGlvbkZvcm0gPSAoKSA9PiB7XHJcbiAgICAgICAgY29uc3QgeyB0aXRsZSwgbGF0aXR1ZGUsIGxvbmdpdHVkZSB9ID0gbmV3TG9jYXRpb25Gb3JtRGF0YVxyXG4gICAgICAgIGNvbnN0IGlzVmFsaWQgPVxyXG4gICAgICAgICAgICB0aXRsZS50cmltKCkgIT09ICcnICYmXHJcbiAgICAgICAgICAgIGxhdGl0dWRlLnRyaW0oKSAhPT0gJycgJiZcclxuICAgICAgICAgICAgbG9uZ2l0dWRlLnRyaW0oKSAhPT0gJycgJiZcclxuICAgICAgICAgICAgIWlzTmFOKHBhcnNlRmxvYXQobGF0aXR1ZGUpKSAmJlxyXG4gICAgICAgICAgICAhaXNOYU4ocGFyc2VGbG9hdChsb25naXR1ZGUpKVxyXG5cclxuICAgICAgICByZXR1cm4gaXNWYWxpZFxyXG4gICAgfVxyXG5cclxuICAgIC8vIEhhbmRsZSBpbnB1dCBjaGFuZ2VzIGZvciB0aGUgbmV3IGxvY2F0aW9uIGZvcm1cclxuICAgIGNvbnN0IGhhbmRsZU5ld0xvY2F0aW9uSW5wdXRDaGFuZ2UgPSAoXHJcbiAgICAgICAgZTogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD4sXHJcbiAgICApID0+IHtcclxuICAgICAgICBjb25zdCB7IG5hbWUsIHZhbHVlIH0gPSBlLnRhcmdldFxyXG4gICAgICAgIHNldE5ld0xvY2F0aW9uRm9ybURhdGEoKHByZXYpID0+ICh7XHJcbiAgICAgICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgICAgIFtuYW1lXTogdmFsdWUsXHJcbiAgICAgICAgfSkpXHJcblxyXG4gICAgICAgIC8vIFZhbGlkYXRlIGFmdGVyIGEgc2hvcnQgZGVsYXkgdG8gYXZvaWQgZXhjZXNzaXZlIHZhbGlkYXRpb25cclxuICAgICAgICBzZXRUaW1lb3V0KHZhbGlkYXRlTmV3TG9jYXRpb25Gb3JtLCAxMDApXHJcbiAgICB9XHJcblxyXG4gICAgLy8gSGFuZGxlIHRoZSBcIkFkZCBMb2NhdGlvblwiIGJ1dHRvbiBpbiB0aGUgbW9kYWxcclxuICAgIGNvbnN0IGhhbmRsZUFkZExvY2F0aW9uID0gKCkgPT4ge1xyXG4gICAgICAgIC8vIFZhbGlkYXRlIGZvcm0gYmVmb3JlIHN1Ym1pdHRpbmdcclxuICAgICAgICBpZiAodmFsaWRhdGVOZXdMb2NhdGlvbkZvcm0oKSkge1xyXG4gICAgICAgICAgICAvLyBTZXQgdGhlIGlucHV0IHZhbHVlcyB0byBtYXRjaCBvdXIgc3RhdGUgYmVmb3JlIGNhbGxpbmcgaGFuZGxlQ3JlYXRlTmV3TG9jYXRpb25cclxuICAgICAgICAgICAgY29uc3QgdGl0bGVJbnB1dCA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKFxyXG4gICAgICAgICAgICAgICAgJ25ldy1sb2NhdGlvbi10aXRsZScsXHJcbiAgICAgICAgICAgICkgYXMgSFRNTElucHV0RWxlbWVudFxyXG4gICAgICAgICAgICBjb25zdCBsYXRJbnB1dCA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKFxyXG4gICAgICAgICAgICAgICAgJ25ldy1sb2NhdGlvbi1sYXRpdHVkZScsXHJcbiAgICAgICAgICAgICkgYXMgSFRNTElucHV0RWxlbWVudFxyXG4gICAgICAgICAgICBjb25zdCBsb25nSW5wdXQgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZChcclxuICAgICAgICAgICAgICAgICduZXctbG9jYXRpb24tbG9uZ2l0dWRlJyxcclxuICAgICAgICAgICAgKSBhcyBIVE1MSW5wdXRFbGVtZW50XHJcblxyXG4gICAgICAgICAgICBpZiAodGl0bGVJbnB1dCAmJiBsYXRJbnB1dCAmJiBsb25nSW5wdXQpIHtcclxuICAgICAgICAgICAgICAgIHRpdGxlSW5wdXQudmFsdWUgPSBuZXdMb2NhdGlvbkZvcm1EYXRhLnRpdGxlXHJcbiAgICAgICAgICAgICAgICBsYXRJbnB1dC52YWx1ZSA9IG5ld0xvY2F0aW9uRm9ybURhdGEubGF0aXR1ZGVcclxuICAgICAgICAgICAgICAgIGxvbmdJbnB1dC52YWx1ZSA9IG5ld0xvY2F0aW9uRm9ybURhdGEubG9uZ2l0dWRlXHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIGhhbmRsZUNyZWF0ZU5ld0xvY2F0aW9uKClcclxuICAgICAgICAgICAgc2V0T3BlbkxvY2F0aW9uTW9kYWwoZmFsc2UpXHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgdG9hc3QuZXJyb3IoJ1BsZWFzZSBmaWxsIGluIGFsbCByZXF1aXJlZCBmaWVsZHMgd2l0aCB2YWxpZCB2YWx1ZXMnKVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAvLyBPdmVycmlkZSB0aGUgZXhpc3RpbmcgaGFuZGxlcnMgdG8gY2xvc2UgdGhlIG1vZGFsXHJcbiAgICBjb25zdCBvcmlnaW5hbEhhbmRsZVNldExvY2F0aW9uQ2hhbmdlID0gaGFuZGxlU2V0TG9jYXRpb25DaGFuZ2VcclxuICAgIGNvbnN0IGhhbmRsZVNldExvY2F0aW9uQ2hhbmdlV2l0aE1vZGFsID0gKHNlbGVjdGVkTG9jYXRpb246IGFueSkgPT4ge1xyXG4gICAgICAgIG9yaWdpbmFsSGFuZGxlU2V0TG9jYXRpb25DaGFuZ2Uoc2VsZWN0ZWRMb2NhdGlvbilcclxuICAgICAgICBpZiAoc2VsZWN0ZWRMb2NhdGlvbiAmJiBzZWxlY3RlZExvY2F0aW9uLnZhbHVlICE9PSAnbmV3TG9jYXRpb24nKSB7XHJcbiAgICAgICAgICAgIHNldE9wZW5Mb2NhdGlvbk1vZGFsKGZhbHNlKVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gKFxyXG4gICAgICAgIDw+XHJcbiAgICAgICAgICAgIHsvKiBNYWluIGRpc3BsYXkgYXJlYSB3aXRoIGxvY2F0aW9uIGFuZCBidXR0b24gKi99XHJcbiAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgIGljb25SaWdodD17PENoZXZyb25Eb3duIGNsYXNzTmFtZT1cInRleHQtb3V0ZXItYm9yZGVyLW91dGVyLXNwYWNlLTQwMFwiIC8+fVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGp1c3RpZnktYmV0d2VlbiBtYXgtaC0xMSB0ZXh0LWJhc2VcIlxyXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgIHNldExvY2F0aW9uT3B0aW9uKCdzZWxlY3QnKVxyXG4gICAgICAgICAgICAgICAgICAgIHNldE9wZW5Mb2NhdGlvbk1vZGFsKHRydWUpXHJcbiAgICAgICAgICAgICAgICB9fT5cclxuICAgICAgICAgICAgICAgIHtnZXRMb2NhdGlvbkRpc3BsYXlUZXh0KCl9XHJcbiAgICAgICAgICAgIDwvQnV0dG9uPlxyXG5cclxuICAgICAgICAgICAgey8qIENvbWJpbmVkIExvY2F0aW9uIE1vZGFsICovfVxyXG4gICAgICAgICAgICA8QWxlcnREaWFsb2dOZXdcclxuICAgICAgICAgICAgICAgIG9wZW5EaWFsb2c9e29wZW5Mb2NhdGlvbk1vZGFsfVxyXG4gICAgICAgICAgICAgICAgc2V0T3BlbkRpYWxvZz17c2V0T3BlbkxvY2F0aW9uTW9kYWx9XHJcbiAgICAgICAgICAgICAgICBoYW5kbGVDcmVhdGU9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBpZiAobG9jYXRpb25PcHRpb24gPT09ICdjb29yZGluYXRlcycpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlVXNlTG9jYXRpb24oKVxyXG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAobG9jYXRpb25PcHRpb24gPT09ICdjdXJyZW50Jykge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBGb3IgY3VycmVudCBsb2NhdGlvbiwgY2hlY2sgaWYgd2UgaGF2ZSBjb29yZGluYXRlcyBidXQgbm8gY2xvc2VzdCBsb2NhdGlvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAhY2xvc2VzdExvY2F0aW9uPy5sYWJlbCAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKGxvY2F0aW9uLmxhdGl0dWRlICE9PSAwIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbG9jYXRpb24ubG9uZ2l0dWRlICE9PSAwKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICApIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIENvb3JkaW5hdGVzIGFyZSBhbHJlYWR5IHNldCB2aWEgaGFuZGxlVXNlQ29vcmRpbmF0ZXNBc0xvY2F0aW9uLCBqdXN0IGNsb3NlIG1vZGFsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRPcGVuTG9jYXRpb25Nb2RhbChmYWxzZSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIH0gZWxzZSBpZiAoY2xvc2VzdExvY2F0aW9uPy5sYWJlbCkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gVGhlcmUncyBhIGNsb3Nlc3QgbG9jYXRpb24gYXZhaWxhYmxlLCBidXQgdXNlciBuZWVkcyB0byBjbGljayB0aGUgXCJVc2UgW0xvY2F0aW9uXVwiIGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gRG9uJ3QgY2xvc2UgbW9kYWwsIGxldCB0aGVtIGNob29zZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gcmV0dXJuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBObyBjb29yZGluYXRlcyB5ZXQsIHRoaXMgc2hvdWxkbid0IGhhcHBlbiBidXQgaGFuZGxlIGdyYWNlZnVsbHlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldE9wZW5Mb2NhdGlvbk1vZGFsKGZhbHNlKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIGlmIChsb2NhdGlvbk9wdGlvbiA9PT0gJ25ldycpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gT25seSBjYWxsIGhhbmRsZUFkZExvY2F0aW9uIGlmIHRoZSBmb3JtIGlzIHZhbGlkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICh2YWxpZGF0ZU5ld0xvY2F0aW9uRm9ybSgpKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVBZGRMb2NhdGlvbigpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0b2FzdC5lcnJvcihcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnUGxlYXNlIGZpbGwgaW4gYWxsIHJlcXVpcmVkIGZpZWxkcyB3aXRoIHZhbGlkIHZhbHVlcycsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBSZXR1cm4gd2l0aG91dCBjbG9zaW5nIHRoZSBtb2RhbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBEZWZhdWx0IGNhc2UgZm9yICdzZWxlY3QnIG9wdGlvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICBzZXRPcGVuTG9jYXRpb25Nb2RhbChmYWxzZSlcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgYWN0aW9uVGV4dD17XHJcbiAgICAgICAgICAgICAgICAgICAgbG9jYXRpb25PcHRpb24gPT09ICdjb29yZGluYXRlcydcclxuICAgICAgICAgICAgICAgICAgICAgICAgPyAnVXNlIExvY2F0aW9uJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICA6IGxvY2F0aW9uT3B0aW9uID09PSAnY3VycmVudCdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA/ICFjbG9zZXN0TG9jYXRpb24/LmxhYmVsICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAobG9jYXRpb24ubGF0aXR1ZGUgIT09IDAgfHxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsb2NhdGlvbi5sb25naXR1ZGUgIT09IDApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ1VzZSBHUFMgQ29vcmRpbmF0ZXMnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ0dldCBMb2NhdGlvbidcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA6IGxvY2F0aW9uT3B0aW9uID09PSAnbmV3J1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnQWRkIExvY2F0aW9uJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnT0snXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB0aXRsZT1cIlNlbGVjdCBsb2NhdGlvblwiXHJcbiAgICAgICAgICAgICAgICBzaXplPVwibWRcIj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBnYXAtNVwiPlxyXG4gICAgICAgICAgICAgICAgICAgIHsvKiBSYWRpbyBvcHRpb25zIGZvciBsb2NhdGlvbiBzZWxlY3Rpb24gbWV0aG9kICovfVxyXG4gICAgICAgICAgICAgICAgICAgIDxSYWRpb0dyb3VwXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtsb2NhdGlvbk9wdGlvbn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgb25WYWx1ZUNoYW5nZT17aGFuZGxlTG9jYXRpb25PcHRpb25DaGFuZ2V9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInNwYWNlLXktOFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7LyogT3B0aW9uIDE6IFNlbGVjdCBmcm9tIGRyb3Bkb3duICovfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBmbGV4IGdhcC0yLjVcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxSYWRpb0dyb3VwSXRlbVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJtZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9XCJzZWxlY3RcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwic2VsZWN0LWxvY2F0aW9uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWJhc2Ugdy1mdWxsIGZvbnQtbWVkaXVtXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbD1cIlNlbGVjdCBmcm9tIGxvY2F0aW9uc1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaHRtbEZvcj1cInNlbGVjdC1sb2NhdGlvblwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtsb2NhdGlvbk9wdGlvbiA9PT0gJ3NlbGVjdCcgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1yb3cgdy1mdWxsXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q29tYm9ib3hcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvcHRpb25zPXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZ2VvTG9jYXRpb25zXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogJyBBZGQgTmV3IExvY2F0aW9uJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogJ25ld0xvY2F0aW9uJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5hbGxMb2NhdGlvbnMoKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBbXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw6ICcgQWRkIE5ldyBMb2NhdGlvbicsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6ICduZXdMb2NhdGlvbicsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtzZWxlY3RlZExvY2F0aW9ufVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlU2V0TG9jYXRpb25DaGFuZ2VXaXRoTW9kYWxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWxlY3QgbG9jYXRpb25cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgey8qIE9wdGlvbiAyOiBFbnRlciBjb29yZGluYXRlcyAqL31cclxuICAgICAgICAgICAgICAgICAgICAgICAge3Nob3dVc2VDb29yZGluYXRlcyAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTIuNVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxSYWRpb0dyb3VwSXRlbVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwibWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT1cImNvb3JkaW5hdGVzXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJlbnRlci1jb29yZGluYXRlc1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJFbnRlciBjb29yZGluYXRlc1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGh0bWxGb3I9XCJlbnRlci1jb29yZGluYXRlc1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtYmFzZSBmb250LW1lZGl1bVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7bG9jYXRpb25PcHRpb24gPT09ICdjb29yZGluYXRlcycgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGdhcC00IHctZnVsbCBwLTQgYm9yZGVyIGJvcmRlci1vdXRlci1zcGFjZS00MDAgYm9yZGVyLWRhc2hlZCByb3VuZGVkLWxnIGJnLWNhcmRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgZ2FwLTQgdy1mdWxsXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJMYXRpdHVkZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaHRtbEZvcj1cImxvY2F0aW9uLWxhdGl0dWRlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwibG9jYXRpb24tbGF0aXR1ZGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lPVwibGF0aXR1ZGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVmPXtsYXRJbnB1dFJlZn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbG9jYXRpb24ubGF0aXR1ZGVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB1cGRhdGVMb2NhdGlvbkNvb3JkaW5hdGVzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciBsYXRpdHVkZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJMb25naXR1ZGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGh0bWxGb3I9XCJsb2NhdGlvbi1sb25naXR1ZGVcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJsb2NhdGlvbi1sb25naXR1ZGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lPVwibG9uZ2l0dWRlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlZj17bG9uZ0lucHV0UmVmfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsb2NhdGlvbi5sb25naXR1ZGVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB1cGRhdGVMb2NhdGlvbkNvb3JkaW5hdGVzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciBsb25naXR1ZGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGxcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBpdGFsaWNcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBFbnRlciBjb29yZGluYXRlcyBpblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRlY2ltYWwgZGVncmVlcyBmb3JtYXRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoZS5nLiwgMzcuODAyNTUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLTEyMi40MTQ2MylcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsobG9jYXRpb24ubGF0aXR1ZGUgIT09IDAgfHxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxvY2F0aW9uLmxvbmdpdHVkZSAhPT1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAwKSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTIgcC0zIGJnLW11dGVkIHJvdW5kZWQtbWRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgQ3VycmVudCBjb29yZGluYXRlczp7JyAnfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7TnVtYmVyKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbG9jYXRpb24ubGF0aXR1ZGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkudG9GaXhlZCg2KX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLHsnICd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtOdW1iZXIoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsb2NhdGlvbi5sb25naXR1ZGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkudG9GaXhlZCg2KX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBPcHRpb24gMzogVXNlIGN1cnJlbnQgbG9jYXRpb24gKi99XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtzaG93Q3VycmVudExvY2F0aW9uICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMi41XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFJhZGlvR3JvdXBJdGVtXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJtZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPVwiY3VycmVudFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwiY3VycmVudC1sb2NhdGlvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJVc2UgY3VycmVudCBsb2NhdGlvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGh0bWxGb3I9XCJjdXJyZW50LWxvY2F0aW9uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1iYXNlIGZvbnQtbWVkaXVtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEF1dG9tYXRpY2FsbHkgZGV0ZWN0cyB5b3VyIGxvY2F0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYW5kIHVzZXMgdGhlIG5lYXJlc3QgYXZhaWxhYmxlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbG9jYXRpb24gb3IgR1BTIGNvb3JkaW5hdGVzLlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2xvY2F0aW9uT3B0aW9uID09PSAnY3VycmVudCcgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogU2hvdyBjbG9zZXN0IGxvY2F0aW9uIGRpYWxvZyBpZiBhdmFpbGFibGUgKi99XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjbG9zZXN0TG9jYXRpb24/LmxhYmVsICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IGJvcmRlciByb3VuZGVkLWxnIGJvcmRlci1vdXRlci1zcGFjZS00MDAgYm9yZGVyLWRhc2hlZCBiZy1jYXJkXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFAgY2xhc3NOYW1lPVwibWItMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBBcmUgeW91IGlueycgJ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xvc2VzdExvY2F0aW9uPy5sYWJlbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID9cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1A+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpY29uTGVmdD17TWFwUGlufVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZVNldExvY2F0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFVzZXsnICd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsb3Nlc3RMb2NhdGlvbj8ubGFiZWxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogU2hvdyBjb29yZGluYXRlcyBpZiBubyBjbG9zZSBsb2NhdGlvbiBmb3VuZCAqL31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyFjbG9zZXN0TG9jYXRpb24/LmxhYmVsICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAobG9jYXRpb24ubGF0aXR1ZGUgIT09IDAgfHxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsb2NhdGlvbi5sb25naXR1ZGUgIT09XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDApICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBib3JkZXIgcm91bmRlZC1sZyBib3JkZXItb3V0ZXItc3BhY2UtNDAwIGJvcmRlci1kYXNoZWQgYmctY2FyZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8UCBjbGFzc05hbWU9XCJtYi0yIGZvbnQtbWVkaXVtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBVc2luZyBHUFNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvb3JkaW5hdGVzOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1A+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxQIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tb25vXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7TnVtYmVyKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxvY2F0aW9uLmxhdGl0dWRlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKS50b0ZpeGVkKDYpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLHsnICd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7TnVtYmVyKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxvY2F0aW9uLmxvbmdpdHVkZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkudG9GaXhlZCg2KX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9QPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8UCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBtdC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBObyBuZWFyYnlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxvY2F0aW9ucyBmb3VuZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgd2l0aGluIHJhbmdlLlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1A+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgey8qIE9wdGlvbiA0OiBBZGQgbmV3IGxvY2F0aW9uICovfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7c2hvd0FkZE5ld0xvY2F0aW9uICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGZsZXggZ2FwLTIuNVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxSYWRpb0dyb3VwSXRlbVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwibWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT1cIm5ld1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwibmV3LWxvY2F0aW9uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbD1cIkFkZCBuZXcgbG9jYXRpb25cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBodG1sRm9yPVwibmV3LWxvY2F0aW9uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1iYXNlIHctZnVsbCBmb250LW1lZGl1bVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7bG9jYXRpb25PcHRpb24gPT09ICduZXcnICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1ncm93IGZsZXggZmxleC1jb2wgZ2FwLTUgcC00IGJnLWJhY2tncm91bmQgcm91bmRlZC1sZyBib3JkZXItb3V0ZXItc3BhY2UtNDAwIGJvcmRlciBib3JkZXItZGFzaGVkXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGdhcC01XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJMb2NhdGlvbiBOYW1lXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGh0bWxGb3I9XCJuZXctbG9jYXRpb24tdGl0bGVcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwibmV3LWxvY2F0aW9uLXRpdGxlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lPVwidGl0bGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIGxvY2F0aW9uIG5hbWVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbmV3TG9jYXRpb25Gb3JtRGF0YS50aXRsZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZU5ld0xvY2F0aW9uSW5wdXRDaGFuZ2VcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogPExhYmVsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbD1cIlBhcmVudCBMb2NhdGlvbiAoT3B0aW9uYWwpXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBodG1sRm9yPVwicGFyZW50LWxvY2F0aW9uXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1yb3cgdy1mdWxsXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENvbWJvYm94XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9wdGlvbnM9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZ2VvTG9jYXRpb25zXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBhbGxMb2NhdGlvbnMoKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogW11cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVQYXJlbnRMb2NhdGlvbkNoYW5nZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VsZWN0IHBhcmVudCBsb2NhdGlvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPiAqL31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbD1cIkxhdGl0dWRlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGh0bWxGb3I9XCJuZXctbG9jYXRpb24tbGF0aXR1ZGVcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwibmV3LWxvY2F0aW9uLWxhdGl0dWRlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lPVwibGF0aXR1ZGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbmV3TG9jYXRpb25Gb3JtRGF0YS5sYXRpdHVkZSB8fFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsb2NhdGlvbi5sYXRpdHVkZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZU5ld0xvY2F0aW9uSW5wdXRDaGFuZ2VcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciBsYXRpdHVkZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJMb25naXR1ZGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaHRtbEZvcj1cIm5ldy1sb2NhdGlvbi1sb25naXR1ZGVcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwibmV3LWxvY2F0aW9uLWxvbmdpdHVkZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbmFtZT1cImxvbmdpdHVkZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBuZXdMb2NhdGlvbkZvcm1EYXRhLmxvbmdpdHVkZSB8fFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsb2NhdGlvbi5sb25naXR1ZGVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVOZXdMb2NhdGlvbklucHV0Q2hhbmdlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgbG9uZ2l0dWRlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICA8L1JhZGlvR3JvdXA+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9BbGVydERpYWxvZ05ldz5cclxuXHJcbiAgICAgICAgICAgIHsvKiBLZWVwIHRoZSBvcmlnaW5hbCBkaWFsb2dzIGZvciBjb21wYXRpYmlsaXR5IHdpdGggZXhpc3RpbmcgY29kZSAqL31cclxuICAgICAgICAgICAgPEFsZXJ0RGlhbG9nTmV3XHJcbiAgICAgICAgICAgICAgICBvcGVuRGlhbG9nPXtvcGVuTmV3TG9jYXRpb25EaWFsb2d9XHJcbiAgICAgICAgICAgICAgICBzZXRPcGVuRGlhbG9nPXtzZXRPcGVuTmV3TG9jYXRpb25EaWFsb2d9XHJcbiAgICAgICAgICAgICAgICBoYW5kbGVDcmVhdGU9e2hhbmRsZUNyZWF0ZU5ld0xvY2F0aW9ufVxyXG4gICAgICAgICAgICAgICAgYWN0aW9uVGV4dD1cIkFkZCBMb2NhdGlvblwiXHJcbiAgICAgICAgICAgICAgICB0aXRsZT1cIkFkZCBOZXcgTG9jYXRpb25cIlxyXG4gICAgICAgICAgICAgICAgc2hvd0ljb249e3RydWV9PlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGdhcC01XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGdhcC01XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJMb2NhdGlvbiBOYW1lXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGh0bWxGb3I9XCJuZXctbG9jYXRpb24tdGl0bGVcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwibmV3LWxvY2F0aW9uLXRpdGxlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lPVwidGl0bGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIGxvY2F0aW9uIG5hbWVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtuZXdMb2NhdGlvbkZvcm1EYXRhLnRpdGxlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVOZXdMb2NhdGlvbklucHV0Q2hhbmdlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7LyogPExhYmVsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGxcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJQYXJlbnQgTG9jYXRpb24gKE9wdGlvbmFsKVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBodG1sRm9yPVwicGFyZW50LWxvY2F0aW9uXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1yb3cgdy1mdWxsXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENvbWJvYm94XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9wdGlvbnM9e2dlb0xvY2F0aW9ucyA/IGFsbExvY2F0aW9ucygpIDogW119XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVQYXJlbnRMb2NhdGlvbkNoYW5nZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWxlY3QgcGFyZW50IGxvY2F0aW9uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+ICovfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cIm5ldy1sb2NhdGlvbi1sYXRpdHVkZVwiIGxhYmVsPVwiTGF0aXR1ZGVcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwibmV3LWxvY2F0aW9uLWxhdGl0dWRlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lPVwibGF0aXR1ZGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbmV3TG9jYXRpb25Gb3JtRGF0YS5sYXRpdHVkZSB8fFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsb2NhdGlvbi5sYXRpdHVkZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlTmV3TG9jYXRpb25JbnB1dENoYW5nZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIGxhdGl0dWRlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbD1cIkxvbmdpdHVkZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBodG1sRm9yPVwibmV3LWxvY2F0aW9uLWxvbmdpdHVkZVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJuZXctbG9jYXRpb24tbG9uZ2l0dWRlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lPVwibG9uZ2l0dWRlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5ld0xvY2F0aW9uRm9ybURhdGEubG9uZ2l0dWRlIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxvY2F0aW9uLmxvbmdpdHVkZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlTmV3TG9jYXRpb25JbnB1dENoYW5nZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIGxvbmdpdHVkZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9BbGVydERpYWxvZ05ldz5cclxuICAgICAgICAgICAgPEFsZXJ0RGlhbG9nTmV3XHJcbiAgICAgICAgICAgICAgICBvcGVuRGlhbG9nPXtvcGVuU2V0TG9jYXRpb25EaWFsb2d9XHJcbiAgICAgICAgICAgICAgICBzZXRPcGVuRGlhbG9nPXtzZXRPcGVuU2V0TG9jYXRpb25EaWFsb2d9XHJcbiAgICAgICAgICAgICAgICBoYW5kbGVDcmVhdGU9e2hhbmRsZUNyZWF0ZU5ld0xvY2F0aW9ufVxyXG4gICAgICAgICAgICAgICAgYWN0aW9uVGV4dD1cIkNyZWF0ZSBMb2NhdGlvblwiXHJcbiAgICAgICAgICAgICAgICB0aXRsZT1cIkN1cnJlbnQgTG9jYXRpb25cIj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBnYXAtNVwiPlxyXG4gICAgICAgICAgICAgICAgICAgIHtjbG9zZXN0TG9jYXRpb24/LmxhYmVsID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgZ2FwLTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjbG9zZXN0TG9jYXRpb24/LmxhYmVsICE9IHVuZGVmaW5lZCA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgZ2FwLTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEFyZSB5b3UgaW4ge2Nsb3Nlc3RMb2NhdGlvbj8ubGFiZWx9P1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1A+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGljb25MZWZ0PXtNYXBQaW59XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVTZXRMb2NhdGlvbn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgVXNlIHtjbG9zZXN0TG9jYXRpb24/LmxhYmVsfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFNlcGFyYXRvciBjbGFzc05hbWU9XCJteS00XCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFAgY2xhc3NOYW1lPVwidXBwZXJjYXNlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBPciBjcmVhdGUgbmV3IGxvY2F0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvUD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1sZ1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBGZXRjaGluZyBjdXJyZW50IGxvY2F0aW9uIHRvb2sgbG9uZywgZG8geW91XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdhbnQgdG8gY3JlYXRlIGEgbmV3IGxvY2F0aW9uIGluc3RlYWQ/XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1sZ1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgRmFpbGVkIHRvIGZldGNoIGN1cnJlbnQgbG9jYXRpb24uIERvIHlvdSB3YW50IHRvXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjcmVhdGUgYSBuZXcgbG9jYXRpb24gaW5zdGVhZD9cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBnYXAtNVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsPVwiTG9jYXRpb24gTmFtZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBodG1sRm9yPVwibmV3LWxvY2F0aW9uLXRpdGxlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZD1cIm5ldy1sb2NhdGlvbi10aXRsZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbmFtZT1cInRpdGxlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciBsb2NhdGlvbiBuYW1lXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17bmV3TG9jYXRpb25Gb3JtRGF0YS50aXRsZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlTmV3TG9jYXRpb25JbnB1dENoYW5nZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9MYWJlbD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgey8qIDxMYWJlbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsPVwiUGFyZW50IExvY2F0aW9uIChPcHRpb25hbClcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaHRtbEZvcj1cInBhcmVudC1sb2NhdGlvblwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtcm93IHctZnVsbFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDb21ib2JveFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvcHRpb25zPXtnZW9Mb2NhdGlvbnMgPyBhbGxMb2NhdGlvbnMoKSA6IFtdfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlUGFyZW50TG9jYXRpb25DaGFuZ2V9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VsZWN0IHBhcmVudCBsb2NhdGlvblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPiAqL31cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbD1cIkxhdGl0dWRlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBodG1sRm9yPVwibmV3LWxvY2F0aW9uLWxhdGl0dWRlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwibmV3LWxvY2F0aW9uLWxhdGl0dWRlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbmFtZT1cImxhdGl0dWRlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5ld0xvY2F0aW9uRm9ybURhdGEubGF0aXR1ZGUgfHxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxvY2F0aW9uLmxhdGl0dWRlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZU5ld0xvY2F0aW9uSW5wdXRDaGFuZ2V9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgbGF0aXR1ZGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJMb25naXR1ZGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGh0bWxGb3I9XCJuZXctbG9jYXRpb24tbG9uZ2l0dWRlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwibmV3LWxvY2F0aW9uLWxvbmdpdHVkZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU9XCJsb25naXR1ZGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbmV3TG9jYXRpb25Gb3JtRGF0YS5sb25naXR1ZGUgfHxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxvY2F0aW9uLmxvbmdpdHVkZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVOZXdMb2NhdGlvbklucHV0Q2hhbmdlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIGxvbmdpdHVkZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvQWxlcnREaWFsb2dOZXc+XHJcbiAgICAgICAgPC8+XHJcbiAgICApXHJcbn1cclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VSZWYiLCJDb21ib2JveCIsIkNSRUFURV9HRU9fTE9DQVRJT04iLCJDcmVhdGVGYXZvcml0ZUxvY2F0aW9uIiwidXNlTGF6eVF1ZXJ5IiwidXNlTXV0YXRpb24iLCJ0b2FzdCIsImlzRW1wdHkiLCJ0cmltIiwiR0VUX0dFT19MT0NBVElPTlMiLCJHZXRGYXZvcml0ZUxvY2F0aW9ucyIsIkZhdm9yaXRlTG9jYXRpb25Nb2RlbCIsIkdlb0xvY2F0aW9uTW9kZWwiLCJnZW5lcmF0ZVVuaXF1ZUlkIiwiSW5wdXQiLCJMYWJlbCIsIkJ1dHRvbiIsIk1hcFBpbiIsIkNoZXZyb25Eb3duIiwiU2VwYXJhdG9yIiwiUmFkaW9Hcm91cCIsIlJhZGlvR3JvdXBJdGVtIiwiQWxlcnREaWFsb2dOZXciLCJQIiwiTG9jYXRpb25GaWVsZCIsInNldEN1cnJlbnRMb2NhdGlvbiIsImhhbmRsZUxvY2F0aW9uQ2hhbmdlIiwiY3VycmVudEV2ZW50Iiwib2ZmbGluZSIsInNob3dBZGROZXdMb2NhdGlvbiIsInNob3dVc2VDb29yZGluYXRlcyIsInNob3dDdXJyZW50TG9jYXRpb24iLCJnZW9Mb2NhdGlvbnMiLCJzZXRHZW9Mb2NhdGlvbnMiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJuZXdMb2NhdGlvbiIsInNldE5ld0xvY2F0aW9uIiwiY2xvc2VzdExvY2F0aW9uIiwic2V0Q2xvc2VzdExvY2F0aW9uIiwic2VsZWN0ZWRMb2NhdGlvbiIsInNldFNlbGVjdGVkTG9jYXRpb24iLCJmYXZvcml0ZUxvY2F0aW9ucyIsInNldEZhdm9yaXRlTG9jYXRpb25zIiwic2VsZWN0ZWRQYXJlbnRMb2NhdGlvbiIsInNldFNlbGVjdGVkUGFyZW50TG9jYXRpb24iLCJsb2NhdGlvbnMiLCJzZXRMb2NhdGlvbnMiLCJvcGVuTmV3TG9jYXRpb25EaWFsb2ciLCJzZXRPcGVuTmV3TG9jYXRpb25EaWFsb2ciLCJvcGVuU2V0TG9jYXRpb25EaWFsb2ciLCJzZXRPcGVuU2V0TG9jYXRpb25EaWFsb2ciLCJsb2NhdGlvbiIsInNldExvY2F0aW9uIiwibGF0aXR1ZGUiLCJsb25naXR1ZGUiLCJmYXZvcml0ZUxvY2F0aW9uTW9kZWwiLCJnZW9sb2NhdGlvbk1vZGVsIiwiZGlzcGxheU5ld0xvY2F0aW9uIiwiaGlkZU5ld0xvY2F0aW9uIiwiaGFuZGxlU2V0TG9jYXRpb25DaGFuZ2UiLCJ2YWx1ZSIsInNldExvY2F0aW9uT3B0aW9uIiwidXNlcklkIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsImV4aXN0aW5nRmF2b3JpdGUiLCJmaW5kIiwiZmF2IiwiZ2VvTG9jYXRpb25JRCIsInVwZGF0ZWRGYXZvcml0ZSIsInVzYWdlIiwic2F2ZSIsInVwZGF0ZWRGYXZvcml0ZXMiLCJtYXAiLCJpZCIsIm5ld0Zhdm9yaXRlIiwibWVtYmVySUQiLCJjcmVhdGVGYXZvcml0ZUxvY2F0aW9uIiwidmFyaWFibGVzIiwiaW5wdXQiLCJvbkNvbXBsZXRlZCIsImRhdGEiLCJnZXRGYXZvcml0ZUxvY2F0aW9ucyIsInVzZXJJRCIsIm9uRXJyb3IiLCJlcnJvciIsImNvbnNvbGUiLCJoYW5kbGVQYXJlbnRMb2NhdGlvbkNoYW5nZSIsImhhbmRsZUNyZWF0ZU5ld0xvY2F0aW9uIiwidGl0bGVJbnB1dCIsImxhdGl0dWRlSW5wdXQiLCJsb25naXR1ZGVJbnB1dCIsImRvY3VtZW50IiwiZ2V0RWxlbWVudEJ5SWQiLCJ0aXRsZSIsInBhcmVudExvY2F0aW9uIiwibGF0TnVtIiwicGFyc2VGbG9hdCIsImxvbmdOdW0iLCJsYXQiLCJpc05hTiIsImxvbmciLCJwYXJlbnRMb2NhdGlvbklEIiwidW5pcXVlSUQiLCJsZW5ndGgiLCJsYWJlbCIsImZhdm9yaXRlSWQiLCJzZXRPcGVuTG9jYXRpb25Nb2RhbCIsImNyZWF0ZUdlb0xvY2F0aW9uIiwicmVzcG9uc2UiLCJmYXZEYXRhIiwib2ZmbGluZUdldEZhdm9yaXRlTG9jYXRpb25zIiwiZ2V0QnlNZW1iZXJJRCIsImZldGNoUG9saWN5IiwicmVhZEZhdm9yaXRlTG9jYXRpb25zIiwibm9kZXMiLCJhbGxMb2NhdGlvbnMiLCJmaWx0ZXJWYWxpZExvY2F0aW9ucyIsImxvY3MiLCJmaWx0ZXIiLCJsb2MiLCJtYXBwZWRMb2NhdGlvbnMiLCJjb21iaW5lZExvY2F0aW9ucyIsInZhbGlkTG9jYXRpb25zIiwic29ydExvY2F0aW9ucyIsImxvY2F0aW9uc1RvU29ydCIsInNvcnQiLCJhIiwiYiIsImFGYXYiLCJiRmF2IiwiZ2V0UHJveGltaXR5IiwiZGlzdGFuY2UiLCJNYXRoIiwic3FydCIsInBvdyIsImZpbmRDbG9zZXN0TG9jYXRpb24iLCJoYW5kbGVVc2VDb29yZGluYXRlc0FzTG9jYXRpb24iLCJyZWR1Y2UiLCJwcmV2IiwiY3VyciIsInByZXZEaXN0YW5jZSIsImN1cnJEaXN0YW5jZSIsInByb3hpbWl0eSIsImhhbmRsZVNldEN1cnJlbnRMb2NhdGlvbiIsIm5hdmlnYXRvciIsIm9wdGlvbnMiLCJ0aW1lb3V0IiwiZ2VvbG9jYXRpb24iLCJnZXRDdXJyZW50UG9zaXRpb24iLCJjb29yZHMiLCJlcnJvck1lc3NhZ2UiLCJjb2RlIiwiUEVSTUlTU0lPTl9ERU5JRUQiLCJQT1NJVElPTl9VTkFWQUlMQUJMRSIsIlRJTUVPVVQiLCJoYW5kbGVTZXRMb2NhdGlvbiIsInVwZGF0ZUxvY2F0aW9uQ29vcmRpbmF0ZXMiLCJnZXRHZW9Mb2NhdGlvbnMiLCJyZWFkR2VvTG9jYXRpb25zIiwibG9hZEdlb0xvY2F0aW9ucyIsImdldEFsbCIsInNldE5ld0xvY2F0aW9uRm9ybURhdGEiLCJ0b1N0cmluZyIsInNldFRpbWVvdXQiLCJ2YWxpZGF0ZU5ld0xvY2F0aW9uRm9ybSIsImxhdElucHV0UmVmIiwibG9uZ0lucHV0UmVmIiwib3BlbkxvY2F0aW9uTW9kYWwiLCJsb2NhdGlvbk9wdGlvbiIsIm5ld0xvY2F0aW9uRm9ybURhdGEiLCJoYW5kbGVMb2NhdGlvbk9wdGlvbkNoYW5nZSIsImdldExvY2F0aW9uRGlzcGxheVRleHQiLCJ1bmRlZmluZWQiLCJOdW1iZXIiLCJ0b0ZpeGVkIiwiaGFuZGxlVXNlTG9jYXRpb24iLCJpc1ZhbGlkIiwiaGFuZGxlTmV3TG9jYXRpb25JbnB1dENoYW5nZSIsImUiLCJuYW1lIiwidGFyZ2V0IiwiaGFuZGxlQWRkTG9jYXRpb24iLCJsYXRJbnB1dCIsImxvbmdJbnB1dCIsIm9yaWdpbmFsSGFuZGxlU2V0TG9jYXRpb25DaGFuZ2UiLCJoYW5kbGVTZXRMb2NhdGlvbkNoYW5nZVdpdGhNb2RhbCIsImljb25SaWdodCIsImNsYXNzTmFtZSIsInZhcmlhbnQiLCJvbkNsaWNrIiwib3BlbkRpYWxvZyIsInNldE9wZW5EaWFsb2ciLCJoYW5kbGVDcmVhdGUiLCJhY3Rpb25UZXh0Iiwic2l6ZSIsImRpdiIsIm9uVmFsdWVDaGFuZ2UiLCJodG1sRm9yIiwib25DaGFuZ2UiLCJwbGFjZWhvbGRlciIsInR5cGUiLCJyZWYiLCJyZXF1aXJlZCIsInAiLCJpY29uTGVmdCIsInNob3dJY29uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/components/location.tsx\n"));

/***/ })

});