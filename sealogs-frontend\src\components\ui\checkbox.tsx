'use client'

import * as React from 'react'
import * as CheckboxPrimitive from '@radix-ui/react-checkbox'
import { Check } from 'lucide-react'
import { cva, type VariantProps } from 'class-variance-authority'

import { cn } from '@/app/lib/utils'
import {
    indicatorColorMap,
    indicatorHoverMap,
    indicatorSizeMap,
} from './radio-group'

const checkboxVariants = cva(
    'peer shrink-0 border shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50',
    {
        variants: {
            variant: {
                default: 'border-primary data-[state=checked]:text-foreground',
                destructive:
                    'border-destructive data-[state=checked]:text-white',
                success:
                    'border-bright-turquoise-600 data-[state=checked]:text-white',
                outline:
                    'border-foreground data-[state=checked]:text-foreground',
                secondary:
                    'border-outer-space-400 data-[state=checked]:text-neutral-400',
                'light-blue':
                    'border-light-blue-vivid-700 data-[state=checked]:text-accent-foreground',
                pink: 'border-pink-vivid-700 data-[state=checked]:text-pink-vivid-700',
                warning:
                    'border-yellow-vivid-700 data-[state=checked]:text-fire-bush-700',
            },
            size: {
                sm: 'size-3',
                default: 'size-4',
                md: 'size-5',
                lg: 'size-7',
            },
            isRadioStyle: {
                true: 'rounded-full aspect-square group flex items-center justify-center border-border relative shadow-[0_2px_0_#FFFFFF33] before:content-[""] before:absolute before:inset-0 before:rounded-full before:shadow-[inset_0_2px_2px_#0000001A]',
                false: 'rounded-sm h-4 w-4',
            },
        },
        defaultVariants: {
            variant: 'default',
            size: 'default',
            isRadioStyle: false,
        },
    },
)

interface CheckboxProps
    extends React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>,
        VariantProps<typeof checkboxVariants> {
    isRadioStyle?: boolean
}

const Checkbox = React.forwardRef<
    React.ElementRef<typeof CheckboxPrimitive.Root>,
    CheckboxProps
>(({ className, variant, size, isRadioStyle, ...props }, ref) => {
    const sizeClass =
        size && isRadioStyle
            ? indicatorSizeMap[size as keyof typeof indicatorSizeMap]
            : indicatorSizeMap.default

    const colorClass =
        variant && isRadioStyle
            ? indicatorColorMap[variant as keyof typeof indicatorColorMap]
            : indicatorColorMap.default

    const hoverClass =
        variant && isRadioStyle
            ? indicatorHoverMap[variant as keyof typeof indicatorHoverMap]
            : indicatorHoverMap.default

    return (
        <CheckboxPrimitive.Root
            ref={ref}
            className={cn(
                checkboxVariants({ variant, size, isRadioStyle }),
                className,
            )}
            {...props}>
            <CheckboxPrimitive.Indicator
                className={cn(
                    'flex items-center justify-center text-current',
                    isRadioStyle ? 'relative z-20' : '',
                )}>
                {isRadioStyle ? (
                    <div
                        className={cn('rounded-full', sizeClass, colorClass)}
                    />
                ) : (
                    <Check className="h-4 w-4" />
                )}
            </CheckboxPrimitive.Indicator>
            {isRadioStyle && (
                <div
                    className={cn(
                        'rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2',
                        'will-change-transform will-change-width will-change-padding transform-gpu',
                        'group-hover:transition-colors group-hover:ease-out group-hover:duration-300',
                        sizeClass,
                        hoverClass,
                    )}
                />
            )}
        </CheckboxPrimitive.Root>
    )
})
Checkbox.displayName = CheckboxPrimitive.Root.displayName

export { Checkbox, type CheckboxProps, checkboxVariants }
