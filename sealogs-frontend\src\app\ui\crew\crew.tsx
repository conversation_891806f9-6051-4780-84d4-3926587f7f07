'use client'
import React, { useEffect, useState } from 'react'
import { useLazyQuery, useMutation } from '@apollo/client'
import {
    UpdateCrewMembers_LogBookEntrySection,
    CreateCrewMembers_LogBookEntrySection,
    Create<PERSON>rewWelfare_LogBookEntrySection,
    DeleteCrewMembers_LogBookEntrySections,
} from '@/app/lib/graphQL/mutation'
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import dayjs from 'dayjs'
import {
    CREW_DUTY,
    CREW_LIST_WITHOUT_TRAINING_STATUS as CREW_LIST,
    CREW_DETAIL_WITH_TRAINING_STATUS as CREW_DETAIL_BY_ID,
    CrewMembers_LogBookEntrySection,
} from '@/app/lib/graphQL/query'
import { LogBookEntryCrewSection } from '../../../../types/logbook-entry-crew-section'

import Crew<PERSON>elfare from '../daily-checks/crew-welfare'
import { useSearchParams } from 'next/navigation'
import { GetCrewListWithTrainingStatus } from '@/app/lib/actions'

import { toast } from 'sonner'
import { formatDBDateTime } from '@/app/helpers/dateHelper'
import SeaLogsMemberModel from '@/app/offline/models/seaLogsMember'
import CrewDutyModel from '@/app/offline/models/crewDuty'
import CrewMembers_LogBookEntrySectionModel from '@/app/offline/models/crewMembers_LogBookEntrySection'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import CrewWelfare_LogBookEntrySectionModel from '@/app/offline/models/crewWelfare_LogBookEntrySection'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import { getFieldName } from '../vessels/actions'
import { SLALL_LogBookFields } from '@/app/lib/logbook-configuration'
import { isEmpty } from 'lodash'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Combobox } from '@/components/ui/comboBox'
import CrewDutyDropdown from '@/components/filter/components/crew-duty-dropdown'
import { Clock, InfoIcon } from 'lucide-react'
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover'
import DatePicker from '@/components/DateRange'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'
import { H2, H3 } from '@/components/ui/typography'
import {
    Avatar,
    AvatarFallback,
    AvatarImage,
    getCrewInitials,
} from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/app/lib/utils'

// Import types from separate file
import {
    CrewMember,
    CrewDuty,
    CrewOption,
    Vessel,
    CrewProps,
    isVessel,
} from './types'
import { Card } from '@/components/ui/card'
import { DivIcon } from 'leaflet'
import { useBreakpoints } from '@/components/hooks/useBreakpoints'
import { formatDateTime } from '@/app/helpers/dateHelper'
import { FooterWrapper } from '@/components/footer-wrapper'
import { FormFooter } from '@/components/ui'
import {
    useResponsiveLabel,
    getResponsiveLabel,
} from '../../../../utils/responsiveLabel'

export default function Crew({
    crewSections = false,
    allCrew,
    logBookEntryID,
    locked,
    logBookConfig = false,
    setCrewMembers,
    crewWelfareCheck,
    updateCrewWelfare,
    vessel = false,
    masterID = 0,
    logEntrySections,
    offline = false,
    crewMembersList,
}: CrewProps) {
    const seaLogsMemberModel = new SeaLogsMemberModel()
    const crewDutyModel = new CrewDutyModel()
    const lbCrewModel = new CrewMembers_LogBookEntrySectionModel()
    const lbWelfareModel = new CrewWelfare_LogBookEntrySectionModel()
    const [allVesselCrews, setAllVesselCrews] = useState<CrewMember[]>([])
    const [allDuties, setAllDuties] = useState<CrewDuty[]>([])
    const searchParams = useSearchParams()
    const vesselID = searchParams.get('vesselID') ?? 0
    const [isLoading, setIsLoading] = useState(true)
    const [loaded, setLoaded] = useState(false)
    const [crewMember, setCrewMember] = useState<CrewOption | null>(null)
    const [duty, setDuty] = useState<{
        label: string
        value: string | number
    } | null>(null)
    const [loginTime, setLoginTime] = useState(new Date())
    // Store logoutTime as a standard Date object or null
    const [logoutTime, setLogoutTime] = useState<Date | null>(null)
    const [duties, setDuties] = useState<CrewDuty[]>([])
    const [crew, setCrew] = useState<any>(crewSections)
    const [crewConfig, setCrewConfig] = useState<any>(true)
    // Field labels and status
    const [punchInStatus, setPunchInStatus] = useState<string>()
    const [punchInLabel, setPunchInLabel] = useState<string>('Sign In')
    const [punchOutStatus, setPunchOutStatus] = useState<string>()
    const [punchOutLabel, setPunchOutLabel] = useState<string>('Sign Out')
    const [workDetailsStatus, setWorkDetailsStatus] = useState<string>()
    const [workDetailsLabel, setWorkDetailsLabel] =
        useState<string>('Work Details')
    // const [editCrew, setEditCrew] = useState(false);
    // const [editCrewMember, setEditCrewMember] = useState(null);
    const [crewManifestEntry, setCrewManifestEntry] = useState<any>({})
    const [openAddCrewMemberDialog, setopenAddCrewMemberDialog] =
        useState(false)

    // Function removed as we're now directly using handleSave
    const [openEditLogoutTimeDialog, setOpenEditLogoutTimeDialog] =
        useState(false)
    const [crewMemberOptions, setCrewMemberOptions] = useState<any>([])
    const [openCrewTrainingDueDialog, setOpenCrewTrainingDueDialog] =
        useState(false)
    const [openConfirmCrewDeleteDialog, setOpenConfirmCrewDeleteDialog] =
        useState(false)
    const [permissions, setPermissions] = useState<any>(false)
    const [edit_logBookEntry, setEdit_logBookEntry] = useState<any>(false)
    const [allMembers, setAllMembers] = useState<any>([])

    const bp = useBreakpoints()

    const init_permissions = () => {
        if (
            permissions &&
            hasPermission(
                process.env.EDIT_LOGBOOKENTRY || 'EDIT_LOGBOOKENTRY',
                permissions,
            )
        ) {
            setEdit_logBookEntry(true)
        } else {
            setEdit_logBookEntry(false)
        }
    }

    const createOfflineCrewWelfareCheck = async () => {
        // I need to add a 2-second delay to fix ConstraintError: Key already exists in the object store.
        const delay = (ms: number) =>
            new Promise((resolve) => setTimeout(resolve, ms))
        await delay(2000)
        const id = generateUniqueId()
        const data = await lbWelfareModel.save({
            id: id,
            logBookEntryID: logBookEntryID,
            fitness: null,
            imSafe: null,
            safetyActions: null,
            waterQuality: null,
            __typename: 'CrewWelfare_LogBookEntrySection',
        })
        updateCrewWelfare(data)
    }

    useEffect(() => {
        setPermissions(getPermissions)
        init_permissions()
    }, [])

    useEffect(() => {
        init_permissions()
    }, [permissions])

    useEffect(() => {
        if (logEntrySections && Array.isArray(logEntrySections)) {
            const hasCrewWelfare = logEntrySections.filter(
                (section: any) =>
                    section &&
                    section.className ===
                        'SeaLogs\\CrewWelfare_LogBookEntrySection',
            ).length
            if (
                hasCrewWelfare === 0 &&
                !crewWelfareCheck &&
                !loaded &&
                !createCrewWelfareCheckLoading
            ) {
                setLoaded(true)
                if (offline) {
                    createOfflineCrewWelfareCheck()
                } else {
                    createCrewWelfareCheck({
                        variables: {
                            input: {
                                logBookEntryID: +logBookEntryID,
                            },
                        },
                    })
                }
            }
        }
    }, [logEntrySections])

    const [queryCrewDetail] = useLazyQuery(CREW_DETAIL_BY_ID, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {},
        onError: (error: any) => {
            console.error('GetCrewDetailError', error)
        },
    })

    const [queryVesselCrews] = useLazyQuery(CREW_LIST, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const data = response.readSeaLogsMembers
            if (data) {
                const allMembers = data.nodes
                    .filter((item: any) => {
                        return +item.id !== +masterID
                    })
                    .map((member: any) => {
                        // const crewWithTraining = GetCrewListWithTrainingStatus(
                        //     [member],
                        //     [vessel],
                        // )[0]
                        return {
                            label: `${member.firstName || ''} ${member.surname || ''}`.trim(),
                            value: member.id,
                            // data: crewWithTraining,
                            profile: {
                                firstName: member.firstName,
                                surname: member.surname,
                                avatar: member.profileImage,
                            },
                        }
                    })

                setAllMembers(allMembers)

                const members = allMembers.filter((member: any) => {
                    if (!crewSections) {
                        return true
                    }
                    return (
                        !Array.isArray(crewSections) ||
                        !crewSections.some(
                            (section: any) =>
                                section &&
                                section.crewMember &&
                                section.crewMember.id === member.value &&
                                section.punchOut === null,
                        )
                    )
                })

                const memberOptions = members.filter(
                    (member: any) =>
                        !crewMembersList ||
                        !Array.isArray(crewMembersList) ||
                        !crewMembersList.includes(+member.value),
                )

                setCrewMemberOptions(memberOptions)
            }
        },
        onError: (error: any) => {
            console.error('queryVesselCrews error', error)
        },
    })

    const loadVesselCrews = async () => {
        if (offline) {
            const data = await seaLogsMemberModel.getByVesselId(vesselID)
            setAllVesselCrews(data)
            if (data) {
                const members = data
                    .filter((item: any) => {
                        return +item.id !== +masterID
                    })
                    .map((member: any) => {
                        const crewWithTraining = GetCrewListWithTrainingStatus(
                            [member],
                            [vessel],
                        )[0]
                        return {
                            label: `${member.firstName || ''} ${member.surname || ''}`.trim(),
                            value: member.id,
                            data: crewWithTraining,
                            profile: {
                                firstName: member.firstName,
                                surname: member.surname,
                                avatar: member.profileImage,
                            },
                        }
                    }) // filter out members who are already in the crew list
                    .filter((member: any) => {
                        if (!crewSections) {
                            return true
                        }
                        return (
                            !Array.isArray(crewSections) ||
                            !crewSections.some(
                                (section: any) =>
                                    section &&
                                    section.crewMember &&
                                    section.crewMember.id === member.value &&
                                    section.punchOut === null,
                            )
                        )
                    })
                setCrewMemberOptions(members)
            }
        } else {
            await queryVesselCrews({
                variables: {
                    filter: {
                        vehicles: { id: { eq: vesselID } },
                        isArchived: { eq: false },
                    },
                },
            })
        }
    }

    useEffect(() => {
        if (isLoading) {
            loadDuties()
            // handleSetCrewConfig()
            loadVesselCrews()
            setIsLoading(false)
        }
    }, [isLoading])

    // Group crew duties by crew member
    const groupCrewDutiesByMember = (crewData: any[]) => {
        if (!crewData || !Array.isArray(crewData) || crewData.length === 0)
            return []

        const groupedCrew: Record<string, any> = {}

        // First, sort the crew data by punchIn time to ensure consistent ordering
        const sortedCrewData = [...crewData].sort((a, b) => {
            if (!a || !b) return 0
            const timeA = a.punchIn ? new Date(a.punchIn).getTime() : 0
            const timeB = b.punchIn ? new Date(b.punchIn).getTime() : 0
            return timeA - timeB // Ascending order (oldest first)
        })

        // Filter out archived members first
        const activeCrewData = sortedCrewData.filter(
            (member) => member && !member.archived,
        )

        activeCrewData.forEach((member) => {
            if (!member) return

            const crewMemberId = member.crewMemberID
            if (!crewMemberId) return

            // If this member already has duties array, preserve it
            if (
                member.duties &&
                Array.isArray(member.duties) &&
                member.duties.length > 0
            ) {
                if (!groupedCrew[crewMemberId]) {
                    // Initialize with the existing duties
                    groupedCrew[crewMemberId] = { ...member }
                } else {
                    // Merge duties from this member with existing duties
                    const existingDuties =
                        groupedCrew[crewMemberId].duties || []

                    member.duties.forEach((duty: any) => {
                        if (!duty) return
                        // Check if this duty is already in the list (avoid duplicates by ID)
                        const isDuplicateById = existingDuties.some(
                            (existingDuty: any) =>
                                existingDuty && existingDuty.id === duty.id,
                        )

                        // Only add if it's not a duplicate
                        if (!isDuplicateById) {
                            existingDuties.push(duty)
                        }
                    })

                    groupedCrew[crewMemberId] = {
                        ...groupedCrew[crewMemberId],
                        duties: existingDuties,
                    }
                }
            } else {
                // Handle members without a duties array
                if (!groupedCrew[crewMemberId]) {
                    // Initialize with the first duty
                    groupedCrew[crewMemberId] = {
                        ...member,
                        duties: [
                            {
                                id: member.id,
                                dutyPerformed: member.dutyPerformed,
                                punchIn: member.punchIn,
                                punchOut: member.punchOut,
                                workDetails: member.workDetails,
                                dutyPerformedID: member.dutyPerformedID,
                                logBookEntryID: member.logBookEntryID,
                            },
                        ],
                    }
                } else if (
                    groupedCrew[crewMemberId].duties &&
                    Array.isArray(groupedCrew[crewMemberId].duties)
                ) {
                    // Check if this duty is already in the list (avoid duplicates by ID)
                    const isDuplicateById = groupedCrew[
                        crewMemberId
                    ].duties.some(
                        (existingDuty: any) =>
                            existingDuty && existingDuty.id === member.id,
                    )

                    // Also check if this is a duplicate duty type with the same time (which would be redundant)
                    const isDuplicateDutyType = groupedCrew[
                        crewMemberId
                    ].duties.some(
                        (existingDuty: any) =>
                            existingDuty &&
                            existingDuty.dutyPerformedID ===
                                member.dutyPerformedID &&
                            existingDuty.punchIn === member.punchIn,
                    )

                    // Only add if it's not a duplicate by ID or duty type
                    if (!isDuplicateById && !isDuplicateDutyType) {
                        groupedCrew[crewMemberId].duties.push({
                            id: member.id,
                            dutyPerformed: member.dutyPerformed,
                            punchIn: member.punchIn,
                            punchOut: member.punchOut,
                            workDetails: member.workDetails,
                            dutyPerformedID: member.dutyPerformedID,
                            logBookEntryID: member.logBookEntryID,
                        })
                    }
                }
            }
        })

        // Sort duties by punchIn time in ascending order for each crew member
        Object.values(groupedCrew).forEach((crewMember: any) => {
            if (
                crewMember &&
                crewMember.duties &&
                Array.isArray(crewMember.duties) &&
                crewMember.duties.length > 1
            ) {
                crewMember.duties.sort((a: any, b: any) => {
                    if (!a || !b) return 0
                    const timeA = a.punchIn ? new Date(a.punchIn).getTime() : 0
                    const timeB = b.punchIn ? new Date(b.punchIn).getTime() : 0
                    return timeA - timeB // Ascending order (oldest first)
                })
            }
        })

        return Object.values(groupedCrew)
    }

    useEffect(() => {
        if (crewSections && Array.isArray(crewSections)) {
            // Process each crew member's training status
            const processedCrewSections = crewSections.map((section: any) => {
                if (section && section.crewMember) {
                    // Apply GetCrewListWithTrainingStatus to the crewMember property
                    const crewMemberWithTrainingStatus =
                        GetCrewListWithTrainingStatus(
                            [section.crewMember],
                            [vessel],
                        )[0]

                    return {
                        ...section,
                        crewMember: crewMemberWithTrainingStatus,
                    }
                }
                return section
            })

            // Preserve existing duties if they exist
            let updatedData = processedCrewSections
            if (crew && Array.isArray(crew) && crew.length > 0) {
                // Create a map of existing crew members with their duties
                const existingCrewMap = crew.reduce(
                    (map: Record<string, any>, member: any) => {
                        if (member && member.crewMemberID) {
                            map[member.crewMemberID] = member
                        }
                        return map
                    },
                    {},
                )

                // Check if any existing crew members have duties that need to be preserved
                const hasExistingDuties = Object.values(existingCrewMap).some(
                    (member: any) =>
                        member &&
                        member.duties &&
                        Array.isArray(member.duties) &&
                        member.duties.length > 0,
                )

                if (hasExistingDuties) {
                    // Update processed data with existing duties where applicable
                    updatedData = processedCrewSections.map((section: any) => {
                        if (!section || !section.crewMemberID) return section

                        const existingMember =
                            existingCrewMap[section.crewMemberID]
                        if (
                            existingMember &&
                            existingMember.duties &&
                            Array.isArray(existingMember.duties) &&
                            existingMember.duties.length > 0
                        ) {
                            // Check if this section's ID is already in the existing duties
                            const dutyExists = existingMember.duties.some(
                                (duty: any) => duty && duty.id === section.id,
                            )

                            if (dutyExists) {
                                // This section is already in the duties, so return the section with duties
                                return {
                                    ...section,
                                    duties: existingMember.duties,
                                }
                            } else {
                                // This is a new duty for this crew member, add it to their duties
                                const updatedDuties = [...existingMember.duties]
                                updatedDuties.push({
                                    id: section.id,
                                    dutyPerformed: section.dutyPerformed,
                                    punchIn: section.punchIn,
                                    punchOut: section.punchOut,
                                    workDetails: section.workDetails,
                                    dutyPerformedID: section.dutyPerformedID,
                                    logBookEntryID: section.logBookEntryID,
                                })

                                return {
                                    ...section,
                                    duties: updatedDuties,
                                }
                            }
                        }

                        // No existing duties for this crew member, create a new duties array
                        return {
                            ...section,
                            duties: [
                                {
                                    id: section.id,
                                    dutyPerformed: section.dutyPerformed,
                                    punchIn: section.punchIn,
                                    punchOut: section.punchOut,
                                    workDetails: section.workDetails,
                                    dutyPerformedID: section.dutyPerformedID,
                                    logBookEntryID: section.logBookEntryID,
                                },
                            ],
                        }
                    })
                }
            }

            // Group crew duties by crew member
            const groupedCrewSections = groupCrewDutiesByMember(updatedData)
            setCrew(groupedCrewSections)
        }
    }, [crewSections, vessel])

    useEffect(() => {
        if (masterID > 0) {
            loadVesselCrews()
        }
    }, [masterID])

    const loadDuties = async () => {
        if (offline) {
            const data = await crewDutyModel.getAll()
            setAllDuties(data)
            if (data) {
                const activeDuties = data.filter((duty: any) => !duty.archived)
                setDuties(activeDuties)
            }
        } else {
            await queryDuties()
        }
    }

    const handleSetCrewConfig = () => {
        if (
            logBookConfig &&
            logBookConfig.customisedLogBookComponents &&
            logBookConfig.customisedLogBookComponents.nodes &&
            Array.isArray(logBookConfig.customisedLogBookComponents.nodes)
        ) {
            const crewMembersConfigs =
                logBookConfig.customisedLogBookComponents.nodes.filter(
                    (config: any) => config && config.title === 'Crew Members',
                )

            const length = crewMembersConfigs.length

            if (length === 1) {
                const config = crewMembersConfigs[0]
                if (
                    config &&
                    config.customisedComponentFields &&
                    config.customisedComponentFields.nodes &&
                    Array.isArray(config.customisedComponentFields.nodes)
                ) {
                    setCrewConfig(
                        config.customisedComponentFields.nodes.map(
                            (field: any) => ({
                                title: field.fieldName,
                                status: field.status,
                            }),
                        ),
                    )
                }
            } else if (length > 1) {
                const sortedConfigs = [...crewMembersConfigs].sort(
                    (a: any, b: any) => parseInt(b.id) - parseInt(a.id),
                )

                const config = sortedConfigs[0]
                if (
                    config &&
                    config.customisedComponentFields &&
                    config.customisedComponentFields.nodes &&
                    Array.isArray(config.customisedComponentFields.nodes)
                ) {
                    setCrewConfig(
                        config.customisedComponentFields.nodes.map(
                            (field: any) => ({
                                title: field.fieldName,
                                status: field.status,
                            }),
                        ),
                    )
                }
            }
        } else {
            setCrewConfig(false)
        }
    }

    const handleSetStatus = () => {
        if (
            Array.isArray(crewConfig) &&
            crewConfig.length > 0 &&
            logBookConfig &&
            logBookConfig.customisedLogBookComponents &&
            logBookConfig.customisedLogBookComponents.nodes &&
            Array.isArray(logBookConfig.customisedLogBookComponents.nodes)
        ) {
            const crewMemberComponents =
                logBookConfig.customisedLogBookComponents.nodes.filter(
                    (config: any) => config && config.title === 'Crew Members',
                )

            const crewMemberComponent =
                crewMemberComponents.length > 0 ? crewMemberComponents[0] : null

            if (
                crewMemberComponent &&
                crewMemberComponent.customisedComponentFields &&
                crewMemberComponent.customisedComponentFields.nodes &&
                Array.isArray(
                    crewMemberComponent.customisedComponentFields.nodes,
                )
            ) {
                // Crew Member
                let title = 'CrewMemberID'
                const crewMemberField =
                    crewMemberComponent.customisedComponentFields.nodes.find(
                        (item: any) => item && item.fieldName === title,
                    )

                // We already have a default value set in the useState, so we only need to update if we have a valid value
                if (crewMemberField) {
                    // We don't need to set crew member label anymore as it's not used
                    // Keeping the code structure for future reference
                }

                // Primary Duty
                title = 'DutyPerformedID'
                const primaryDutyField =
                    crewMemberComponent.customisedComponentFields.nodes.find(
                        (item: any) => item && item.fieldName === title,
                    )

                // We already have a default value set in the useState, so we only need to update if we have a valid value
                if (primaryDutyField) {
                    // We don't need to set primary duty label anymore as it's not used
                    // Keeping the code structure for future reference
                }

                // Punch in
                title = 'PunchIn'
                const punchInConfig = crewConfig.find(
                    (config: any) => config && config.title === title,
                )
                setPunchInStatus(punchInConfig?.status || 'On')

                const punchInField =
                    crewMemberComponent.customisedComponentFields.nodes.find(
                        (item: any) => item && item.fieldName === title,
                    )

                // We already have a default value set in the useState, so we only need to update if we have a valid value
                if (punchInField) {
                    const customTitle = punchInField.customisedFieldTitle
                    const fieldNameValue = getFieldName(
                        punchInField,
                        SLALL_LogBookFields,
                    )

                    // Only update if we have a valid value
                    if (customTitle && customTitle.trim() !== '') {
                        setPunchInLabel(customTitle)
                    } else if (fieldNameValue && fieldNameValue.trim() !== '') {
                        setPunchInLabel(fieldNameValue)
                    }
                    // Otherwise keep the default 'Sign In'
                }

                // Punch out
                title = 'PunchOut'
                const punchOutConfig = crewConfig.find(
                    (config: any) => config && config.title === title,
                )
                setPunchOutStatus(punchOutConfig?.status || 'On')

                const punchOutField =
                    crewMemberComponent.customisedComponentFields.nodes.find(
                        (item: any) => item && item.fieldName === title,
                    )

                // We already have a default value set in the useState, so we only need to update if we have a valid value
                if (punchOutField) {
                    const customTitle = punchOutField.customisedFieldTitle
                    const fieldNameValue = getFieldName(
                        punchOutField,
                        SLALL_LogBookFields,
                    )

                    // Only update if we have a valid value
                    if (customTitle && customTitle.trim() !== '') {
                        setPunchOutLabel(customTitle)
                    } else if (fieldNameValue && fieldNameValue.trim() !== '') {
                        setPunchOutLabel(fieldNameValue)
                    }
                    // Otherwise keep the default 'Sign Out'
                }

                // Work details
                title = 'WorkDetails'
                const workDetailsConfig = crewConfig.find(
                    (config: any) => config && config.title === title,
                )
                setWorkDetailsStatus(workDetailsConfig?.status || 'On')

                const workDetailsField =
                    crewMemberComponent.customisedComponentFields.nodes.find(
                        (item: any) => item && item.fieldName === title,
                    )

                // We already have a default value set in the useState, so we only need to update if we have a valid value
                if (workDetailsField) {
                    const customTitle = workDetailsField.customisedFieldTitle
                    const fieldNameValue = getFieldName(
                        workDetailsField,
                        SLALL_LogBookFields,
                    )

                    // Only update if we have a valid value
                    if (customTitle && customTitle.trim() !== '') {
                        setWorkDetailsLabel(customTitle)
                    } else if (fieldNameValue && fieldNameValue.trim() !== '') {
                        setWorkDetailsLabel(fieldNameValue)
                    }
                    // Otherwise keep the default 'Work Details'
                }
            }
        } else {
            // Set default values if crewConfig is not valid
            setPunchInStatus('On')
            setPunchInLabel('Sign In')
            setPunchOutStatus('On')
            setPunchOutLabel('Sign Out')
            setWorkDetailsStatus('On')
            setWorkDetailsLabel('Work Details')
        }
    }

    const [queryDuties] = useLazyQuery(CREW_DUTY, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readCrewDuties.nodes
            if (data) {
                const activeDuties = data.filter((duty: any) => !duty.archived)
                setDuties(activeDuties)
            }
        },
        onError: (error: any) => {
            console.error('queryDutiesEntry error', error)
        },
    })

    const handleLogin = (date: any) => {
        if (!date) {
            // Handle the case when date is null or undefined (unselected)
            const currentTime = new Date()
            setLoginTime(currentTime)
            setCrewManifestEntry({
                ...crewManifestEntry,
                punchIn: formatDBDateTime(currentTime),
            })
            return
        }

        try {
            // Ensure we have a valid date by creating a new Date object
            const validDate = new Date(date)

            // Check if the date is valid
            if (isNaN(validDate.getTime())) {
                console.error('Invalid date provided to handleLogin:', date)
                return
            }

            setLoginTime(validDate)
            setCrewManifestEntry({
                ...crewManifestEntry,
                punchIn: formatDBDateTime(validDate),
            })

            // If logout time is set and is before the new login time, reset it
            if (logoutTime && validDate.getTime() > logoutTime.getTime()) {
                setLogoutTime(null)
                setCrewManifestEntry((prev: any) => ({
                    ...prev,
                    punchOut: null,
                }))
            }
        } catch (error) {
            console.error('Error in handleLogin:', error)
            toast.error('An error occurred while setting the sign in time')
        }
    }

    const handleLogout = (date: any) => {
        if (!date) {
            // Handle the case when date is null or undefined (unselected)
            setLogoutTime(null)
            setCrewManifestEntry({
                ...crewManifestEntry,
                punchOut: null,
            })
            return
        }

        try {
            // Ensure we have a valid date by creating a new Date object
            const validDate = new Date(date)

            // Check if the date is valid
            if (isNaN(validDate.getTime())) {
                console.error('Invalid date provided to handleLogout:', date)
                return
            }

            // If the date doesn't have time set (hours and minutes are 0),
            // set the current time
            if (validDate.getHours() === 0 && validDate.getMinutes() === 0) {
                const now = new Date()
                validDate.setHours(now.getHours())
                validDate.setMinutes(now.getMinutes())
            }

            // Convert to dayjs for easier comparison
            const dayjsDate = dayjs(validDate)

            // Ensure logout time is after login time
            if (loginTime && dayjsDate.isBefore(loginTime)) {
                toast.error('Sign out time must be after sign in time')
                return
            }

            // Store the date as a standard Date object to avoid any issues with dayjs
            setLogoutTime(validDate)

            // Update crew manifest entry with formatted date string
            setCrewManifestEntry({
                ...crewManifestEntry,
                punchOut: formatDBDateTime(validDate),
            })
        } catch (error) {
            console.error('Error in handleLogout:', error)
            toast.error('An error occurred while setting the sign out time')
        }
    }

    const handleAddManifest = () => {
        // Check permissions first
        if (!edit_logBookEntry) {
            toast.error('You do not have permission to edit this log entry')
            return
        }

        // Filter crew member options to only show available crew members
        if (allMembers && Array.isArray(allMembers)) {
            // Get a list of crew members who are already signed in (without sign-out time)
            const signedInCrewMemberIDs = new Set()

            if (crew && Array.isArray(crew)) {
                crew.forEach((member: any) => {
                    // Only consider members who are signed in without a sign-out time
                    if (
                        member &&
                        member.duties &&
                        Array.isArray(member.duties)
                    ) {
                        // Check if any duty has no punch out time
                        const hasActiveShift = member.duties.some(
                            (duty: any) => duty && duty.punchOut === null,
                        )
                        if (hasActiveShift) {
                            signedInCrewMemberIDs.add(member.crewMemberID)
                        }
                    } else if (member && member.punchOut === null) {
                        signedInCrewMemberIDs.add(member.crewMemberID)
                    }
                })
            }

            // Filter out crew members who are already signed in
            const availableCrewMembers = allMembers.filter((member: any) => {
                if (!member) return false
                return !signedInCrewMemberIDs.has(member.value)
            })

            // Further filter out crew members who are in the crewMembersList (if applicable)
            const filteredCrewOptions = availableCrewMembers.filter(
                (member: any) =>
                    !member ||
                    !crewMembersList ||
                    !Array.isArray(crewMembersList) ||
                    !crewMembersList.includes(+member.value),
            )

            setCrewMemberOptions(filteredCrewOptions)
        } else {
            // If allMembers is not valid, just proceed with empty options
            setCrewMemberOptions([])
        }

        // Set up the new crew manifest entry with current time
        const currentTime = new Date()
        const crewManifestEntry: any = {
            id: 0,
            logBookEntryID: +logBookEntryID,
            crewMemberID: 0,
            dutyPerformedID: 0,
            punchIn: formatDBDateTime(currentTime),
            punchOut: null,
        }

        setCrewManifestEntry(crewManifestEntry)
        setLoginTime(currentTime)
        setLogoutTime(null)
        setCrewMember(null)
        setDuty(null)
        setopenAddCrewMemberDialog(true)
    }

    const handleEditManifest = (memberData: any) => {
        if (!edit_logBookEntry) {
            toast.error('You do not have permission to edit this log entry')
            return
        }

        // If this is a grouped crew member with multiple duties, use the first duty
        const dutyToEdit =
            memberData.duties && memberData.duties.length > 0
                ? memberData.duties[0]
                : memberData

        setCrewManifestEntry({
            id: dutyToEdit?.id,
            logBookEntryID: dutyToEdit?.logBookEntryID,
            crewMemberID: memberData?.crewMemberID,
            dutyPerformedID: dutyToEdit?.dutyPerformedID,
            punchIn: dutyToEdit?.punchIn,
            punchOut: dutyToEdit?.punchOut,
            workDetails: dutyToEdit?.workDetails,
        })

        // Create a proper crew member object with profile details
        const crewMemberWithProfile = {
            label: `${memberData.crewMember.firstName || ''} ${memberData.crewMember.surname !== null ? memberData.crewMember.surname : ''}`.trim(),
            value: memberData.crewMember.id,
            data: memberData.crewMember,
            profile: {
                firstName: memberData.crewMember.firstName,
                surname: memberData.crewMember.surname,
                avatar: memberData.crewMember.profileImage,
            },
        }

        setCrewMember(crewMemberWithProfile)

        // Find the correct duty in the duties array
        const selectedDuty = duties.find(
            (memberDuty: any) => memberDuty.id === dutyToEdit?.dutyPerformedID,
        )

        if (selectedDuty) {
            setDuty({
                label: selectedDuty.title,
                value: selectedDuty.id,
            })
        }

        setLoginTime(
            dutyToEdit?.punchIn ? new Date(dutyToEdit.punchIn) : new Date(),
        )
        setLogoutTime(
            dutyToEdit?.punchOut ? new Date(dutyToEdit.punchOut) : null,
        )
        setopenAddCrewMemberDialog(true)
    }

    const handleSignOutTime = (memberData: any) => {
        if (!edit_logBookEntry) {
            toast.error('You do not have permission to edit this log entry')
            return
        }

        // Determine if this is a nested duty or a main crew member
        const isNestedDuty = !memberData.crewMember

        if (isNestedDuty) {
            // This is a nested duty
            // Find the parent crew member for this duty
            const parentMember =
                crew && Array.isArray(crew)
                    ? crew.find((c: any) => {
                          return (
                              c &&
                              c.duties &&
                              Array.isArray(c.duties) &&
                              c.duties.some(
                                  (d: any) => d && d.id === memberData.id,
                              )
                          )
                      })
                    : null

            if (parentMember) {
                // Set crew manifest entry with the parent crew member ID
                setCrewManifestEntry({
                    id: memberData?.id,
                    logBookEntryID: memberData?.logBookEntryID,
                    crewMemberID: parentMember.crewMemberID, // Add the parent crew member ID
                    dutyPerformedID: memberData?.dutyPerformed?.id,
                    punchIn: memberData?.punchIn,
                    punchOut: memberData?.punchOut,
                    workDetails: memberData?.workDetails,
                })

                // Create a proper crew member object with profile details
                const crewMemberWithProfile = {
                    label: `${parentMember.crewMember.firstName || ''} ${parentMember.crewMember.surname !== null ? parentMember.crewMember.surname : ''}`.trim(),
                    value: parentMember.crewMember.id,
                    data: parentMember.crewMember,
                    profile: {
                        firstName: parentMember.crewMember.firstName,
                        surname: parentMember.crewMember.surname,
                        avatar: parentMember.crewMember.profileImage,
                    },
                }

                setCrewMember(crewMemberWithProfile)

                // Set duty
                if (memberData.dutyPerformed) {
                    const selectedDuty = duties.find(
                        (memberDuty: any) =>
                            memberDuty.id === memberData?.dutyPerformed?.id,
                    )

                    if (selectedDuty) {
                        setDuty({
                            label: selectedDuty.title,
                            value: selectedDuty.id,
                        })
                    }
                }
            } else {
                // If parent member not found, show an error
                toast.error('Could not find the associated crew member')
                return
            }
        } else {
            // This is a main crew member
            setCrewManifestEntry({
                id: memberData?.id,
                logBookEntryID: memberData?.logBookEntryID,
                crewMemberID: memberData?.crewMemberID,
                dutyPerformedID: memberData?.dutyPerformed?.id,
                punchIn: memberData?.punchIn,
                punchOut: memberData?.punchOut,
                workDetails: memberData?.workDetails,
            })

            // Create a proper crew member object with profile details
            const crewMemberWithProfile = {
                label: `${memberData.crewMember.firstName || ''} ${memberData.crewMember.surname !== null ? memberData.crewMember.surname : ''}`.trim(),
                value: memberData.crewMember.id,
                data: memberData.crewMember,
                profile: {
                    firstName: memberData.crewMember.firstName,
                    surname: memberData.crewMember.surname,
                    avatar: memberData.crewMember.profileImage,
                },
            }

            setCrewMember(crewMemberWithProfile)

            // Set duty
            if (memberData.dutyPerformed) {
                const selectedDuty = duties.find(
                    (memberDuty: any) =>
                        memberDuty.id === memberData?.dutyPerformed?.id,
                )

                if (selectedDuty) {
                    setDuty({
                        label: selectedDuty.title,
                        value: selectedDuty.id,
                    })
                }
            }
        }

        // Set times
        setLoginTime(
            memberData.punchIn ? new Date(memberData.punchIn) : new Date(),
        )
        setLogoutTime(
            memberData.punchOut ? new Date(memberData.punchOut) : null,
        )

        setOpenEditLogoutTimeDialog(true)
    }

    const handleCrewMember = async (selected: any) => {
        if (!selected) return
        setDuty({
            label: '-- Select Duty --',
            value: 0,
        })

        const { data } = await queryCrewDetail({
            variables: {
                crewMemberID: selected.value,
            },
        })

        const member = data.readOneSeaLogsMember
        const crewWithTraining = GetCrewListWithTrainingStatus(
            [member],
            [vessel],
        )[0]

        const value = {
            ...selected,
            data: crewWithTraining,
        }

        setCrewMember(value)

        // Check if the crew has a training due
        if (
            value.data &&
            value.data.trainingStatus &&
            value.data.trainingStatus.label !== 'Good'
        ) {
            setOpenCrewTrainingDueDialog(true)
        }

        // Set default duty
        if (allCrew && Array.isArray(allCrew)) {
            const crewMember = allCrew.find(
                (member: any) => member && member.id === value.value,
            )

            if (crewMember && crewMember.primaryDutyID) {
                if (duties && Array.isArray(duties)) {
                    const crewDuty = duties.find(
                        (d: any) => d && d.id === crewMember.primaryDutyID,
                    )
                    if (crewDuty) {
                        const newDuty = {
                            label: crewDuty.title,
                            value: crewDuty.id,
                        }
                        setDuty(newDuty)
                        setCrewManifestEntry({
                            ...crewManifestEntry,
                            crewMemberID: crewMember.id,
                            dutyPerformedID: crewDuty.id,
                        })
                    } else {
                        setCrewManifestEntry({
                            ...crewManifestEntry,
                            crewMemberID: crewMember.id,
                        })
                    }
                } else {
                    setCrewManifestEntry({
                        ...crewManifestEntry,
                        crewMemberID: crewMember.id,
                    })
                }
            } else if (crewMember) {
                setCrewManifestEntry({
                    ...crewManifestEntry,
                    crewMemberID: crewMember.id,
                })
            }
        }
    }

    const handleDuty = (value: any) => {
        setDuty(value)
        setCrewManifestEntry({
            ...crewManifestEntry,
            dutyPerformedID: value?.value || 0,
        })
    }

    const handleCancel = () => {
        setCrewManifestEntry({} as LogBookEntryCrewSection)
        setCrewMember(null)
        setDuty(null)
        setLoginTime(new Date())
        setLogoutTime(null)
        setopenAddCrewMemberDialog(false)
        setOpenEditLogoutTimeDialog(false)
    }

    const handleSave = async (callBy?: string) => {
        // Validate required fields
        if (
            !crewManifestEntry.crewMemberID ||
            crewManifestEntry.crewMemberID === 0
        ) {
            toast.error('Please select a crew member')
            return
        }

        if (
            !crewManifestEntry.dutyPerformedID ||
            crewManifestEntry.dutyPerformedID === 0
        ) {
            toast.error('Please select a duty')
            return
        }

        // Get work details from the textarea
        const workDetailsElement = document.getElementById(
            'work-details',
        ) as HTMLInputElement
        const workDetails = workDetailsElement?.value || ''

        const variables = {
            id: crewManifestEntry.id,
            crewMemberID: crewManifestEntry.crewMemberID,
            dutyPerformedID: +crewManifestEntry?.dutyPerformedID,
            logBookEntryID: +logBookEntryID,
            punchIn: loginTime ? formatDBDateTime(loginTime) : null,
            punchOut: logoutTime ? formatDBDateTime(logoutTime) : null,
            workDetails: workDetails,
        }
        try {
            // Case 1: Updating an existing crew entry
            if (crewManifestEntry.id > 0) {
                if (offline) {
                    // Save the updated crew member to the database
                    await lbCrewModel.save(variables)

                    // Get all crew IDs to fetch updated data
                    const crewIds =
                        crew && Array.isArray(crew)
                            ? crew.map((c: any) => c.id).filter(Boolean)
                            : []

                    // Reset the form
                    setCrewManifestEntry({})

                    // Fetch the updated crew data
                    let crewData = await lbCrewModel.getByIds(crewIds)

                    if (crewData) {
                        // Process crew members with training status
                        const processedData = crewData.map((section: any) => {
                            if (section && section.crewMember) {
                                const crewMemberWithTrainingStatus =
                                    GetCrewListWithTrainingStatus(
                                        [section.crewMember],
                                        [vessel],
                                    )[0]
                                return {
                                    ...section,
                                    crewMember: crewMemberWithTrainingStatus,
                                }
                            }
                            return section
                        })

                        // Group crew duties by crew member
                        const groupedData =
                            groupCrewDutiesByMember(processedData)

                        // Update state
                        setCrew(groupedData)
                        setCrewMembers(groupedData)
                    }
                } else {
                    // Online mode - use GraphQL mutation
                    updateCrewMembers_LogBookEntrySection({
                        variables: { input: variables },
                    })
                }

                // Close dialogs
                setopenAddCrewMemberDialog(false)
                if (callBy === 'update') {
                    setOpenEditLogoutTimeDialog(false)
                }
            } else if (crewManifestEntry.crewMemberID > 0) {
                if (offline) {
                    // Generate a unique ID for the new entry
                    const uniqueId = generateUniqueId()
                    const data = {
                        ...variables,
                        id: uniqueId,
                    }

                    // Save the new crew member to the database
                    await lbCrewModel.save(data)

                    // Get the selected crew member and duty information
                    const selectedMember = allVesselCrews.find(
                        (c: any) => c.id === crewManifestEntry.crewMemberID,
                    )
                    const selectedDuty = allDuties.find(
                        (d: any) => d.id === crewManifestEntry.dutyPerformedID,
                    )

                    if (!selectedMember || !selectedDuty) {
                        toast.error(
                            'Could not find crew member or duty information',
                        )
                        return
                    }

                    // Create a new crew entry with the necessary data for immediate display
                    const newCrewEntry = {
                        ...data,
                        crewMember: GetCrewListWithTrainingStatus(
                            [selectedMember],
                            [vessel],
                        )[0],
                        dutyPerformed: selectedDuty,
                    }

                    // Get existing crew data or initialize empty array
                    const existingCrew = Array.isArray(crew) ? [...crew] : []

                    // Check if this crew member already exists in the list
                    const existingCrewMemberIndex = existingCrew.findIndex(
                        (c: any) => c && c.crewMemberID === data.crewMemberID,
                    )

                    let updatedCrewData = [...existingCrew]

                    if (existingCrewMemberIndex !== -1) {
                        // If the crew member already exists, add this duty to their duties array
                        const existingCrewMember = {
                            ...updatedCrewData[existingCrewMemberIndex],
                        }

                        if (
                            existingCrewMember.duties &&
                            Array.isArray(existingCrewMember.duties)
                        ) {
                            // Add the new duty to the existing duties array
                            existingCrewMember.duties.push({
                                id: data.id,
                                dutyPerformed: selectedDuty,
                                punchIn: data.punchIn,
                                punchOut: data.punchOut,
                                workDetails: data.workDetails,
                                dutyPerformedID: data.dutyPerformedID,
                                logBookEntryID: data.logBookEntryID,
                            })
                        } else {
                            // Create a duties array if it doesn't exist
                            existingCrewMember.duties = [
                                {
                                    id: data.id,
                                    dutyPerformed: selectedDuty,
                                    punchIn: data.punchIn,
                                    punchOut: data.punchOut,
                                    workDetails: data.workDetails,
                                    dutyPerformedID: data.dutyPerformedID,
                                    logBookEntryID: data.logBookEntryID,
                                },
                            ]
                        }

                        // Update the crew member in the array
                        updatedCrewData[existingCrewMemberIndex] =
                            existingCrewMember
                    } else {
                        // If this is a new crew member, add them to the list with their first duty
                        updatedCrewData.push({
                            ...newCrewEntry,
                            duties: [
                                {
                                    id: data.id,
                                    dutyPerformed: selectedDuty,
                                    punchIn: data.punchIn,
                                    punchOut: data.punchOut,
                                    workDetails: data.workDetails,
                                    dutyPerformedID: data.dutyPerformedID,
                                    logBookEntryID: data.logBookEntryID,
                                },
                            ],
                        })
                    }

                    // Group the updated crew data
                    const groupedData = groupCrewDutiesByMember(updatedCrewData)

                    // Update state with the new grouped data
                    setCrew(groupedData)
                    setCrewMembers(groupedData)
                    setCrewManifestEntry({})

                    // Also fetch the latest data from the database to ensure consistency
                    const crewIds = updatedCrewData
                        .map((c: any) => c && c.id)
                        .filter(Boolean)
                        .concat([uniqueId])

                    let crewData = await lbCrewModel.getByIds(crewIds)
                    if (crewData) {
                        // Process crew members with training status
                        const processedDbData = crewData.map((section: any) => {
                            if (section && section.crewMember) {
                                const crewMemberWithTrainingStatus =
                                    GetCrewListWithTrainingStatus(
                                        [section.crewMember],
                                        [vessel],
                                    )[0]
                                return {
                                    ...section,
                                    crewMember: crewMemberWithTrainingStatus,
                                }
                            }
                            return section
                        })

                        // Group crew duties by crew member
                        const groupedDbData =
                            groupCrewDutiesByMember(processedDbData)

                        // Update with the database data to ensure consistency
                        setCrew(groupedDbData)
                        setCrewMembers(groupedDbData)
                    }
                } else {
                    // Online mode - use GraphQL mutation
                    createCrewMembers_LogBookEntrySection({
                        variables: { input: variables },
                    })
                }

                // Close dialog
                setopenAddCrewMemberDialog(false)
            } else {
                // No valid crew member selected, just cancel
                handleCancel()
            }
        } catch (error) {
            console.error('Error saving crew member:', error)
            toast.error('Failed to save crew member. Please try again.')
        }
    }

    const [updateCrewMembers_LogBookEntrySection] = useMutation(
        UpdateCrewMembers_LogBookEntrySection,
        {
            onCompleted: () => {
                // First, update the UI immediately with the updated data
                if (crewManifestEntry.id > 0 && crew && Array.isArray(crew)) {
                    // Create a deep copy of the current crew data
                    let updatedCrewData = JSON.parse(JSON.stringify(crew))

                    // Find the crew member and duty that was updated
                    let foundAndUpdated = false

                    // Loop through all crew members
                    for (let i = 0; i < updatedCrewData.length; i++) {
                        const member = updatedCrewData[i]

                        // Check if this is the main duty that was updated
                        if (member.id === crewManifestEntry.id) {
                            // Update the main duty
                            updatedCrewData[i] = {
                                ...member,
                                punchOut: crewManifestEntry.punchOut,
                            }
                            foundAndUpdated = true
                            break
                        }

                        // Check if this is a nested duty that was updated
                        if (member.duties && Array.isArray(member.duties)) {
                            for (let j = 0; j < member.duties.length; j++) {
                                const duty = member.duties[j]
                                if (duty.id === crewManifestEntry.id) {
                                    // Update the nested duty
                                    member.duties[j] = {
                                        ...duty,
                                        punchOut: crewManifestEntry.punchOut,
                                    }
                                    foundAndUpdated = true
                                    break
                                }
                            }

                            if (foundAndUpdated) {
                                break
                            }
                        }
                    }

                    if (foundAndUpdated) {
                        // Group the updated crew data
                        const groupedData =
                            groupCrewDutiesByMember(updatedCrewData)

                        // Update state with the new grouped data
                        setCrew(groupedData)
                        setCrewMembers(groupedData)
                    }
                }

                // Then, fetch the latest data from the server to ensure consistency
                const appendData = [...crew.map((c: any) => c.id)]
                setCrewManifestEntry({})
                const searchFilter: SearchFilter = {}
                searchFilter.id = { in: appendData }
                searchFilter.archived = { eq: false }
                getSectionCrewMembers_LogBookEntrySection({
                    variables: {
                        filter: searchFilter,
                    },
                })
            },
            onError: (error) => {
                console.error('updateCrewMembers_LogBookEntrySection', error)
            },
        },
    )

    const [createCrewWelfareCheck, { loading: createCrewWelfareCheckLoading }] =
        useMutation(CreateCrewWelfare_LogBookEntrySection, {
            onCompleted: (response) => {
                const data = response.createCrewWelfare_LogBookEntrySection
                updateCrewWelfare(data)
            },
            onError: (error) => {
                console.error('createCrewWelfareCheck', error)
            },
        })

    const [createCrewMembers_LogBookEntrySection] = useMutation(
        CreateCrewMembers_LogBookEntrySection,
        {
            onCompleted: (data) => {
                // First, update the UI immediately with the new crew member
                // Get the selected crew member and duty information
                const selectedMember = allMembers.find(
                    (m: any) => m.value === crewManifestEntry.crewMemberID,
                )?.data

                const selectedDuty = duties.find(
                    (d: any) => d.id === crewManifestEntry.dutyPerformedID,
                )

                if (selectedMember && selectedDuty) {
                    // Create a new crew entry with the necessary data for immediate display
                    const newCrewEntry = {
                        id: data.createCrewMembers_LogBookEntrySection.id,
                        crewMemberID: crewManifestEntry.crewMemberID,
                        dutyPerformedID: crewManifestEntry.dutyPerformedID,
                        logBookEntryID: +logBookEntryID,
                        punchIn: loginTime ? formatDBDateTime(loginTime) : null,
                        punchOut: logoutTime
                            ? formatDBDateTime(logoutTime)
                            : null,
                        workDetails:
                            (
                                document.getElementById(
                                    'work-details',
                                ) as HTMLInputElement
                            )?.value || '',
                        crewMember: selectedMember,
                        dutyPerformed: selectedDuty,
                    }

                    // Create a new array with existing crew data plus the new entry
                    let updatedCrewData = crew ? [...crew] : []

                    // Check if this crew member already exists in the list
                    const existingCrewMemberIndex = updatedCrewData.findIndex(
                        (c: any) =>
                            c.crewMemberID === crewManifestEntry.crewMemberID,
                    )

                    if (existingCrewMemberIndex >= 0) {
                        // If the crew member already exists, add this duty to their duties array
                        const existingCrewMember =
                            updatedCrewData[existingCrewMemberIndex]
                        const updatedDuties = existingCrewMember.duties
                            ? [...existingCrewMember.duties]
                            : []

                        updatedDuties.push({
                            id: data.createCrewMembers_LogBookEntrySection.id,
                            dutyPerformed: selectedDuty,
                            punchIn: newCrewEntry.punchIn,
                            punchOut: newCrewEntry.punchOut,
                            workDetails: newCrewEntry.workDetails,
                            dutyPerformedID: newCrewEntry.dutyPerformedID,
                            logBookEntryID: newCrewEntry.logBookEntryID,
                        })

                        // Update the crew member with the new duties array
                        updatedCrewData[existingCrewMemberIndex] = {
                            ...existingCrewMember,
                            duties: updatedDuties,
                        }
                    } else {
                        // If this is a new crew member, add them to the list with their first duty
                        updatedCrewData.push({
                            ...newCrewEntry,
                            duties: [
                                {
                                    id: data
                                        .createCrewMembers_LogBookEntrySection
                                        .id,
                                    dutyPerformed: selectedDuty,
                                    punchIn: newCrewEntry.punchIn,
                                    punchOut: newCrewEntry.punchOut,
                                    workDetails: newCrewEntry.workDetails,
                                    dutyPerformedID:
                                        newCrewEntry.dutyPerformedID,
                                    logBookEntryID: newCrewEntry.logBookEntryID,
                                },
                            ],
                        })
                    }

                    // Group the updated crew data
                    const groupedData = groupCrewDutiesByMember(updatedCrewData)

                    // Update state with the new grouped data
                    setCrew(groupedData)
                    setCrewMembers(groupedData)
                }

                // Then, fetch the latest data from the server to ensure consistency
                const appendData = crew
                    ? [
                          ...crew?.map((c: any) => c.id),
                          data.createCrewMembers_LogBookEntrySection.id,
                      ]
                    : [data.createCrewMembers_LogBookEntrySection.id]
                setCrewManifestEntry({})
                const searchFilter: SearchFilter = {}
                searchFilter.id = { in: appendData }
                getSectionCrewMembers_LogBookEntrySection({
                    variables: {
                        filter: searchFilter,
                    },
                })
            },
            onError: (error) => {
                console.error('createCrewMembers_LogBookEntrySection', error)
            },
        },
    )

    const [getSectionCrewMembers_LogBookEntrySection] = useLazyQuery(
        CrewMembers_LogBookEntrySection,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readCrewMembers_LogBookEntrySections.nodes
                if (data) {
                    // Process crew members with training status
                    const processedData = data.map((section: any) => {
                        if (section.crewMember) {
                            const crewMemberWithTrainingStatus =
                                GetCrewListWithTrainingStatus(
                                    [section.crewMember],
                                    [vessel],
                                )[0]
                            return {
                                ...section,
                                crewMember: crewMemberWithTrainingStatus,
                            }
                        }
                        return section
                    })

                    // Preserve existing duties if they exist
                    let updatedData = processedData
                    if (crew && crew.length > 0) {
                        // Create a map of existing crew members with their duties
                        const existingCrewMap = crew.reduce(
                            (map: Record<string, any>, member: any) => {
                                if (member.crewMemberID) {
                                    map[member.crewMemberID] = member
                                }
                                return map
                            },
                            {},
                        )

                        // Update processed data with existing duties where applicable
                        updatedData = processedData.map((section: any) => {
                            const existingMember =
                                existingCrewMap[section.crewMemberID]
                            if (
                                existingMember &&
                                existingMember.duties &&
                                existingMember.duties.length > 0
                            ) {
                                // Find the matching duty in the existing duties array
                                const existingDutyIndex =
                                    existingMember.duties.findIndex(
                                        (duty: any) => duty.id === section.id,
                                    )

                                if (existingDutyIndex >= 0) {
                                    // This section is already in the duties, update it with the latest data
                                    const updatedDuties = [
                                        ...existingMember.duties,
                                    ]
                                    updatedDuties[existingDutyIndex] = {
                                        ...updatedDuties[existingDutyIndex],
                                        // Update with the latest data from the server
                                        punchIn: section.punchIn,
                                        punchOut: section.punchOut,
                                        workDetails: section.workDetails,
                                    }

                                    return {
                                        ...section,
                                        duties: updatedDuties,
                                    }
                                } else {
                                    // This is a new duty for this crew member, add it to their duties
                                    const updatedDuties = [
                                        ...existingMember.duties,
                                    ]
                                    updatedDuties.push({
                                        id: section.id,
                                        dutyPerformed: section.dutyPerformed,
                                        punchIn: section.punchIn,
                                        punchOut: section.punchOut,
                                        workDetails: section.workDetails,
                                        dutyPerformedID:
                                            section.dutyPerformedID,
                                        logBookEntryID: section.logBookEntryID,
                                    })

                                    return {
                                        ...section,
                                        duties: updatedDuties,
                                    }
                                }
                            }

                            // No existing duties for this crew member, create a new duties array
                            return {
                                ...section,
                                duties: [
                                    {
                                        id: section.id,
                                        dutyPerformed: section.dutyPerformed,
                                        punchIn: section.punchIn,
                                        punchOut: section.punchOut,
                                        workDetails: section.workDetails,
                                        dutyPerformedID:
                                            section.dutyPerformedID,
                                        logBookEntryID: section.logBookEntryID,
                                    },
                                ],
                            }
                        })
                    }

                    // Group crew duties by crew member
                    const groupedData = groupCrewDutiesByMember(updatedData)

                    setCrew(groupedData)
                    setCrewMembers(groupedData)

                    const members = allMembers.filter((member: any) => {
                        if (!data) {
                            return true
                        }
                        return !data.some(
                            (section: any) =>
                                section.crewMember.id === member.value &&
                                section.punchOut === null,
                        )
                    })
                    setCrewMemberOptions(
                        members.filter(
                            (member: any) =>
                                !crewMembersList ||
                                !crewMembersList.includes(+member.value),
                        ),
                    )
                }
            },
            onError: (error: any) => {
                console.error(
                    'getSectionCrewMembers_LogBookEntrySection',
                    error,
                )
            },
        },
    )

    const handleArchive = async () => {
        setOpenConfirmCrewDeleteDialog(false)

        if (!crewManifestEntry.id) {
            toast.error('No crew member selected to delete')
            return
        }

        if (offline) {
            try {
                // First try to delete the record
                const result = await lbCrewModel.delete({
                    id: crewManifestEntry.id,
                })

                if (!result) {
                    // If delete fails, mark as archived
                    await lbCrewModel.save({
                        id: crewManifestEntry.id,
                        archived: true,
                    })
                }

                const appendData = [...crew.map((c: any) => c.id)]
                setCrewManifestEntry({})
                const data = await lbCrewModel.getByIds(appendData)

                if (data) {
                    // Process crew members with training status
                    const processedData = data.map((section: any) => {
                        if (section.crewMember) {
                            const crewMemberWithTrainingStatus =
                                GetCrewListWithTrainingStatus(
                                    [section.crewMember],
                                    [vessel],
                                )[0]
                            return {
                                ...section,
                                crewMember: crewMemberWithTrainingStatus,
                            }
                        }
                        return section
                    })

                    // Group crew duties by crew member
                    const groupedData = groupCrewDutiesByMember(processedData)

                    setCrew(groupedData)
                    setCrewMembers(groupedData)
                }
            } catch (error) {
                console.error('Error deleting crew member:', error)
                toast.error('Failed to delete crew member')
            }
        } else {
            try {
                // Use the delete mutation instead of update
                await deleteCrewMembersLogBookEntrySections({
                    variables: {
                        ids: [crewManifestEntry.id],
                    },
                })
            } catch (error) {
                console.error('Error deleting crew member:', error)
                toast.error('Failed to delete crew member')
            }
        }

        setopenAddCrewMemberDialog(false)
    }

    // Function removed as we're directly using setOpenConfirmCrewDeleteDialog
    const [deleteCrewMembersLogBookEntrySections] = useMutation(
        DeleteCrewMembers_LogBookEntrySections,
        {
            onCompleted: () => {
                const appendData = [...crew.map((c: any) => c.id)]
                const searchFilter: SearchFilter = {}
                searchFilter.id = { in: appendData }
                getSectionCrewMembers_LogBookEntrySection({
                    variables: {
                        filter: searchFilter,
                    },
                })
                setOpenConfirmCrewDeleteDialog(false)
                setopenAddCrewMemberDialog(false)
            },
            onError: (error) => {
                console.error(
                    'deleteCrewMembersLogBookEntrySections error:',
                    error,
                )
            },
        },
    )
    // Function removed as we're using handleArchive instead
    const crewCount = () => {
        if (!crew || !Array.isArray(crew)) return 0

        const count = crew.filter(
            (member: any) =>
                member && member.crewMemberID > 0 && member.punchOut === null,
        ).length
        return count
    }
    useEffect(() => {
        if (crewConfig) {
            handleSetStatus()
        }
    }, [crewConfig])

    useEffect(() => {
        if (!isEmpty(logBookConfig)) {
            handleSetCrewConfig()
        }
    }, [logBookConfig])
    // Removed unused overdueTextWarning variable

    return (
        <>
            <div className="grid lg:grid-cols-8 gap-36 lg:gap-6 xl:gap-8">
                <Card className="lg:col-span-5 space-y-8">
                    <H2>Crew</H2>
                    {crew ? (
                        <div>
                            <Table className="[&_td_span]:inset-y-0">
                                <TableHeader>
                                    <TableRow>
                                        <TableHead className="pl-2.5 text-left align-bottom standard:align-top">
                                            Crew
                                        </TableHead>
                                        <TableHead className="px-[5px] text-left align-bottom standard:align-top">
                                            Duty
                                        </TableHead>

                                        {bp.standard ? (
                                            <>
                                                {punchInStatus !== 'Off' && (
                                                    <TableHead className="px-[5px] text-right">
                                                        {punchInLabel ||
                                                            'Sign In'}
                                                    </TableHead>
                                                )}

                                                {punchOutStatus !== 'Off' && (
                                                    <TableHead className="pl-[5px] pr-2.5 text-right">
                                                        {punchOutLabel ||
                                                            'Sign Out'}
                                                    </TableHead>
                                                )}
                                            </>
                                        ) : (
                                            <TableHead className="text-wrap standard:text-nowrap pr-0 text-right">
                                                <>
                                                    {punchInStatus !== 'Off'
                                                        ? punchInLabel ||
                                                          'Sign In'
                                                        : ''}
                                                    /
                                                    <br className="standard:hidden" />
                                                    {punchOutStatus !== 'Off'
                                                        ? punchOutLabel ||
                                                          'Sign Out'
                                                        : ''}
                                                </>
                                            </TableHead>
                                        )}
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {crew
                                        .filter(
                                            (member: any) =>
                                                +member.crewMemberID > 0 &&
                                                member.archived === false,
                                        )
                                        .map((member: any) => {
                                            // Check if member has multiple duties
                                            const hasMultipleDuties =
                                                member.duties &&
                                                Array.isArray(member.duties) &&
                                                member.duties.length > 1

                                            // Get additional duties (if any)
                                            const additionalDuties =
                                                hasMultipleDuties
                                                    ? member.duties
                                                          .slice(1)
                                                          .filter(
                                                              (duty: any) => {
                                                                  // Get the first duty's title
                                                                  const firstDutyTitle =
                                                                      member
                                                                          .duties[0]
                                                                          .dutyPerformed &&
                                                                      (
                                                                          member
                                                                              .duties[0]
                                                                              .dutyPerformed as any
                                                                      ).title

                                                                  // Get current duty's title
                                                                  const currentDutyTitle =
                                                                      duty.dutyPerformed &&
                                                                      (
                                                                          duty.dutyPerformed as any
                                                                      ).title

                                                                  // Only include duties with different titles
                                                                  return (
                                                                      currentDutyTitle &&
                                                                      firstDutyTitle !==
                                                                          currentDutyTitle
                                                                  )
                                                              },
                                                          )
                                                    : []

                                            return (
                                                <React.Fragment
                                                    key={`crew-${member.id}`}>
                                                    {/* Main crew member row */}
                                                    <TableRow
                                                        key={member.id}
                                                        aria-disabled={locked}
                                                        className={`group ${hasMultipleDuties ? 'border-b-0' : ''}`}
                                                        onClick={(e) => {
                                                            // Don't do anything if locked
                                                            if (locked) return

                                                            // Prevent row click if the event originated from a button
                                                            if (
                                                                e.target instanceof
                                                                    HTMLElement &&
                                                                (e.target.closest(
                                                                    'button',
                                                                ) ||
                                                                    e.target.closest(
                                                                        '[role="button"]',
                                                                    ))
                                                            ) {
                                                                return
                                                            }
                                                            handleEditManifest(
                                                                member,
                                                            )
                                                        }}>
                                                        <TableCell
                                                            className={cn(
                                                                'text-left',
                                                                additionalDuties.length >
                                                                    0 &&
                                                                    ' text-foreground',
                                                            )}>
                                                            <div className="flex items-center">
                                                                <Avatar
                                                                    size="sm"
                                                                    variant={
                                                                        member
                                                                            .crewMember
                                                                            .trainingStatus
                                                                            ?.label !==
                                                                        'Good'
                                                                            ? 'destructive'
                                                                            : 'success'
                                                                    }>
                                                                    <AvatarFallback>
                                                                        {getCrewInitials(
                                                                            member
                                                                                .crewMember
                                                                                .firstName,
                                                                            member
                                                                                .crewMember
                                                                                .surname,
                                                                        )}
                                                                    </AvatarFallback>
                                                                </Avatar>
                                                                <div className="hidden leading-none sm:flex flex-col justify-center ml-2">
                                                                    <span className="flex gap-2.5 items-center">
                                                                        <span className="text-foreground">
                                                                            {
                                                                                member
                                                                                    .crewMember
                                                                                    .firstName
                                                                            }{' '}
                                                                            {
                                                                                member
                                                                                    .crewMember
                                                                                    .surname
                                                                            }
                                                                        </span>
                                                                        {member.workDetails && (
                                                                            <Popover>
                                                                                <PopoverTrigger
                                                                                    onClick={(
                                                                                        e,
                                                                                    ) => {
                                                                                        e.stopPropagation()
                                                                                    }}
                                                                                    className="p-0 text-muted-foreground">
                                                                                    <InfoIcon
                                                                                        className="text-light-blue-vivid-900 fill-light-blue-vivid-50"
                                                                                        size={
                                                                                            24
                                                                                        }
                                                                                    />
                                                                                </PopoverTrigger>
                                                                                <PopoverContent className="w-80">
                                                                                    <div className="text-sm">
                                                                                        {
                                                                                            member.workDetails
                                                                                        }
                                                                                    </div>
                                                                                </PopoverContent>
                                                                            </Popover>
                                                                        )}
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </TableCell>

                                                        <TableCell
                                                            className={cn(
                                                                'text-left grid items-center',
                                                                additionalDuties.length >
                                                                    0 &&
                                                                    'text-foreground',
                                                            )}>
                                                            <div className="truncate">
                                                                {member.dutyPerformed &&
                                                                (
                                                                    member.dutyPerformed as any
                                                                ).title
                                                                    ? (
                                                                          member.dutyPerformed as any
                                                                      ).title
                                                                    : 'Not assigned'}
                                                            </div>
                                                        </TableCell>

                                                        {bp.standard ? (
                                                            <>
                                                                <TableCell
                                                                    className={cn(
                                                                        'text-right',
                                                                        additionalDuties.length >
                                                                            0 &&
                                                                            'text-foreground',
                                                                    )}>
                                                                    {punchInStatus !==
                                                                        'Off' &&
                                                                        (member?.punchIn
                                                                            ? formatDateTime(
                                                                                  member.punchIn,
                                                                              )
                                                                            : 'Not Available')}
                                                                </TableCell>

                                                                <TableCell
                                                                    className={cn(
                                                                        'text-right phablet:pr-2.5 relaive',
                                                                        additionalDuties.length >
                                                                            0 &&
                                                                            'text-input',
                                                                    )}>
                                                                    <div className="flex justify-end">
                                                                        {punchOutStatus !==
                                                                            'Off' &&
                                                                            (!member.punchOut ? (
                                                                                <Button
                                                                                    variant="text"
                                                                                    className={cn(
                                                                                        'text-fill-inherit font-normal h-fit',
                                                                                    )}
                                                                                    disabled={
                                                                                        locked
                                                                                    }
                                                                                    onClick={(
                                                                                        e,
                                                                                    ) => {
                                                                                        e.stopPropagation()
                                                                                        handleSignOutTime(
                                                                                            member,
                                                                                        )
                                                                                    }}>
                                                                                    {punchOutLabel ||
                                                                                        'Sign Out'}
                                                                                </Button>
                                                                            ) : (
                                                                                <span
                                                                                    className={cn(
                                                                                        'whitespace-nowrap h-8 flex items-center',
                                                                                    )}>
                                                                                    {formatDateTime(
                                                                                        member.punchOut,
                                                                                    )}
                                                                                </span>
                                                                            ))}
                                                                    </div>
                                                                </TableCell>
                                                            </>
                                                        ) : (
                                                            <TableCell>
                                                                <div
                                                                    className={cn(
                                                                        'text-right relaive flex flex-col phablet:pr-2.5 justify-center items-end',
                                                                        additionalDuties.length >
                                                                            0 &&
                                                                            'text-input',
                                                                    )}>
                                                                    {punchInStatus !==
                                                                        'Off' &&
                                                                        (member?.punchIn
                                                                            ? formatDateTime(
                                                                                  member.punchIn,
                                                                              )
                                                                            : 'Not Available')}

                                                                    {punchOutStatus !==
                                                                        'Off' &&
                                                                        (!member.punchOut ? (
                                                                            <Button
                                                                                variant="text"
                                                                                className={cn(
                                                                                    'text-fill-inherit font-normal h-fit ml-auto',
                                                                                )}
                                                                                disabled={
                                                                                    locked
                                                                                }
                                                                                onClick={(
                                                                                    e,
                                                                                ) => {
                                                                                    e.stopPropagation()
                                                                                    handleSignOutTime(
                                                                                        member,
                                                                                    )
                                                                                }}>
                                                                                {punchOutLabel ||
                                                                                    'Sign Out'}
                                                                            </Button>
                                                                        ) : (
                                                                            <span
                                                                                className={cn(
                                                                                    'whitespace-nowrap h-8 flex items-center w-full justify-end',
                                                                                )}>
                                                                                {formatDateTime(
                                                                                    member.punchOut,
                                                                                )}
                                                                            </span>
                                                                        ))}
                                                                </div>
                                                            </TableCell>
                                                        )}
                                                    </TableRow>

                                                    {/* Render additional duties as separate rows */}
                                                    {additionalDuties.map(
                                                        (
                                                            duty: any,
                                                            index: number,
                                                        ) => (
                                                            <TableRow
                                                                key={`duty-${duty.id}-${index}`}
                                                                aria-disabled={
                                                                    locked
                                                                }
                                                                className={cn(
                                                                    'group',
                                                                    index ===
                                                                        additionalDuties.length -
                                                                            1
                                                                        ? ''
                                                                        : 'border-b-0',
                                                                )}
                                                                onClick={(
                                                                    e,
                                                                ) => {
                                                                    // Don't do anything if locked
                                                                    if (locked)
                                                                        return

                                                                    // Prevent row click if the event originated from a button
                                                                    if (
                                                                        e.target instanceof
                                                                            HTMLElement &&
                                                                        (e.target.closest(
                                                                            'button',
                                                                        ) ||
                                                                            e.target.closest(
                                                                                '[role="button"]',
                                                                            ))
                                                                    ) {
                                                                        return
                                                                    }
                                                                    handleEditManifest(
                                                                        member,
                                                                    )
                                                                }}>
                                                                <td
                                                                    className={cn(
                                                                        ' text-input tiny:px-1 phablet:px-2.5 z-[5] py-2 text-left relative',
                                                                    )}>
                                                                    <div className="flex flex-col absolute -top-[42%] items-center w-8 h-full">
                                                                        {index ===
                                                                            0 && (
                                                                            <div className="w-full h-2" />
                                                                        )}
                                                                        <div className="w-[1px] flex-1 border-l border-dashed border-outer-space-400" />
                                                                        <div className="size-[5px] rounded-full bg-background border border-outer-space-400" />
                                                                    </div>
                                                                </td>

                                                                <TableCell
                                                                    className={cn(
                                                                        ' text-input grid items-center text-left',
                                                                    )}>
                                                                    <div className="truncate">
                                                                        {duty.dutyPerformed &&
                                                                        (
                                                                            duty.dutyPerformed as any
                                                                        ).title
                                                                            ? (
                                                                                  duty.dutyPerformed as any
                                                                              )
                                                                                  .title
                                                                            : 'Not assigned'}
                                                                    </div>
                                                                </TableCell>
                                                                {bp.standard ? (
                                                                    <>
                                                                        <TableCell
                                                                            className={cn(
                                                                                ' text-input py-2 text-right',
                                                                            )}>
                                                                            {punchInStatus !==
                                                                                'Off' &&
                                                                                (duty?.punchIn
                                                                                    ? formatDateTime(
                                                                                          duty.punchIn,
                                                                                      )
                                                                                    : 'Not Available')}
                                                                        </TableCell>
                                                                        <TableCell
                                                                            className={cn(
                                                                                ' text-input phablet:pr-2.5 py-2 text-right',
                                                                            )}>
                                                                            <div className="flex justify-end">
                                                                                {punchOutStatus !==
                                                                                    'Off' &&
                                                                                    (!duty.punchOut ? (
                                                                                        <Button
                                                                                            variant="text"
                                                                                            className={cn(
                                                                                                'text-fill-inherit font-normal size-fit',
                                                                                            )}
                                                                                            disabled={
                                                                                                locked
                                                                                            }
                                                                                            onClick={(
                                                                                                e,
                                                                                            ) => {
                                                                                                e.stopPropagation()
                                                                                                handleSignOutTime(
                                                                                                    duty,
                                                                                                )
                                                                                            }}>
                                                                                            {punchOutLabel ||
                                                                                                'Sign Out'}
                                                                                        </Button>
                                                                                    ) : (
                                                                                        <span
                                                                                            className={cn(
                                                                                                'whitespace-nowrap flex items-center w-full justify-end',
                                                                                            )}>
                                                                                            {formatDateTime(
                                                                                                duty.punchOut,
                                                                                            )}
                                                                                        </span>
                                                                                    ))}
                                                                            </div>
                                                                        </TableCell>
                                                                    </>
                                                                ) : (
                                                                    <TableCell>
                                                                        <div className="text-right flex-1 phablet:pr-2.5 relaive flex flex-col justify-end items-end">
                                                                            {punchInStatus !==
                                                                                'Off' &&
                                                                                (duty?.punchIn
                                                                                    ? formatDateTime(
                                                                                          duty.punchIn,
                                                                                      )
                                                                                    : 'Not Available')}
                                                                            {punchOutStatus !==
                                                                                'Off' &&
                                                                                (!duty.punchOut ? (
                                                                                    <Button
                                                                                        variant="text"
                                                                                        className={cn(
                                                                                            'text-fill-inherit font-normal size-fit',
                                                                                        )}
                                                                                        disabled={
                                                                                            locked
                                                                                        }
                                                                                        onClick={(
                                                                                            e,
                                                                                        ) => {
                                                                                            e.stopPropagation()
                                                                                            handleSignOutTime(
                                                                                                duty,
                                                                                            )
                                                                                        }}>
                                                                                        {punchOutLabel ||
                                                                                            'Sign Out'}
                                                                                    </Button>
                                                                                ) : (
                                                                                    <span
                                                                                        className={cn(
                                                                                            'whitespace-nowrap flex items-center w-full justify-end',
                                                                                        )}>
                                                                                        {formatDateTime(
                                                                                            duty.punchOut,
                                                                                        )}
                                                                                    </span>
                                                                                ))}
                                                                        </div>
                                                                    </TableCell>
                                                                )}
                                                            </TableRow>
                                                        ),
                                                    )}
                                                </React.Fragment>
                                            )
                                        })}
                                </TableBody>
                            </Table>

                            <FormFooter>
                                <div className="flex items-center gap-2">
                                    <Label className="mb-0 font-semibold">
                                        Minimum crew:
                                    </Label>
                                    <Badge
                                        variant={
                                            isVessel(vessel) &&
                                            crewCount() > (vessel?.maxPOB ?? 0)
                                                ? 'destructive'
                                                : 'success'
                                        }
                                        className="rounded-full flex items-center justify-center size-[25px]">
                                        {isVessel(vessel) ? vessel.minCrew : 0}
                                    </Badge>
                                    {isVessel(vessel) &&
                                        crewCount() > (vessel?.maxPOB ?? 0) && (
                                            <small className="text-destructive">
                                                You have more people on board
                                                than your vessel is configured
                                                to carry
                                            </small>
                                        )}
                                </div>

                                <Button
                                    className="w-full tiny:w-fit px-2.5"
                                    disabled={locked}
                                    onClick={handleAddManifest}>
                                    Add crew
                                </Button>
                            </FormFooter>
                        </div>
                    ) : (
                        <div className="flex justify-end">
                            <Button
                                disabled={locked}
                                onClick={handleAddManifest}>
                                Add crew members to this trip
                            </Button>
                        </div>
                    )}
                </Card>

                {crew &&
                    logBookConfig &&
                    logBookConfig?.customisedLogBookComponents?.nodes?.find(
                        (config: { title: string; active: boolean }) =>
                            config.title === 'Crew Welfare' &&
                            config.active === true,
                    ) && (
                        <Card className="lg:col-span-3 space-y-8">
                            <CrewWelfare
                                offline={offline}
                                logBookConfig={logBookConfig}
                                locked={locked || !edit_logBookEntry}
                                crewWelfareCheck={crewWelfareCheck}
                                updateCrewWelfare={updateCrewWelfare}
                            />
                        </Card>
                    )}
            </div>

            <AlertDialogNew
                openDialog={openAddCrewMemberDialog}
                setOpenDialog={setopenAddCrewMemberDialog}
                handleCreate={handleSave}
                handleCancel={handleCancel}
                handleDestructiveAction={
                    crewManifestEntry.id > 0
                        ? () => setOpenConfirmCrewDeleteDialog(true)
                        : undefined
                }
                showDestructiveAction={crewManifestEntry.id > 0}
                destructiveActionText="Delete"
                title={
                    crewManifestEntry.id > 0
                        ? 'Update crew member'
                        : 'Add crew member'
                }
                actionText={crewManifestEntry.id > 0 ? 'Update' : 'Add'}
                cancelText="Cancel"
                contentClassName="max-w-2xl">
                <div className="space-y-8">
                    <div className="flex items-top gap-4 mb-4">
                        {crewMember && (
                            <Avatar
                                variant={
                                    crewMember.data?.trainingStatus?.label !==
                                    'Good'
                                        ? 'destructive'
                                        : 'success'
                                }
                                className="size-12 border-2">
                                <AvatarImage
                                    src={crewMember.profile?.avatar}
                                    alt={`${crewMember.profile?.firstName || ''} ${crewMember.profile?.surname || ''}`.trim()}
                                />
                                <AvatarFallback>
                                    {getCrewInitials(
                                        crewMember.profile?.firstName ||
                                            crewMember.data?.firstName,
                                        crewMember.profile?.surname ||
                                            crewMember.data?.surname,
                                    )}
                                </AvatarFallback>
                            </Avatar>
                        )}
                        <div className="flex-1 items-center">
                            <H3 className="text-lg">
                                {crewMember?.profile?.firstName ||
                                crewMember?.data?.firstName ||
                                crewMember?.profile?.surname ||
                                crewMember?.data?.surname
                                    ? `${crewMember.profile?.firstName || crewMember.data?.firstName || ''} ${crewMember.profile?.surname || crewMember.data?.surname || ''}`.trim()
                                    : ''}
                            </H3>
                            {crewMember &&
                                (crewMember?.data?.trainingStatus?.label !==
                                'Good' ? (
                                    <div>
                                        <p className="text-sm text-destructive">
                                            Training is overdue
                                        </p>
                                        {crewMember?.data?.trainingStatus
                                            ?.dues &&
                                            crewMember.data.trainingStatus.dues
                                                .length > 0 && (
                                                <ul className="text-sm text-destructive">
                                                    {crewMember.data.trainingStatus.dues.map(
                                                        (
                                                            due: any,
                                                            index: number,
                                                        ) => (
                                                            <li
                                                                key={index}
                                                                className="flex items-center gap-2">
                                                                <span className="text-destructive text-lg">
                                                                    •
                                                                </span>
                                                                <span>
                                                                    {
                                                                        due
                                                                            .trainingType
                                                                            .title
                                                                    }{' '}
                                                                    -{' '}
                                                                    {
                                                                        due
                                                                            .status
                                                                            .label
                                                                    }
                                                                </span>
                                                            </li>
                                                        ),
                                                    )}
                                                </ul>
                                            )}
                                    </div>
                                ) : (
                                    <p className="text-sm text-bright-turquoise-600">
                                        Training up to date
                                    </p>
                                ))}
                        </div>
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-[31px]">
                        <Combobox
                            label={'Crew member'}
                            modal
                            buttonClassName="w-full"
                            options={crewMemberOptions.map(
                                (option: CrewOption) => ({
                                    ...option,
                                    value: String(option.value),
                                }),
                            )}
                            value={crewMember}
                            onChange={handleCrewMember}
                        />
                        <CrewDutyDropdown
                            label="Primary duty"
                            crewDutyID={Number(duty?.value) || 0}
                            onChange={handleDuty}
                            multi={false}
                            modal
                            offline={offline}
                            hideCreateOption={false}
                        />
                    </div>

                    <div className="grid grid-cols-1 xs:grid-cols-2 pr-px gap-[31px]">
                        {punchInStatus !== 'Off' && (
                            <DatePicker
                                id="signin-date"
                                modal
                                value={loginTime}
                                onChange={handleLogin}
                                label={punchInLabel || 'Sign In'}
                                dateFormat="dd MMM,"
                                placeholder={`${punchInLabel || 'Sign In'} Time`}
                                mode="single"
                                type="datetime"
                                closeOnSelect={false}
                                icon={Clock}
                                className="w-full"
                            />
                        )}

                        {punchOutStatus !== 'Off' && (
                            <DatePicker
                                id="signout-date"
                                modal
                                value={logoutTime || undefined}
                                onChange={handleLogout}
                                label={punchOutLabel || 'Sign Out'}
                                placeholder={`${punchOutLabel || 'Sign Out'} Time`}
                                mode="single"
                                type="datetime" // Keep datetime to include time picker
                                dateFormat="dd MMM,"
                                timeFormat="HH:mm" // Explicitly set time format
                                closeOnSelect={false} // Allow time selection
                                clearable={true}
                                icon={Clock}
                                className="w-full"
                            />
                        )}
                    </div>

                    {workDetailsStatus !== 'Off' && (
                        <Label
                            htmlFor="work-details"
                            label={workDetailsLabel || 'Work Details'}>
                            <Textarea
                                id="work-details"
                                rows={4}
                                className="w-full resize-none"
                                placeholder="Enter work details"
                                defaultValue={crewManifestEntry?.workDetails}
                            />
                        </Label>
                    )}
                </div>
            </AlertDialogNew>

            <AlertDialogNew
                openDialog={openEditLogoutTimeDialog}
                setOpenDialog={setOpenEditLogoutTimeDialog}
                handleCreate={() => handleSave('update')}
                handleCancel={handleCancel}
                actionText={getResponsiveLabel(
                    bp.phablet,
                    'Update',
                    'Update Time',
                )}
                cancelText="Cancel"
                contentClassName="top-[38svh]"
                size="sm"
                title="Update sign out time"
                className="space-y-4">
                <div className="w-full relative">
                    <DatePicker
                        modal
                        id="signout-date"
                        name="signout-date"
                        label={`${punchOutLabel || 'Sign Out'} Time`}
                        value={logoutTime || undefined}
                        mode="single"
                        type="datetime" // Keep datetime to include time picker
                        onChange={handleLogout}
                        dateFormat="dd MMM,"
                        timeFormat="HH:mm" // Explicitly set time format
                        placeholder={`${punchOutLabel || 'Sign Out'} Time`}
                        closeOnSelect={false} // Allow time selection
                        clearable={true}
                        icon={Clock}
                        className="w-full"
                    />
                </div>
            </AlertDialogNew>

            <AlertDialogNew
                openDialog={openCrewTrainingDueDialog}
                setOpenDialog={setOpenCrewTrainingDueDialog}
                contentClassName="max-w-xl"
                className="space-y-4"
                cancelText="Cancel"
                actionText="Yes, Continue"
                handleCreate={() => setOpenCrewTrainingDueDialog(false)}
                handleCancel={() => {
                    setOpenCrewTrainingDueDialog(false)
                    setCrewMember(null)
                    setDuty(null)
                }}
                title="Crew member training status"
                variant="warning"
                showIcon>
                <div className="grid grid-cols-1 gap-4">
                    <p>
                        {crewMember?.data?.firstName ||
                        crewMember?.data?.surname
                            ? `${crewMember.data.firstName || ''} ${crewMember.data.surname || ''}`.trim()
                            : 'This crew member'}{' '}
                        has overdue training sessions on this vessel. These
                        sessions are:
                    </p>

                    {crewMember?.data?.trainingStatus?.dues?.map(
                        (item: any, dueIndex: number) => (
                            <p key={dueIndex}>
                                <strong>
                                    {`${item.trainingType.title} - ${item.status.label}`}
                                </strong>
                            </p>
                        ),
                    )}

                    <p>
                        Do you still want to add this crew member to this
                        vessel?
                    </p>
                </div>
            </AlertDialogNew>

            <AlertDialogNew
                openDialog={openConfirmCrewDeleteDialog}
                setOpenDialog={setOpenConfirmCrewDeleteDialog}
                handleCreate={handleArchive}
                handleCancel={() => {
                    setOpenConfirmCrewDeleteDialog(false)
                    // Don't reset crew member here as it's needed for the parent dialog
                }}
                actionText="Remove"
                cancelText="Cancel"
                contentClassName="max-w-md"
                variant="warning"
                showIcon
                title="Remove crew member">
                <div className="text-sm">
                    Are you sure you want to remove{' '}
                    {crewMember?.data?.firstName || crewMember?.data?.surname
                        ? `${crewMember.data.firstName || ''} ${crewMember.data.surname || ''}`.trim()
                        : 'this crew member'}{' '}
                    from this trip manifest?
                </div>
            </AlertDialogNew>
        </>
    )
}
