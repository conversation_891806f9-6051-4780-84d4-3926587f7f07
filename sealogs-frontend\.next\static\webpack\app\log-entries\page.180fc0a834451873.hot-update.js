"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/supernumerary-event.tsx":
/*!**********************************************************!*\
  !*** ./src/app/ui/logbook/forms/supernumerary-event.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SupernumeraryEvent; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _barrel_optimize_names_Check_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_signature_pad__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/signature-pad */ \"(app-pages-browser)/./src/components/signature-pad.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var lodash_trim__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! lodash/trim */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/trim.js\");\n/* harmony import */ var lodash_trim__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(lodash_trim__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _components_time__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../components/time */ \"(app-pages-browser)/./src/app/ui/logbook/components/time.tsx\");\n/* harmony import */ var _app_offline_models_tripEvent__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/tripEvent */ \"(app-pages-browser)/./src/app/offline/models/tripEvent.js\");\n/* harmony import */ var _app_offline_models_eventType_Supernumerary__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/eventType_Supernumerary */ \"(app-pages-browser)/./src/app/offline/models/eventType_Supernumerary.js\");\n/* harmony import */ var _app_offline_models_supernumerary_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/models/supernumerary_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/supernumerary_LogBookEntrySection.js\");\n/* harmony import */ var _app_offline_models_logBookEntrySection_Signature__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/app/offline/models/logBookEntrySection_Signature */ \"(app-pages-browser)/./src/app/offline/models/logBookEntrySection_Signature.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_daily_check_field__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/daily-check-field */ \"(app-pages-browser)/./src/components/daily-check-field.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Removed unused imports\nfunction SupernumeraryEvent(param) {\n    let { logBookConfig = false, locked, closeModal, currentTrip = false, updateTripReport, tripReport, inLogbook = false, selectedEvent = false, offline = false } = param;\n    var _supernumerary_guestList, _logBookConfig_policies;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useSearchParams)();\n    var _searchParams_get;\n    const logentryID = (_searchParams_get = searchParams.get(\"logentryID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    const [currentSignature, setCurrentSignature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentGuest, setCurrentGuest] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [supernumeraryConfig, setSupernumeraryConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openAddGuestDialog, setOpenAddGuestDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentEvent, setCurrentEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(selectedEvent);\n    const [supernumerary, setSupernumerary] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [formError, setFormError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [closeOnSave, setCloseOnSave] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const tripEventModel = new _app_offline_models_tripEvent__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\n    const supernumeraryModel = new _app_offline_models_eventType_Supernumerary__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n    const supernumerarySectionModel = new _app_offline_models_supernumerary_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_14__[\"default\"]();\n    const signatureModel = new _app_offline_models_logBookEntrySection_Signature__WEBPACK_IMPORTED_MODULE_15__[\"default\"]();\n    const handleSaveGuest = async ()=>{\n        if (!supernumeraryConfig || supernumeraryConfig.find((c)=>c.title === \"Supernumerary_Signature\" && c.status != \"Off\")) {\n            if ((currentSignature === null || currentSignature === void 0 ? void 0 : currentSignature.id) > 0) {\n                if (offline) {\n                    // updateLogBookEntrySection_Signature\n                    await signatureModel.save({\n                        id: currentSignature === null || currentSignature === void 0 ? void 0 : currentSignature.id,\n                        signatureData: currentSignature.signatureData\n                    });\n                } else {\n                    updateLogBookEntrySection_Signature({\n                        variables: {\n                            input: {\n                                id: currentSignature === null || currentSignature === void 0 ? void 0 : currentSignature.id,\n                                signatureData: currentSignature.signatureData\n                            }\n                        }\n                    });\n                }\n                updateGuest(currentSignature === null || currentSignature === void 0 ? void 0 : currentSignature.id);\n            } else {\n                if (currentSignature) {\n                    if (offline) {\n                        // createLogBookEntrySection_Signature\n                        const data = await signatureModel.save({\n                            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_16__.generateUniqueId)(),\n                            signatureData: currentSignature.signatureData\n                        });\n                        updateGuest(data.id);\n                        setOpenAddGuestDialog(false);\n                    } else {\n                        createLogBookEntrySection_Signature({\n                            variables: {\n                                input: {\n                                    signatureData: currentSignature.signatureData\n                                }\n                            }\n                        });\n                    }\n                } else {\n                    updateGuest();\n                }\n            }\n        } else {\n            updateGuest();\n        }\n    };\n    const handleDeleteGuest = async ()=>{\n        if (offline) {\n            // updateSupernumeraryLogbookEntrySection\n            await supernumerarySectionModel.save({\n                id: currentGuest.id,\n                supernumeraryID: 0\n            });\n            setCurrentSignature(false);\n            // getSectionSupernumerary_LogBookEntrySection\n            await supernumerarySectionModel.getByIds(supernumerary.guestList.nodes.map((guest)=>guest.id));\n            setCurrentGuest(false);\n            // readOneEventType_Supernumerary\n            const data = await supernumeraryModel.getById(supernumerary.id);\n            setSupernumerary(data);\n            setOpenAddGuestDialog(false);\n        } else {\n            updateSupernumeraryLogbookEntrySection({\n                variables: {\n                    input: {\n                        id: currentGuest.id,\n                        supernumeraryID: 0\n                    }\n                }\n            });\n        }\n    };\n    const [createTripEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateTripEvent, {\n        onCompleted: (response)=>{\n            const data = response.createTripEvent;\n            setCurrentEvent(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating trip event\", error);\n        }\n    });\n    const [createSupernumerary, { loading: createSupernumeraryLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateEventType_Supernumerary, {\n        onCompleted: (data)=>{\n            const supernumeraryID = data.createEventType_Supernumerary.id;\n            setSupernumerary({\n                ...supernumerary,\n                id: supernumeraryID\n            });\n            createTripEvent({\n                variables: {\n                    input: {\n                        eventCategory: \"EventSupernumerary\",\n                        logBookEntrySectionID: currentTrip.id,\n                        supernumeraryID: supernumeraryID\n                    }\n                }\n            });\n            if (closeOnSave) {\n                setCloseOnSave(false);\n                closeModal();\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error creating supernumerary event\", error);\n        }\n    });\n    const [createSupernumeraryLogbookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateSupernumerary_LogBookEntrySection, {\n        onCompleted: ()=>{\n            loadSupernumerary(supernumerary.id);\n            setCurrentSignature(false);\n            setOpenAddGuestDialog(false);\n        },\n        onError: (error)=>{\n            console.error(error);\n        }\n    });\n    // This function is not currently used but may be needed in the future\n    // for retrieving event data\n    // const getCurrentEvent = async (id: any) => {\n    //     if (offline) {\n    //         // getTripEvent\n    //         const event = await tripEventModel.getById(id)\n    //         if (event) {\n    //             // setTrainingID(event.crewTraining.id)\n    //             setCurrentEvent(event)\n    //         }\n    //     } else {\n    //         getTripEvent({\n    //             variables: {\n    //                 id: id,\n    //             },\n    //         })\n    //     }\n    // }\n    // This query is not currently used but may be needed if getCurrentEvent is used\n    // const [getTripEvent] = useLazyQuery(GetTripEvent, {\n    //     fetchPolicy: 'cache-and-network',\n    //     onCompleted: (response) => {\n    //         const event = response.readOneTripEvent\n    //         if (event) {\n    //             // setTrainingID(event.crewTraining.id)\n    //         }\n    //     },\n    //     onError: (error) => {\n    //         console.error('Error getting current event', error)\n    //     },\n    // })\n    const [updateTripEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateTripEvent, {\n        onCompleted: ()=>{\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    currentTrip.id\n                ]\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error updating trip event\", error);\n        }\n    });\n    const [updateSupernumerary, { loading: updateSupernumeraryLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateEventType_Supernumerary, {\n        onCompleted: ()=>{\n            if (closeOnSave) {\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n                setCloseOnSave(false);\n                closeModal();\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error updating supernumerary event\", error);\n        }\n    });\n    const [updateSupernumeraryLogbookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateSupernumerary_LogBookEntrySection, {\n        onCompleted: ()=>{\n            setCurrentSignature(false);\n            getSectionSupernumerary_LogBookEntrySection({\n                variables: {\n                    id: supernumerary.guestList.nodes.map((guest)=>guest.id)\n                }\n            });\n            setOpenAddGuestDialog(false);\n        },\n        onError: (error)=>{\n            console.error(error);\n        }\n    });\n    const [createLogBookEntrySection_Signature] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateLogBookEntrySection_Signature, {\n        onCompleted: (data)=>{\n            var _data_createLogBookEntrySection_Signature;\n            updateGuest(data === null || data === void 0 ? void 0 : (_data_createLogBookEntrySection_Signature = data.createLogBookEntrySection_Signature) === null || _data_createLogBookEntrySection_Signature === void 0 ? void 0 : _data_createLogBookEntrySection_Signature.id);\n            setOpenAddGuestDialog(false);\n        },\n        onError: (error)=>{\n            console.error(error);\n        }\n    });\n    const updateGuest = async function() {\n        let signatureID = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;\n        var _document_getElementById, _document_getElementById1;\n        const firstName = (_document_getElementById = document.getElementById(\"firstname\")) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value;\n        const surname = (_document_getElementById1 = document.getElementById(\"surname\")) === null || _document_getElementById1 === void 0 ? void 0 : _document_getElementById1.value;\n        if (currentGuest && (currentGuest === null || currentGuest === void 0 ? void 0 : currentGuest.id) > 0) {\n            if (signatureID == 0) {\n                signatureID = currentGuest.sectionSignature.id;\n            }\n            if (offline) {\n                // updateSupernumeraryLogbookEntrySection\n                await supernumerarySectionModel.save({\n                    id: currentGuest.id,\n                    firstName: firstName,\n                    surname: surname,\n                    sectionSignatureID: signatureID,\n                    supernumeraryID: supernumerary.id\n                });\n                setCurrentSignature(false);\n                // getSectionSupernumerary_LogBookEntrySection\n                await supernumerarySectionModel.getByIds(supernumerary.guestList.nodes.map((guest)=>guest.id));\n                setCurrentGuest(false);\n                // readOneEventType_Supernumerary\n                const data = await supernumeraryModel.getById(supernumerary.id);\n                setSupernumerary(data);\n                setOpenAddGuestDialog(false);\n            } else {\n                updateSupernumeraryLogbookEntrySection({\n                    variables: {\n                        input: {\n                            id: currentGuest.id,\n                            firstName: firstName,\n                            surname: surname,\n                            sectionSignatureID: signatureID,\n                            supernumeraryID: supernumerary.id\n                        }\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                // createSupernumeraryLogbookEntrySection\n                await supernumerarySectionModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_16__.generateUniqueId)(),\n                    firstName: firstName,\n                    surname: surname,\n                    logBookEntryID: logentryID,\n                    sectionSignatureID: signatureID,\n                    supernumeraryID: supernumerary.id\n                });\n                loadSupernumerary(supernumerary.id);\n                setCurrentSignature(false);\n                setOpenAddGuestDialog(false);\n            } else {\n                if (supernumerary.id > 0) {\n                    createSupernumeraryLogbookEntrySection({\n                        variables: {\n                            input: {\n                                firstName: firstName,\n                                surname: surname,\n                                logBookEntryID: logentryID,\n                                sectionSignatureID: signatureID,\n                                supernumeraryID: supernumerary.id\n                            }\n                        }\n                    });\n                }\n            }\n        }\n    };\n    const [updateLogBookEntrySection_Signature] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateLogBookEntrySection_Signature, {\n        onCompleted: ()=>{},\n        onError: (error)=>{\n            console.error(error);\n        }\n    });\n    const onSignatureChanged = (sign)=>{\n        currentSignature ? setCurrentSignature({\n            ...currentSignature,\n            signatureData: sign\n        }) : setCurrentSignature({\n            signatureData: sign\n        });\n    };\n    const [getSectionSupernumerary_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_23__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.Supernumerary_LogBookEntrySection, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: ()=>{\n            // We don't need to use the response data here\n            setCurrentGuest(false);\n            readOneEventType_Supernumerary({\n                variables: {\n                    id: supernumerary.id\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Supernumerary_LogBookEntrySection error\", error);\n        }\n    });\n    // Using handleSaveGuest and setOpenAddGuestDialog(false) directly instead\n    const handleAddGuest = ()=>{\n        setCurrentGuest(false);\n        setOpenAddGuestDialog(true);\n    };\n    const handleFirstNameClick = (guest)=>{\n        setCurrentGuest(guest);\n        setOpenAddGuestDialog(true);\n    };\n    const handleSave = async ()=>{\n        const input = {\n            id: supernumerary.id || 0,\n            title: supernumerary.title,\n            totalGuest: +supernumerary.totalGuest || 0,\n            isBriefed: supernumerary.isBriefed || false,\n            briefingTime: supernumerary.briefingTime || dayjs__WEBPACK_IMPORTED_MODULE_8___default()().format(\"HH:mm\")\n        };\n        setFormError({});\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_9___default()(supernumerary)) {\n            setFormError({\n                element: \"title\",\n                message: \"Title is required\"\n            });\n            return;\n        } else if (!supernumerary.title || lodash_isEmpty__WEBPACK_IMPORTED_MODULE_9___default()(lodash_trim__WEBPACK_IMPORTED_MODULE_10___default()(supernumerary.title))) {\n            setFormError({\n                element: \"title\",\n                message: \"Title is required\"\n            });\n        } else if (!supernumerary.totalGuest || +supernumerary.totalGuest <= 0) {\n            setFormError({\n                element: \"totalGuest\",\n                message: \"Number of guests is required\"\n            });\n            return;\n        }\n        if (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.supernumerary) {\n            if (offline) {\n                // updateTripEvent\n                const data = await tripEventModel.save({\n                    id: +currentEvent.id,\n                    eventCategory: \"EventSupernumerary\",\n                    logBookEntrySectionID: currentTrip.id\n                });\n                setCurrentEvent(data);\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n            } else {\n                updateTripEvent({\n                    variables: {\n                        input: {\n                            id: +currentEvent.id,\n                            eventCategory: \"EventSupernumerary\",\n                            logBookEntrySectionID: currentTrip.id\n                        }\n                    }\n                });\n            }\n        }\n        if (!supernumerary.id || +supernumerary.id === 0) {\n            if (offline) {\n                const data = await supernumeraryModel.save({\n                    ...input,\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_16__.generateUniqueId)()\n                });\n                const supernumeraryID = data.id;\n                setSupernumerary(data);\n                // createTripEvent\n                const tripEventData = await tripEventModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_16__.generateUniqueId)(),\n                    eventCategory: \"EventSupernumerary\",\n                    logBookEntrySectionID: currentTrip.id,\n                    supernumeraryID: supernumeraryID\n                });\n                setCurrentEvent(tripEventData);\n                if (closeOnSave) {\n                    setCloseOnSave(false);\n                    closeModal();\n                }\n            } else {\n                createSupernumerary({\n                    variables: {\n                        input: input\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                const data = await supernumeraryModel.save(input);\n                setSupernumerary(data);\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n                if (closeOnSave) {\n                    setCloseOnSave(false);\n                    closeModal();\n                }\n            } else {\n                updateSupernumerary({\n                    variables: {\n                        input: input\n                    }\n                });\n            }\n        }\n    };\n    const handleBriefingTimeChange = (date)=>{\n        const briefingTime = dayjs__WEBPACK_IMPORTED_MODULE_8___default()(date).format(\"HH:mm\");\n        setSupernumerary({\n            ...supernumerary,\n            briefingTime: briefingTime\n        });\n    };\n    const [readOneEventType_Supernumerary, { loading: loadingSupernumerary }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_23__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.ReadOneEventType_Supernumerary, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            setSupernumerary(data.readOneEventType_Supernumerary);\n        },\n        onError: (error)=>{\n            console.error(error);\n        }\n    });\n    const loadSupernumerary = async (id)=>{\n        if (offline) {\n            // readOneEventType_Supernumerary\n            const data = await supernumeraryModel.getById(id);\n            setSupernumerary(data);\n        } else {\n            await readOneEventType_Supernumerary({\n                variables: {\n                    id: id\n                }\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (logBookConfig) {\n            var _logBookConfig_customisedLogBookComponents_nodes_filter_;\n            setSupernumeraryConfig((_logBookConfig_customisedLogBookComponents_nodes_filter_ = logBookConfig.customisedLogBookComponents.nodes.filter((config)=>config.componentClass === \"EventType_LogBookComponent\")[0]) === null || _logBookConfig_customisedLogBookComponents_nodes_filter_ === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes_filter_.customisedComponentFields.nodes.map((field)=>({\n                    title: field.fieldName,\n                    status: field.status\n                })));\n        }\n        if (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.supernumerary) {\n            loadSupernumerary(currentEvent.supernumerary.id);\n        }\n    }, [\n        logBookConfig,\n        currentEvent\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    !inLogbook && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.H4, {\n                        className: \"mt-6 uppercase\",\n                        children: \"Supernumerary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                        lineNumber: 605,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.P, {\n                        className: \"max-w-[40rem] leading-loose\",\n                        children: \"This section covers guest sign-ins and any policies they must read.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                        lineNumber: 607,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                lineNumber: 603,\n                columnNumber: 13\n            }, this),\n            (supernumerary === null || supernumerary === void 0 ? void 0 : supernumerary.id) && +(supernumerary === null || supernumerary === void 0 ? void 0 : supernumerary.id) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                onClick: handleAddGuest,\n                isLoading: loadingSupernumerary || createSupernumeraryLoading || updateSupernumeraryLoading,\n                iconLeft: _barrel_optimize_names_Check_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n                children: \"Add Guest\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                lineNumber: 613,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_18__.Label, {\n                                label: \"Title of supernumerary\",\n                                htmlFor: \"title\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_17__.Input, {\n                                    id: \"title\",\n                                    type: \"text\",\n                                    value: supernumerary === null || supernumerary === void 0 ? void 0 : supernumerary.title,\n                                    onChange: (e)=>{\n                                        setSupernumerary({\n                                            ...supernumerary,\n                                            title: e.target.value\n                                        });\n                                    },\n                                    placeholder: \"Enter Title\",\n                                    onBlur: ()=>{\n                                        setCloseOnSave(false);\n                                        handleSave();\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                                    lineNumber: 627,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                                lineNumber: 626,\n                                columnNumber: 21\n                            }, this),\n                            (formError === null || formError === void 0 ? void 0 : formError.element) === \"title\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-destructive mt-1\",\n                                children: formError === null || formError === void 0 ? void 0 : formError.message\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                                lineNumber: 645,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                        lineNumber: 625,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_18__.Label, {\n                                label: \"Number of Guests\",\n                                htmlFor: \"totalGuest\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_17__.Input, {\n                                    id: \"totalGuest\",\n                                    type: \"number\",\n                                    min: 1,\n                                    value: supernumerary === null || supernumerary === void 0 ? void 0 : supernumerary.totalGuest,\n                                    onChange: (e)=>{\n                                        setSupernumerary({\n                                            ...supernumerary,\n                                            totalGuest: e.target.value\n                                        });\n                                    },\n                                    onBlur: ()=>{\n                                        setCloseOnSave(false);\n                                        handleSave();\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                                    lineNumber: 652,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                                lineNumber: 651,\n                                columnNumber: 21\n                            }, this),\n                            (formError === null || formError === void 0 ? void 0 : formError.element) === \"totalGuest\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-destructive mt-1\",\n                                children: formError === null || formError === void 0 ? void 0 : formError.message\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                                lineNumber: 670,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                        lineNumber: 650,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                lineNumber: 624,\n                columnNumber: 13\n            }, this),\n            !supernumeraryConfig || supernumeraryConfig.find((c)=>c.title === \"Supernumerary_BriefingTime\" && c.status != \"Off\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_18__.Label, {\n                label: \"Time of Briefing\",\n                htmlFor: \"time\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    time: supernumerary === null || supernumerary === void 0 ? void 0 : supernumerary.briefingTime,\n                    handleTimeChange: handleBriefingTimeChange,\n                    timeID: \"time\",\n                    fieldName: \"Time\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                    lineNumber: 683,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                lineNumber: 682,\n                columnNumber: 17\n            }, this) : null,\n            !supernumeraryConfig || supernumeraryConfig.find((c)=>c.title === \"Supernumerary_GuestBriefing\" && c.status != \"Off\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_21__.CheckField, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_21__.CheckFieldTopContent, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                        lineNumber: 698,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_21__.CheckFieldContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_daily_check_field__WEBPACK_IMPORTED_MODULE_21__.DailyCheckField, {\n                            displayLabel: \"Guest Briefing Completed\",\n                            inputId: \"isBriefed\",\n                            handleNoChange: ()=>{\n                                setSupernumerary({\n                                    ...supernumerary,\n                                    isBriefed: false\n                                });\n                            },\n                            handleYesChange: ()=>{\n                                setSupernumerary({\n                                    ...supernumerary,\n                                    isBriefed: true\n                                });\n                            },\n                            hideCommentButton: true,\n                            defaultYesChecked: (supernumerary === null || supernumerary === void 0 ? void 0 : supernumerary.isBriefed) === true,\n                            defaultNoChecked: (supernumerary === null || supernumerary === void 0 ? void 0 : supernumerary.isBriefed) === false,\n                            disabled: locked\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                            lineNumber: 700,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                        lineNumber: 699,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                lineNumber: 697,\n                columnNumber: 17\n            }, this) : null,\n            (supernumerary === null || supernumerary === void 0 ? void 0 : (_supernumerary_guestList = supernumerary.guestList) === null || _supernumerary_guestList === void 0 ? void 0 : _supernumerary_guestList.nodes.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.Table, {\n                className: \"border border-outer-space-400 border-dashed rounded-lg pt-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                            className: \"hover:bg-transparent\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                className: \"text-left\",\n                                children: \"Guests\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                                lineNumber: 731,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                            lineNumber: 730,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                        lineNumber: 729,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableBody, {\n                        children: supernumerary.guestList.nodes.map((guest, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                onClick: ()=>handleFirstNameClick(guest),\n                                className: \"cursor-pointer\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                    className: \"text-left pl-2.5\",\n                                    children: \"\".concat((guest === null || guest === void 0 ? void 0 : guest.firstName) || \"\", \" \").concat((guest === null || guest === void 0 ? void 0 : guest.surname) || \"\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                                    lineNumber: 741,\n                                    columnNumber: 37\n                                }, this)\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                                lineNumber: 737,\n                                columnNumber: 33\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                        lineNumber: 734,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                lineNumber: 728,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        variant: \"secondary\",\n                        onClick: ()=>{\n                            closeModal();\n                        },\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                        lineNumber: 751,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        iconLeft: _barrel_optimize_names_Check_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n                        onClick: locked ? ()=>{} : ()=>{\n                            setCloseOnSave(true);\n                            handleSave();\n                        },\n                        disabled: loadingSupernumerary || createSupernumeraryLoading || updateSupernumeraryLoading,\n                        children: !currentEvent || +currentEvent.id === 0 ? \"Save\" : \"Update\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                        lineNumber: 758,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                lineNumber: 750,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_19__.AlertDialogNew, {\n                openDialog: openAddGuestDialog,\n                setOpenDialog: setOpenAddGuestDialog,\n                handleCreate: handleSaveGuest,\n                handleDestructiveAction: handleDeleteGuest,\n                destructiveActionText: \"Delete Guest\",\n                showDestructiveAction: currentGuest && (currentGuest === null || currentGuest === void 0 ? void 0 : currentGuest.id) > 0,\n                title: currentGuest && (currentGuest === null || currentGuest === void 0 ? void 0 : currentGuest.id) > 0 ? \"Update Guest\" : \"Add Guest\",\n                actionText: currentGuest && (currentGuest === null || currentGuest === void 0 ? void 0 : currentGuest.id) > 0 ? \"Update Guest\" : \"Save Guest\",\n                size: \"xl\",\n                position: \"center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-8\",\n                    children: !locked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    !supernumeraryConfig || supernumeraryConfig.find((c)=>c.title === \"Supernumerary_FirstName\" && c.status != \"Off\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_18__.Label, {\n                                        label: \"First Name\",\n                                        htmlFor: \"firstname\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_17__.Input, {\n                                            id: \"firstname\",\n                                            type: \"text\",\n                                            placeholder: \"First Name\",\n                                            name: \"firstname\",\n                                            value: currentGuest ? currentGuest === null || currentGuest === void 0 ? void 0 : currentGuest.firstName : \"\",\n                                            onChange: (e)=>{\n                                                setCurrentGuest({\n                                                    ...currentGuest,\n                                                    firstName: e.target.value\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                                            lineNumber: 810,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                                        lineNumber: 807,\n                                        columnNumber: 37\n                                    }, this) : null,\n                                    !supernumeraryConfig || supernumeraryConfig.find((c)=>c.title === \"Supernumerary_Surname\" && c.status != \"Off\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_18__.Label, {\n                                        label: \"Surname\",\n                                        htmlFor: \"surname\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_17__.Input, {\n                                            id: \"surname\",\n                                            type: \"text\",\n                                            placeholder: \"Surname\",\n                                            name: \"surname\",\n                                            value: currentGuest ? currentGuest === null || currentGuest === void 0 ? void 0 : currentGuest.surname : \"\",\n                                            onChange: (e)=>{\n                                                setCurrentGuest({\n                                                    ...currentGuest,\n                                                    surname: e.target.value\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                                            lineNumber: 836,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                                        lineNumber: 835,\n                                        columnNumber: 37\n                                    }, this) : null\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                                lineNumber: 800,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: !supernumeraryConfig || supernumeraryConfig.find((c)=>c.title === \"Supernumerary_Signature\" && c.status != \"Off\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_signature_pad__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    signature: currentGuest === null || currentGuest === void 0 ? void 0 : currentGuest.sectionSignature,\n                                    onSignatureChanged: onSignatureChanged\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                                    lineNumber: 863,\n                                    columnNumber: 37\n                                }, this) : null\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                                lineNumber: 856,\n                                columnNumber: 29\n                            }, this),\n                            !supernumeraryConfig || supernumeraryConfig.find((c)=>c.title === \"Supernumerary_Policies\" && c.status != \"Off\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"md:col-span-2 mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.P, {\n                                        className: \"mb-4\",\n                                        children: \"Please read and accept the following policies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                                        lineNumber: 878,\n                                        columnNumber: 37\n                                    }, this),\n                                    logBookConfig && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.Table, {\n                                        className: \"border border-outer-space-400 border-dashed rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                                    className: \"hover:bg-transparent\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                                            className: \"w-3/4 text-left pt-1\",\n                                                            children: \"Policy\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                                                            lineNumber: 886,\n                                                            columnNumber: 53\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                                            className: \"w-1/4 text-left pt-1\",\n                                                            children: \"Action\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                                                            lineNumber: 889,\n                                                            columnNumber: 53\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                                                    lineNumber: 885,\n                                                    columnNumber: 49\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                                                lineNumber: 884,\n                                                columnNumber: 45\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableBody, {\n                                                children: logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_policies = logBookConfig.policies) === null || _logBookConfig_policies === void 0 ? void 0 : _logBookConfig_policies.nodes.map((policy, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                className: \"text-left\",\n                                                                children: policy.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                                                                lineNumber: 901,\n                                                                columnNumber: 61\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"https://api.sealogs.com/assets/\" + policy.fileFilename,\n                                                                    className: \"font-medium hover:underline\",\n                                                                    target: \"_blank\",\n                                                                    children: \"View\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                                                                    lineNumber: 905,\n                                                                    columnNumber: 65\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                                                                lineNumber: 904,\n                                                                columnNumber: 61\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                                                        lineNumber: 900,\n                                                        columnNumber: 57\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                                                lineNumber: 894,\n                                                columnNumber: 45\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                                        lineNumber: 883,\n                                        columnNumber: 41\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                                lineNumber: 877,\n                                columnNumber: 33\n                            }, this) : null\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                    lineNumber: 797,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n                lineNumber: 778,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\supernumerary-event.tsx\",\n        lineNumber: 602,\n        columnNumber: 9\n    }, this);\n} // Using the imported SignaturePadComponent\n_s(SupernumeraryEvent, \"OmgJXcICZKHn7ultpoUFKxL+/jQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useSearchParams,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_23__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_23__.useLazyQuery\n    ];\n});\n_c = SupernumeraryEvent;\nvar _c;\n$RefreshReg$(_c, \"SupernumeraryEvent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/supernumerary-event.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/signature-pad.tsx":
/*!******************************************!*\
  !*** ./src/components/signature-pad.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_signature_canvas__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-signature-canvas */ \"(app-pages-browser)/./node_modules/.pnpm/react-signature-canvas@1.0._a29b96fcae92641cd34cf7cd162e41d1/node_modules/react-signature-canvas/build/index.js\");\n/* harmony import */ var react_signature_canvas__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_signature_canvas__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Eraser_Lock_Pen_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Eraser,Lock,Pen!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Eraser_Lock_Pen_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Eraser,Lock,Pen!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/pen.js\");\n/* harmony import */ var _barrel_optimize_names_Eraser_Lock_Pen_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Eraser,Lock,Pen!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/eraser.js\");\n/* harmony import */ var _ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst SignaturePad = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_1___default().memo(_c = _s((param)=>{\n    let { signature, member, memberId, title, onSignatureChanged, penColor = \"blue\", className, description, locked = false, ...canvasProps } = param;\n    _s();\n    const padRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    var _signature_signatureData;\n    const [dataUrl, setDataUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_signature_signatureData = signature === null || signature === void 0 ? void 0 : signature.signatureData) !== null && _signature_signatureData !== void 0 ? _signature_signatureData : null);\n    // 1. If we have a valid ID but no inline data, fetch it\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if ((signature === null || signature === void 0 ? void 0 : signature.id) && signature.id > 0 && !signature.signatureData) {\n            (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_7__.getSignatureUrl)(signature.id).then(setDataUrl).catch((err)=>console.error(\"Fetch sig URL failed:\", err));\n        }\n    }, [\n        signature === null || signature === void 0 ? void 0 : signature.id,\n        signature === null || signature === void 0 ? void 0 : signature.signatureData\n    ]);\n    // 2. Whenever dataUrl updates, load it into the pad\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const pad = padRef.current;\n        if (pad && dataUrl) {\n            pad.clear();\n            pad.fromDataURL(dataUrl);\n        }\n    }, [\n        dataUrl\n    ]);\n    const handleEnd = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const pad = padRef.current;\n        if (pad && !pad.isEmpty()) {\n            const url = pad.toDataURL();\n            onSignatureChanged(url, member, memberId);\n        }\n    }, [\n        onSignatureChanged,\n        member,\n        memberId\n    ]);\n    const handleClear = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const pad = padRef.current;\n        pad === null || pad === void 0 ? void 0 : pad.clear();\n        onSignatureChanged(\"\", member, memberId);\n        setDataUrl(null);\n    }, [\n        onSignatureChanged,\n        member,\n        memberId\n    ]);\n    const displayTitle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _ref;\n        return (_ref = title !== null && title !== void 0 ? title : member) !== null && _ref !== void 0 ? _ref : \"Signature\";\n    }, [\n        title,\n        member\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"relative w-full space-y-2\", className),\n        children: [\n            locked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                variant: \"destructive\",\n                className: \"absolute top-2 right-2 text-xs gap-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eraser_Lock_Pen_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"h-3 w-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 25\n                    }, undefined),\n                    \"Locked\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                lineNumber: 94,\n                columnNumber: 21\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui__WEBPACK_IMPORTED_MODULE_5__.P, {\n                    className: \"text-sm text-muted-foreground\",\n                    children: description\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 25\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                lineNumber: 102,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                htmlFor: \"sig-canvas\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react_signature_canvas__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    ...canvasProps,\n                    ref: padRef,\n                    penColor: penColor,\n                    canvasProps: {\n                        id: \"sig-canvas\",\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"border-2 border-dashed border-outer-space-400 rounded-lg h-48\", locked ? \"bg-muted/50\" : \"bg-white\"),\n                        style: {\n                            width: \"100%\",\n                            touchAction: locked ? \"none\" : \"auto\",\n                            cursor: locked ? \"not-allowed\" : \"crosshair\"\n                        }\n                    },\n                    onEnd: handleEnd\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                lineNumber: 111,\n                columnNumber: 17\n            }, undefined),\n            locked && // an overlay to prevent drawing when locked\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-white/60 rounded-lg pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                lineNumber: 134,\n                columnNumber: 21\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 text-xs text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eraser_Lock_Pen_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 25\n                            }, undefined),\n                            \"Draw your signature\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        size: \"sm\",\n                        iconLeft: _barrel_optimize_names_Eraser_Lock_Pen_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                        className: \"w-fit\",\n                        onClick: handleClear,\n                        disabled: locked,\n                        \"aria-label\": \"Clear signature\",\n                        children: \"Clear\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n                lineNumber: 137,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\signature-pad.tsx\",\n        lineNumber: 92,\n        columnNumber: 13\n    }, undefined);\n}, \"TJjvpSMRJ7e9XwkzR4OfCEZZTXM=\")), \"TJjvpSMRJ7e9XwkzR4OfCEZZTXM=\");\n_c1 = SignaturePad;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SignaturePad);\nvar _c, _c1;\n$RefreshReg$(_c, \"SignaturePad$React.memo\");\n$RefreshReg$(_c1, \"SignaturePad\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/signature-pad.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/check-field-label.tsx":
/*!*************************************************!*\
  !*** ./src/components/ui/check-field-label.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CheckFieldLabel: function() { return /* binding */ CheckFieldLabel; },\n/* harmony export */   checkFieldLabelVariants: function() { return /* binding */ checkFieldLabelVariants; },\n/* harmony export */   innerWrapperVariants: function() { return /* binding */ innerWrapperVariants; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(app-pages-browser)/./node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./src/components/ui/radio-group.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _typography__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* __next_internal_client_entry_do_not_use__ CheckFieldLabel,checkFieldLabelVariants,innerWrapperVariants auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst checkFieldLabelVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"cursor-pointer w-full px-4 rounded-e-lg will-change-transform will-change-color will-change-padding transform-gpu hover:transition-all hover:ease-out hover:duration-300\", {\n    variants: {\n        variant: {\n            default: \"hover:bg-fire-bush-100 hover:border-yellow-vivid-600\",\n            primary: \"hover:bg-light-blue-vivid-50 hover:border-light-blue-vivid-600\",\n            secondary: \"hover:bg-background hover:border-outer-space-400\",\n            success: \"hover:bg-bright-turquoise-100 hover:border-teal-600\",\n            destructive: \"hover:bg-red-vivid-50 hover:border-red-vivid-600\",\n            warning: \"hover:bg-fire-bush-100 hover:border-yellow-vivid-600\",\n            pink: \"hover:bg-pink-vivid-50 hover:border-pink-vivid-600\",\n            outline: \"hover:bg-background hover:border-outer-space-400\",\n            \"light-blue\": \"hover:bg-light-blue-vivid-50 hover:border-light-blue-vivid-600\"\n        },\n        size: {\n            default: \"py-[10.5px]\",\n            sm: \"py-2\",\n            lg: \"py-6\"\n        },\n        disabled: {\n            true: \"hover:bg-transparent hover:border-border\",\n            false: \"\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\",\n        disabled: false\n    }\n});\nconst innerWrapperVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"relative inset-x-0 w-12 rounded-s-lg justify-center flex items-center\", {\n    variants: {\n        variant: {\n            default: \"bg-light-blue-vivid-50 border-light-blue-vivid-600\",\n            primary: \"bg-light-blue-vivid-50 border-light-blue-vivid-600\",\n            secondary: \"bg-background border-outer-space-400\",\n            success: \"bg-bright-turquoise-100 border-teal-600\",\n            destructive: \"bg-red-vivid-50 border-red-vivid-600\",\n            warning: \"bg-fire-bush-100 border-yellow-vivid-600\",\n            pink: \"bg-pink-vivid-50 border-pink-vivid-600\",\n            outline: \"bg-background border-outer-space-400\",\n            \"light-blue\": \"bg-light-blue-vivid-50 border-light-blue-vivid-600\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\n/**\r\n * CheckFieldLabel component that combines a checkbox or radio button with a label\r\n * in a styled container. It can be used for checkboxes or radio buttons.\r\n */ const CheckFieldLabel = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = _s((param, ref)=>{\n    let { type = \"checkbox\", id, checked, onCheckedChange, disabled, value, name, label, children, className, variant, size, radioGroupValue, isRadioStyle = true, rightContent, leftContent, onClick, ...props } = param;\n    _s();\n    // Generate a unique ID if none is provided\n    const uniqueId = react__WEBPACK_IMPORTED_MODULE_1__.useId();\n    const inputId = id || \"\".concat(type, \"-\").concat(uniqueId);\n    // Determine if radio button is checked based on radioGroupValue\n    const isRadioChecked = type === \"radio\" ? radioGroupValue === value : checked;\n    // Handle click on the container to toggle checkbox or select radio\n    // and call the external onClick handler if provided\n    const handleContainerClick = (e)=>{\n        if (disabled) return;\n        // First handle the checkbox/radio toggle\n        if (type === \"checkbox\" && onCheckedChange) {\n            onCheckedChange(!checked);\n        } else if (type === \"radio\" && onCheckedChange && !isRadioChecked) {\n            onCheckedChange(true);\n        }\n        // Then call the external onClick handler if provided\n        if (onClick) {\n            onClick(e);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex rounded-lg relative group justify-evenly border border-border shadow-sm cursor-pointer\", disabled && \"opacity-50 cursor-not-allowed\", className),\n        onClick: handleContainerClick,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(innerWrapperVariants({\n                    variant\n                })),\n                children: type === \"checkbox\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_3__.Checkbox, {\n                    id: inputId,\n                    isRadioStyle: isRadioStyle,\n                    checked: checked,\n                    onCheckedChange: (checked)=>{\n                        // We're making the checkbox non-interactive\n                        // The parent container will handle the click\n                        if (typeof checked === \"boolean\" && onCheckedChange) {\n                            onCheckedChange(checked);\n                        }\n                    },\n                    disabled: disabled,\n                    name: name,\n                    variant: variant,\n                    size: \"lg\",\n                    className: \"pointer-events-none\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\check-field-label.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 25\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_4__.RadioGroupItem, {\n                    id: inputId,\n                    value: value || \"\",\n                    disabled: disabled,\n                    variant: variant,\n                    size: \"md\",\n                    checked: isRadioChecked,\n                    className: \"pointer-events-none\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\check-field-label.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 25\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\check-field-label.tsx\",\n                lineNumber: 214,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex items-center\", checkFieldLabelVariants({\n                    variant: \"secondary\",\n                    size,\n                    disabled\n                })),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex flex-1 items-center\", {\n                        \"gap-2\": leftContent || rightContent\n                    }),\n                    children: [\n                        leftContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center\",\n                            children: leftContent\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\check-field-label.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 29\n                        }, undefined),\n                        children ? children : label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_typography__WEBPACK_IMPORTED_MODULE_6__.P, {\n                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"text-wrap text-foreground text-base\"),\n                            children: label\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\check-field-label.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 35\n                        }, undefined),\n                        rightContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center\",\n                            children: rightContent\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\check-field-label.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\check-field-label.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\check-field-label.tsx\",\n                lineNumber: 248,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\check-field-label.tsx\",\n        lineNumber: 205,\n        columnNumber: 13\n    }, undefined);\n}, \"j7NPILheLIfrWAvm8S/GM4Sml/8=\")), \"j7NPILheLIfrWAvm8S/GM4Sml/8=\");\n_c1 = CheckFieldLabel;\nCheckFieldLabel.displayName = \"CheckFieldLabel\";\n\nvar _c, _c1;\n$RefreshReg$(_c, \"CheckFieldLabel$React.forwardRef\");\n$RefreshReg$(_c1, \"CheckFieldLabel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL2NoZWNrLWZpZWxkLWxhYmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBRThCO0FBQ21DO0FBQ2Q7QUFDUztBQUN4QjtBQUNKO0FBRWhDLE1BQU1NLDBCQUEwQkwsNkRBQUdBLENBQy9CLDRLQUNBO0lBQ0lNLFVBQVU7UUFDTkMsU0FBUztZQUNMQyxTQUFTO1lBQ1RDLFNBQ0k7WUFDSkMsV0FBVztZQUNYQyxTQUFTO1lBQ1RDLGFBQWE7WUFDYkMsU0FBUztZQUNUQyxNQUFNO1lBQ05DLFNBQVM7WUFDVCxjQUNJO1FBQ1I7UUFDQUMsTUFBTTtZQUNGUixTQUFTO1lBQ1RTLElBQUk7WUFDSkMsSUFBSTtRQUNSO1FBQ0FDLFVBQVU7WUFDTkMsTUFBTTtZQUNOQyxPQUFPO1FBQ1g7SUFDSjtJQUNBQyxpQkFBaUI7UUFDYmYsU0FBUztRQUNUUyxNQUFNO1FBQ05HLFVBQVU7SUFDZDtBQUNKO0FBR0osTUFBTUksdUJBQXVCdkIsNkRBQUdBLENBQzVCLHlFQUNBO0lBQ0lNLFVBQVU7UUFDTkMsU0FBUztZQUNMQyxTQUFTO1lBQ1RDLFNBQVM7WUFDVEMsV0FBVztZQUNYQyxTQUFTO1lBQ1RDLGFBQWE7WUFDYkMsU0FBUztZQUNUQyxNQUFNO1lBQ05DLFNBQVM7WUFDVCxjQUNJO1FBQ1I7SUFDSjtJQUNBTyxpQkFBaUI7UUFDYmYsU0FBUztJQUNiO0FBQ0o7QUFxRko7OztDQUdDLEdBQ0QsTUFBTWlCLGdDQUFrQnpCLEdBQUFBLDZDQUFnQixTQUNwQyxRQXFCSTJCO1FBcEJBLEVBQ0lDLE9BQU8sVUFBVSxFQUNqQkMsRUFBRSxFQUNGQyxPQUFPLEVBQ1BDLGVBQWUsRUFDZlgsUUFBUSxFQUNSWSxLQUFLLEVBQ0xDLElBQUksRUFDSkMsS0FBSyxFQUNMQyxRQUFRLEVBQ1JDLFNBQVMsRUFDVDVCLE9BQU8sRUFDUFMsSUFBSSxFQUNKb0IsZUFBZSxFQUNmQyxlQUFlLElBQUksRUFDbkJDLFlBQVksRUFDWkMsV0FBVyxFQUNYQyxPQUFPLEVBQ1AsR0FBR0MsT0FDTjs7SUFHRCwyQ0FBMkM7SUFDM0MsTUFBTUMsV0FBVzNDLHdDQUFXO0lBQzVCLE1BQU02QyxVQUFVaEIsTUFBTSxHQUFXYyxPQUFSZixNQUFLLEtBQVksT0FBVGU7SUFFakMsZ0VBQWdFO0lBQ2hFLE1BQU1HLGlCQUNGbEIsU0FBUyxVQUFVUyxvQkFBb0JMLFFBQVFGO0lBRW5ELG1FQUFtRTtJQUNuRSxvREFBb0Q7SUFDcEQsTUFBTWlCLHVCQUF1QixDQUFDQztRQUMxQixJQUFJNUIsVUFBVTtRQUVkLHlDQUF5QztRQUN6QyxJQUFJUSxTQUFTLGNBQWNHLGlCQUFpQjtZQUN4Q0EsZ0JBQWdCLENBQUNEO1FBQ3JCLE9BQU8sSUFBSUYsU0FBUyxXQUFXRyxtQkFBbUIsQ0FBQ2UsZ0JBQWdCO1lBQy9EZixnQkFBZ0I7UUFDcEI7UUFFQSxxREFBcUQ7UUFDckQsSUFBSVUsU0FBUztZQUNUQSxRQUFRTztRQUNaO0lBQ0o7SUFFQSxxQkFDSSw4REFBQ0M7UUFDR3RCLEtBQUtBO1FBQ0xTLFdBQVdoQyxrREFBRUEsQ0FDVCwrRkFDQWdCLFlBQVksaUNBQ1pnQjtRQUVKSyxTQUFTTTtRQUNSLEdBQUdMLEtBQUs7OzBCQUNULDhEQUFDTztnQkFBSWIsV0FBV2hDLGtEQUFFQSxDQUFDb0IscUJBQXFCO29CQUFFaEI7Z0JBQVE7MEJBQzdDb0IsU0FBUywyQkFDTiw4REFBQzFCLDZEQUFRQTtvQkFDTDJCLElBQUlnQjtvQkFDSlAsY0FBY0E7b0JBQ2RSLFNBQVNBO29CQUNUQyxpQkFBaUIsQ0FBQ0Q7d0JBQ2QsNENBQTRDO3dCQUM1Qyw2Q0FBNkM7d0JBQzdDLElBQ0ksT0FBT0EsWUFBWSxhQUNuQkMsaUJBQ0Y7NEJBQ0VBLGdCQUFnQkQ7d0JBQ3BCO29CQUNKO29CQUNBVixVQUFVQTtvQkFDVmEsTUFBTUE7b0JBQ056QixTQUFTQTtvQkFDVFMsTUFBSztvQkFDTG1CLFdBQVU7Ozs7OzhDQUdkLDhEQUFDakMsc0VBQWNBO29CQUNYMEIsSUFBSWdCO29CQUNKYixPQUFPQSxTQUFTO29CQUNoQlosVUFBVUE7b0JBQ1ZaLFNBQVNBO29CQUNUUyxNQUFLO29CQUNMYSxTQUFTZ0I7b0JBQ1RWLFdBQVU7Ozs7Ozs7Ozs7OzBCQUl0Qiw4REFBQ2E7Z0JBQ0diLFdBQVdoQyxrREFBRUEsQ0FDVCxxQkFDQUUsd0JBQXdCO29CQUNwQkUsU0FBUztvQkFDVFM7b0JBQ0FHO2dCQUNKOzBCQUVKLDRFQUFDNkI7b0JBQ0diLFdBQVdoQyxrREFBRUEsQ0FBQyw0QkFBNEI7d0JBQ3RDLFNBQVNvQyxlQUFlRDtvQkFDNUI7O3dCQUNDQyw2QkFDRyw4REFBQ1M7NEJBQUliLFdBQVU7c0NBQ1ZJOzs7Ozs7d0JBR1JMLFdBQ0tBLFdBQ0FELHVCQUNJLDhEQUFDN0IsMENBQUNBOzRCQUNFK0IsV0FBV2hDLGtEQUFFQSxDQUNUO3NDQUVIOEI7Ozs7Ozt3QkFHZEssOEJBQ0csOERBQUNVOzRCQUFJYixXQUFVO3NDQUNWRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPN0I7O0FBR0pkLGdCQUFnQnlCLFdBQVcsR0FBRztBQUUyQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy91aS9jaGVjay1maWVsZC1sYWJlbC50c3g/NzE3MiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcclxuXHJcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0J1xyXG5pbXBvcnQgeyBjdmEsIHR5cGUgVmFyaWFudFByb3BzIH0gZnJvbSAnY2xhc3MtdmFyaWFuY2UtYXV0aG9yaXR5J1xyXG5pbXBvcnQgeyBDaGVja2JveCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9jaGVja2JveCdcclxuaW1wb3J0IHsgUmFkaW9Hcm91cEl0ZW0gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvcmFkaW8tZ3JvdXAnXHJcbmltcG9ydCB7IGNuIH0gZnJvbSAnQC9hcHAvbGliL3V0aWxzJ1xyXG5pbXBvcnQgeyBQIH0gZnJvbSAnLi90eXBvZ3JhcGh5J1xyXG5cclxuY29uc3QgY2hlY2tGaWVsZExhYmVsVmFyaWFudHMgPSBjdmEoXHJcbiAgICAnY3Vyc29yLXBvaW50ZXIgdy1mdWxsIHB4LTQgcm91bmRlZC1lLWxnIHdpbGwtY2hhbmdlLXRyYW5zZm9ybSB3aWxsLWNoYW5nZS1jb2xvciB3aWxsLWNoYW5nZS1wYWRkaW5nIHRyYW5zZm9ybS1ncHUgaG92ZXI6dHJhbnNpdGlvbi1hbGwgaG92ZXI6ZWFzZS1vdXQgaG92ZXI6ZHVyYXRpb24tMzAwJyxcclxuICAgIHtcclxuICAgICAgICB2YXJpYW50czoge1xyXG4gICAgICAgICAgICB2YXJpYW50OiB7XHJcbiAgICAgICAgICAgICAgICBkZWZhdWx0OiAnaG92ZXI6YmctZmlyZS1idXNoLTEwMCBob3Zlcjpib3JkZXIteWVsbG93LXZpdmlkLTYwMCcsXHJcbiAgICAgICAgICAgICAgICBwcmltYXJ5OlxyXG4gICAgICAgICAgICAgICAgICAgICdob3ZlcjpiZy1saWdodC1ibHVlLXZpdmlkLTUwIGhvdmVyOmJvcmRlci1saWdodC1ibHVlLXZpdmlkLTYwMCcsXHJcbiAgICAgICAgICAgICAgICBzZWNvbmRhcnk6ICdob3ZlcjpiZy1iYWNrZ3JvdW5kIGhvdmVyOmJvcmRlci1vdXRlci1zcGFjZS00MDAnLFxyXG4gICAgICAgICAgICAgICAgc3VjY2VzczogJ2hvdmVyOmJnLWJyaWdodC10dXJxdW9pc2UtMTAwIGhvdmVyOmJvcmRlci10ZWFsLTYwMCcsXHJcbiAgICAgICAgICAgICAgICBkZXN0cnVjdGl2ZTogJ2hvdmVyOmJnLXJlZC12aXZpZC01MCBob3Zlcjpib3JkZXItcmVkLXZpdmlkLTYwMCcsXHJcbiAgICAgICAgICAgICAgICB3YXJuaW5nOiAnaG92ZXI6YmctZmlyZS1idXNoLTEwMCBob3Zlcjpib3JkZXIteWVsbG93LXZpdmlkLTYwMCcsXHJcbiAgICAgICAgICAgICAgICBwaW5rOiAnaG92ZXI6YmctcGluay12aXZpZC01MCBob3Zlcjpib3JkZXItcGluay12aXZpZC02MDAnLFxyXG4gICAgICAgICAgICAgICAgb3V0bGluZTogJ2hvdmVyOmJnLWJhY2tncm91bmQgaG92ZXI6Ym9yZGVyLW91dGVyLXNwYWNlLTQwMCcsXHJcbiAgICAgICAgICAgICAgICAnbGlnaHQtYmx1ZSc6XHJcbiAgICAgICAgICAgICAgICAgICAgJ2hvdmVyOmJnLWxpZ2h0LWJsdWUtdml2aWQtNTAgaG92ZXI6Ym9yZGVyLWxpZ2h0LWJsdWUtdml2aWQtNjAwJyxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgc2l6ZToge1xyXG4gICAgICAgICAgICAgICAgZGVmYXVsdDogJ3B5LVsxMC41cHhdJyxcclxuICAgICAgICAgICAgICAgIHNtOiAncHktMicsXHJcbiAgICAgICAgICAgICAgICBsZzogJ3B5LTYnLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBkaXNhYmxlZDoge1xyXG4gICAgICAgICAgICAgICAgdHJ1ZTogJ2hvdmVyOmJnLXRyYW5zcGFyZW50IGhvdmVyOmJvcmRlci1ib3JkZXInLFxyXG4gICAgICAgICAgICAgICAgZmFsc2U6ICcnLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgZGVmYXVsdFZhcmlhbnRzOiB7XHJcbiAgICAgICAgICAgIHZhcmlhbnQ6ICdkZWZhdWx0JyxcclxuICAgICAgICAgICAgc2l6ZTogJ2RlZmF1bHQnLFxyXG4gICAgICAgICAgICBkaXNhYmxlZDogZmFsc2UsXHJcbiAgICAgICAgfSxcclxuICAgIH0sXHJcbilcclxuXHJcbmNvbnN0IGlubmVyV3JhcHBlclZhcmlhbnRzID0gY3ZhKFxyXG4gICAgJ3JlbGF0aXZlIGluc2V0LXgtMCB3LTEyIHJvdW5kZWQtcy1sZyBqdXN0aWZ5LWNlbnRlciBmbGV4IGl0ZW1zLWNlbnRlcicsXHJcbiAgICB7XHJcbiAgICAgICAgdmFyaWFudHM6IHtcclxuICAgICAgICAgICAgdmFyaWFudDoge1xyXG4gICAgICAgICAgICAgICAgZGVmYXVsdDogJ2JnLWxpZ2h0LWJsdWUtdml2aWQtNTAgYm9yZGVyLWxpZ2h0LWJsdWUtdml2aWQtNjAwJyxcclxuICAgICAgICAgICAgICAgIHByaW1hcnk6ICdiZy1saWdodC1ibHVlLXZpdmlkLTUwIGJvcmRlci1saWdodC1ibHVlLXZpdmlkLTYwMCcsXHJcbiAgICAgICAgICAgICAgICBzZWNvbmRhcnk6ICdiZy1iYWNrZ3JvdW5kIGJvcmRlci1vdXRlci1zcGFjZS00MDAnLFxyXG4gICAgICAgICAgICAgICAgc3VjY2VzczogJ2JnLWJyaWdodC10dXJxdW9pc2UtMTAwIGJvcmRlci10ZWFsLTYwMCcsXHJcbiAgICAgICAgICAgICAgICBkZXN0cnVjdGl2ZTogJ2JnLXJlZC12aXZpZC01MCBib3JkZXItcmVkLXZpdmlkLTYwMCcsXHJcbiAgICAgICAgICAgICAgICB3YXJuaW5nOiAnYmctZmlyZS1idXNoLTEwMCBib3JkZXIteWVsbG93LXZpdmlkLTYwMCcsXHJcbiAgICAgICAgICAgICAgICBwaW5rOiAnYmctcGluay12aXZpZC01MCBib3JkZXItcGluay12aXZpZC02MDAnLFxyXG4gICAgICAgICAgICAgICAgb3V0bGluZTogJ2JnLWJhY2tncm91bmQgYm9yZGVyLW91dGVyLXNwYWNlLTQwMCcsXHJcbiAgICAgICAgICAgICAgICAnbGlnaHQtYmx1ZSc6XHJcbiAgICAgICAgICAgICAgICAgICAgJ2JnLWxpZ2h0LWJsdWUtdml2aWQtNTAgYm9yZGVyLWxpZ2h0LWJsdWUtdml2aWQtNjAwJyxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgICAgIGRlZmF1bHRWYXJpYW50czoge1xyXG4gICAgICAgICAgICB2YXJpYW50OiAnZGVmYXVsdCcsXHJcbiAgICAgICAgfSxcclxuICAgIH0sXHJcbilcclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgQ2hlY2tGaWVsZExhYmVsUHJvcHNcclxuICAgIGV4dGVuZHMgT21pdDxSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD4sICdvbkNsaWNrJz4sXHJcbiAgICAgICAgVmFyaWFudFByb3BzPHR5cGVvZiBjaGVja0ZpZWxkTGFiZWxWYXJpYW50cz4ge1xyXG4gICAgLyoqXHJcbiAgICAgKiBUaGUgdHlwZSBvZiBpbnB1dCB0byB1c2VcclxuICAgICAqIEBkZWZhdWx0IFwiY2hlY2tib3hcIlxyXG4gICAgICovXHJcbiAgICB0eXBlPzogJ2NoZWNrYm94JyB8ICdyYWRpbydcclxuICAgIC8qKlxyXG4gICAgICogVGhlIElEIG9mIHRoZSBpbnB1dFxyXG4gICAgICovXHJcbiAgICBpZD86IHN0cmluZ1xyXG4gICAgLyoqXHJcbiAgICAgKiBXaGV0aGVyIHRoZSBpbnB1dCBpcyBjaGVja2VkXHJcbiAgICAgKi9cclxuICAgIGNoZWNrZWQ/OiBib29sZWFuXHJcbiAgICAvKipcclxuICAgICAqIENhbGxiYWNrIHdoZW4gdGhlIGNoZWNrZWQgc3RhdGUgY2hhbmdlc1xyXG4gICAgICovXHJcbiAgICBvbkNoZWNrZWRDaGFuZ2U/OiAoY2hlY2tlZDogYm9vbGVhbikgPT4gdm9pZFxyXG4gICAgLyoqXHJcbiAgICAgKiBXaGV0aGVyIHRoZSBpbnB1dCBpcyBkaXNhYmxlZFxyXG4gICAgICovXHJcbiAgICBkaXNhYmxlZD86IGJvb2xlYW5cclxuICAgIC8qKlxyXG4gICAgICogVGhlIHZhbHVlIG9mIHRoZSBpbnB1dCAoZm9yIHJhZGlvIGJ1dHRvbnMpXHJcbiAgICAgKi9cclxuICAgIHZhbHVlPzogc3RyaW5nXHJcbiAgICAvKipcclxuICAgICAqIFRoZSBuYW1lIG9mIHRoZSBpbnB1dCAoZm9yIHJhZGlvIGJ1dHRvbnMpXHJcbiAgICAgKi9cclxuICAgIG5hbWU/OiBzdHJpbmdcclxuICAgIC8qKlxyXG4gICAgICogVGhlIGxhYmVsIHRleHRcclxuICAgICAqL1xyXG4gICAgbGFiZWw/OiBzdHJpbmcgfCBSZWFjdC5SZWFjdE5vZGVcclxuICAgIC8qKlxyXG4gICAgICogQ2hpbGRyZW4gdG8gcmVuZGVyIGluc2lkZSB0aGUgbGFiZWwgYXJlYSAob3ZlcnJpZGVzIGxhYmVsIGlmIHByb3ZpZGVkKVxyXG4gICAgICovXHJcbiAgICBjaGlsZHJlbj86IFJlYWN0LlJlYWN0Tm9kZVxyXG4gICAgLyoqXHJcbiAgICAgKiBBZGRpdGlvbmFsIGNsYXNzTmFtZSBmb3IgdGhlIGxhYmVsXHJcbiAgICAgKi9cclxuICAgIGNsYXNzTmFtZT86IHN0cmluZ1xyXG4gICAgLyoqXHJcbiAgICAgKiBUaGUgcmFkaW8gZ3JvdXAgdmFsdWUgKGZvciByYWRpbyBidXR0b25zKVxyXG4gICAgICovXHJcbiAgICByYWRpb0dyb3VwVmFsdWU/OiBzdHJpbmdcclxuICAgIC8qKlxyXG4gICAgICogQ29udGVudCB0byBiZSBkaXNwbGF5ZWQgdG8gdGhlIHJpZ2h0IG9mIHRoZSBsYWJlbCAoZS5nLiwgaW5mbyBpY29uKVxyXG4gICAgICovXHJcbiAgICByaWdodENvbnRlbnQ/OiBSZWFjdC5SZWFjdE5vZGVcclxuICAgIC8qKlxyXG4gICAgICogQ29udGVudCB0byBiZSBkaXNwbGF5ZWQgdG8gdGhlIGxlZnQgb2YgdGhlIGxhYmVsIChlLmcuLCBpY29uKVxyXG4gICAgICovXHJcbiAgICBsZWZ0Q29udGVudD86IFJlYWN0LlJlYWN0Tm9kZVxyXG4gICAgLyoqXHJcbiAgICAgKiBUaGUgdmFyaWFudCBvZiB0aGUgY2hlY2tib3ggb3IgcmFkaW8gYnV0dG9uXHJcbiAgICAgKi9cclxuICAgIHZhcmlhbnQ/OlxyXG4gICAgICAgIHwgJ2RlZmF1bHQnXHJcbiAgICAgICAgfCAnc2Vjb25kYXJ5J1xyXG4gICAgICAgIHwgJ3N1Y2Nlc3MnXHJcbiAgICAgICAgfCAnZGVzdHJ1Y3RpdmUnXHJcbiAgICAgICAgfCAncGluaydcclxuICAgICAgICB8ICdvdXRsaW5lJ1xyXG4gICAgICAgIHwgJ2xpZ2h0LWJsdWUnXHJcbiAgICAgICAgfCAnd2FybmluZydcclxuICAgICAgICB8IG51bGxcclxuICAgICAgICB8IHVuZGVmaW5lZFxyXG4gICAgLyoqXHJcbiAgICAgKiBXaGV0aGVyIHRoZSBjaGVja2JveCBzaG91bGQgdmlzdWFsbHkgYXBwZWFyIGFzIGEgcmFkaW8gYnV0dG9uXHJcbiAgICAgKiB3aGlsZSBtYWludGFpbmluZyBjaGVja2JveCBmdW5jdGlvbmFsaXR5IChtdWx0aXBsZSBzZWxlY3Rpb24pXHJcbiAgICAgKi9cclxuICAgIGlzUmFkaW9TdHlsZT86IGJvb2xlYW5cclxuICAgIC8qKlxyXG4gICAgICogQWRkaXRpb25hbCBjbGljayBoYW5kbGVyIGZvciB0aGUgY29udGFpbmVyXHJcbiAgICAgKiBUaGlzIHdpbGwgYmUgY2FsbGVkIGFmdGVyIHRoZSBjaGVja2JveC9yYWRpbyBpcyB0b2dnbGVkXHJcbiAgICAgKi9cclxuICAgIG9uQ2xpY2s/OiAoZXZlbnQ6IFJlYWN0Lk1vdXNlRXZlbnQ8SFRNTERpdkVsZW1lbnQ+KSA9PiB2b2lkXHJcbn1cclxuXHJcbi8qKlxyXG4gKiBDaGVja0ZpZWxkTGFiZWwgY29tcG9uZW50IHRoYXQgY29tYmluZXMgYSBjaGVja2JveCBvciByYWRpbyBidXR0b24gd2l0aCBhIGxhYmVsXHJcbiAqIGluIGEgc3R5bGVkIGNvbnRhaW5lci4gSXQgY2FuIGJlIHVzZWQgZm9yIGNoZWNrYm94ZXMgb3IgcmFkaW8gYnV0dG9ucy5cclxuICovXHJcbmNvbnN0IENoZWNrRmllbGRMYWJlbCA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTERpdkVsZW1lbnQsIENoZWNrRmllbGRMYWJlbFByb3BzPihcclxuICAgIChcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIHR5cGUgPSAnY2hlY2tib3gnLFxyXG4gICAgICAgICAgICBpZCxcclxuICAgICAgICAgICAgY2hlY2tlZCxcclxuICAgICAgICAgICAgb25DaGVja2VkQ2hhbmdlLFxyXG4gICAgICAgICAgICBkaXNhYmxlZCxcclxuICAgICAgICAgICAgdmFsdWUsXHJcbiAgICAgICAgICAgIG5hbWUsXHJcbiAgICAgICAgICAgIGxhYmVsLFxyXG4gICAgICAgICAgICBjaGlsZHJlbixcclxuICAgICAgICAgICAgY2xhc3NOYW1lLFxyXG4gICAgICAgICAgICB2YXJpYW50LFxyXG4gICAgICAgICAgICBzaXplLFxyXG4gICAgICAgICAgICByYWRpb0dyb3VwVmFsdWUsXHJcbiAgICAgICAgICAgIGlzUmFkaW9TdHlsZSA9IHRydWUsXHJcbiAgICAgICAgICAgIHJpZ2h0Q29udGVudCxcclxuICAgICAgICAgICAgbGVmdENvbnRlbnQsXHJcbiAgICAgICAgICAgIG9uQ2xpY2ssXHJcbiAgICAgICAgICAgIC4uLnByb3BzXHJcbiAgICAgICAgfSxcclxuICAgICAgICByZWYsXHJcbiAgICApID0+IHtcclxuICAgICAgICAvLyBHZW5lcmF0ZSBhIHVuaXF1ZSBJRCBpZiBub25lIGlzIHByb3ZpZGVkXHJcbiAgICAgICAgY29uc3QgdW5pcXVlSWQgPSBSZWFjdC51c2VJZCgpXHJcbiAgICAgICAgY29uc3QgaW5wdXRJZCA9IGlkIHx8IGAke3R5cGV9LSR7dW5pcXVlSWR9YFxyXG5cclxuICAgICAgICAvLyBEZXRlcm1pbmUgaWYgcmFkaW8gYnV0dG9uIGlzIGNoZWNrZWQgYmFzZWQgb24gcmFkaW9Hcm91cFZhbHVlXHJcbiAgICAgICAgY29uc3QgaXNSYWRpb0NoZWNrZWQgPVxyXG4gICAgICAgICAgICB0eXBlID09PSAncmFkaW8nID8gcmFkaW9Hcm91cFZhbHVlID09PSB2YWx1ZSA6IGNoZWNrZWRcclxuXHJcbiAgICAgICAgLy8gSGFuZGxlIGNsaWNrIG9uIHRoZSBjb250YWluZXIgdG8gdG9nZ2xlIGNoZWNrYm94IG9yIHNlbGVjdCByYWRpb1xyXG4gICAgICAgIC8vIGFuZCBjYWxsIHRoZSBleHRlcm5hbCBvbkNsaWNrIGhhbmRsZXIgaWYgcHJvdmlkZWRcclxuICAgICAgICBjb25zdCBoYW5kbGVDb250YWluZXJDbGljayA9IChlOiBSZWFjdC5Nb3VzZUV2ZW50PEhUTUxEaXZFbGVtZW50PikgPT4ge1xyXG4gICAgICAgICAgICBpZiAoZGlzYWJsZWQpIHJldHVyblxyXG5cclxuICAgICAgICAgICAgLy8gRmlyc3QgaGFuZGxlIHRoZSBjaGVja2JveC9yYWRpbyB0b2dnbGVcclxuICAgICAgICAgICAgaWYgKHR5cGUgPT09ICdjaGVja2JveCcgJiYgb25DaGVja2VkQ2hhbmdlKSB7XHJcbiAgICAgICAgICAgICAgICBvbkNoZWNrZWRDaGFuZ2UoIWNoZWNrZWQpXHJcbiAgICAgICAgICAgIH0gZWxzZSBpZiAodHlwZSA9PT0gJ3JhZGlvJyAmJiBvbkNoZWNrZWRDaGFuZ2UgJiYgIWlzUmFkaW9DaGVja2VkKSB7XHJcbiAgICAgICAgICAgICAgICBvbkNoZWNrZWRDaGFuZ2UodHJ1ZSlcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLy8gVGhlbiBjYWxsIHRoZSBleHRlcm5hbCBvbkNsaWNrIGhhbmRsZXIgaWYgcHJvdmlkZWRcclxuICAgICAgICAgICAgaWYgKG9uQ2xpY2spIHtcclxuICAgICAgICAgICAgICAgIG9uQ2xpY2soZSlcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgcmVmPXtyZWZ9XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxyXG4gICAgICAgICAgICAgICAgICAgICdmbGV4IHJvdW5kZWQtbGcgcmVsYXRpdmUgZ3JvdXAganVzdGlmeS1ldmVubHkgYm9yZGVyIGJvcmRlci1ib3JkZXIgc2hhZG93LXNtIGN1cnNvci1wb2ludGVyJyxcclxuICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZCAmJiAnb3BhY2l0eS01MCBjdXJzb3Itbm90LWFsbG93ZWQnLFxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZSxcclxuICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVDb250YWluZXJDbGlja31cclxuICAgICAgICAgICAgICAgIHsuLi5wcm9wc30+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17Y24oaW5uZXJXcmFwcGVyVmFyaWFudHMoeyB2YXJpYW50IH0pKX0+XHJcbiAgICAgICAgICAgICAgICAgICAge3R5cGUgPT09ICdjaGVja2JveCcgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxDaGVja2JveFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9e2lucHV0SWR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc1JhZGlvU3R5bGU9e2lzUmFkaW9TdHlsZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2NoZWNrZWR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoZWNrZWRDaGFuZ2U9eyhjaGVja2VkKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gV2UncmUgbWFraW5nIHRoZSBjaGVja2JveCBub24taW50ZXJhY3RpdmVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBUaGUgcGFyZW50IGNvbnRhaW5lciB3aWxsIGhhbmRsZSB0aGUgY2xpY2tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGVvZiBjaGVja2VkID09PSAnYm9vbGVhbicgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGVja2VkQ2hhbmdlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hlY2tlZENoYW5nZShjaGVja2VkKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17ZGlzYWJsZWR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lPXtuYW1lfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD17dmFyaWFudH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJsZ1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwb2ludGVyLWV2ZW50cy1ub25lXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8UmFkaW9Hcm91cEl0ZW1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkPXtpbnB1dElkfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3ZhbHVlIHx8ICcnfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2Rpc2FibGVkfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD17dmFyaWFudH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJtZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtpc1JhZGlvQ2hlY2tlZH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInBvaW50ZXItZXZlbnRzLW5vbmVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAnZmxleCBpdGVtcy1jZW50ZXInLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjaGVja0ZpZWxkTGFiZWxWYXJpYW50cyh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50OiAnc2Vjb25kYXJ5JyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSksXHJcbiAgICAgICAgICAgICAgICAgICAgKX0+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKCdmbGV4IGZsZXgtMSBpdGVtcy1jZW50ZXInLCB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAnZ2FwLTInOiBsZWZ0Q29udGVudCB8fCByaWdodENvbnRlbnQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAge2xlZnRDb250ZW50ICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2xlZnRDb250ZW50fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtjaGlsZHJlblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBjaGlsZHJlblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBsYWJlbCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8UFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICd0ZXh0LXdyYXAgdGV4dC1mb3JlZ3JvdW5kIHRleHQtYmFzZScsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2xhYmVsfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9QPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7cmlnaHRDb250ZW50ICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3JpZ2h0Q29udGVudH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIClcclxuICAgIH0sXHJcbilcclxuXHJcbkNoZWNrRmllbGRMYWJlbC5kaXNwbGF5TmFtZSA9ICdDaGVja0ZpZWxkTGFiZWwnXHJcblxyXG5leHBvcnQgeyBDaGVja0ZpZWxkTGFiZWwsIGNoZWNrRmllbGRMYWJlbFZhcmlhbnRzLCBpbm5lcldyYXBwZXJWYXJpYW50cyB9XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImN2YSIsIkNoZWNrYm94IiwiUmFkaW9Hcm91cEl0ZW0iLCJjbiIsIlAiLCJjaGVja0ZpZWxkTGFiZWxWYXJpYW50cyIsInZhcmlhbnRzIiwidmFyaWFudCIsImRlZmF1bHQiLCJwcmltYXJ5Iiwic2Vjb25kYXJ5Iiwic3VjY2VzcyIsImRlc3RydWN0aXZlIiwid2FybmluZyIsInBpbmsiLCJvdXRsaW5lIiwic2l6ZSIsInNtIiwibGciLCJkaXNhYmxlZCIsInRydWUiLCJmYWxzZSIsImRlZmF1bHRWYXJpYW50cyIsImlubmVyV3JhcHBlclZhcmlhbnRzIiwiQ2hlY2tGaWVsZExhYmVsIiwiZm9yd2FyZFJlZiIsInJlZiIsInR5cGUiLCJpZCIsImNoZWNrZWQiLCJvbkNoZWNrZWRDaGFuZ2UiLCJ2YWx1ZSIsIm5hbWUiLCJsYWJlbCIsImNoaWxkcmVuIiwiY2xhc3NOYW1lIiwicmFkaW9Hcm91cFZhbHVlIiwiaXNSYWRpb1N0eWxlIiwicmlnaHRDb250ZW50IiwibGVmdENvbnRlbnQiLCJvbkNsaWNrIiwicHJvcHMiLCJ1bmlxdWVJZCIsInVzZUlkIiwiaW5wdXRJZCIsImlzUmFkaW9DaGVja2VkIiwiaGFuZGxlQ29udGFpbmVyQ2xpY2siLCJlIiwiZGl2IiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/check-field-label.tsx\n"));

/***/ })

});