"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew/crew.tsx":
/*!**********************************!*\
  !*** ./src/app/ui/crew/crew.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Crew; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _daily_checks_crew_welfare__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../daily-checks/crew-welfare */ \"(app-pages-browser)/./src/app/ui/daily-checks/crew-welfare.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/helpers/dateHelper */ \"(app-pages-browser)/./src/app/helpers/dateHelper.ts\");\n/* harmony import */ var _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/seaLogsMember */ \"(app-pages-browser)/./src/app/offline/models/seaLogsMember.js\");\n/* harmony import */ var _app_offline_models_crewDuty__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/crewDuty */ \"(app-pages-browser)/./src/app/offline/models/crewDuty.js\");\n/* harmony import */ var _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/crewMembers_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/crewMembers_LogBookEntrySection.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _app_offline_models_crewWelfare_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/app/offline/models/crewWelfare_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/crewWelfare_LogBookEntrySection.js\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _vessels_actions__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../vessels/actions */ \"(app-pages-browser)/./src/app/ui/vessels/actions.tsx\");\n/* harmony import */ var _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/app/lib/logbook-configuration */ \"(app-pages-browser)/./src/app/lib/logbook-configuration/index.ts\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_19___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_19__);\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_filter_components_crew_duty_dropdown__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/filter/components/crew-duty-dropdown */ \"(app-pages-browser)/./src/components/filter/components/crew-duty-dropdown.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_InfoIcon_lucide_react__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,InfoIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_InfoIcon_lucide_react__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,InfoIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _components_DateRange__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/DateRange */ \"(app-pages-browser)/./src/components/DateRange.tsx\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! ./types */ \"(app-pages-browser)/./src/app/ui/crew/types.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! ../../../../utils/responsiveLabel */ \"(app-pages-browser)/./utils/responsiveLabel.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Import types from separate file\n\n\n\n\n\n\nfunction Crew(param) {\n    let { crewSections = false, allCrew, logBookEntryID, locked, logBookConfig = false, setCrewMembers, crewWelfareCheck, updateCrewWelfare, vessel = false, masterID = 0, logEntrySections, offline = false, crewMembersList } = param;\n    var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents, _crewMember_data_trainingStatus, _crewMember_data, _crewMember_profile, _crewMember_profile1, _crewMember_profile2, _crewMember_profile3, _crewMember_data1, _crewMember_profile4, _crewMember_data2, _crewMember_profile5, _crewMember_data3, _crewMember_profile6, _crewMember_data4, _crewMember_profile7, _crewMember_data5, _crewMember_profile8, _crewMember_data6, _crewMember_data_trainingStatus1, _crewMember_data7, _crewMember_data_trainingStatus2, _crewMember_data8, _crewMember_data9, _crewMember_data10, _crewMember_data_trainingStatus_dues, _crewMember_data_trainingStatus3, _crewMember_data11, _crewMember_data12, _crewMember_data13;\n    _s();\n    const seaLogsMemberModel = new _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\n    const crewDutyModel = new _app_offline_models_crewDuty__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\n    const lbCrewModel = new _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n    const lbWelfareModel = new _app_offline_models_crewWelfare_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_15__[\"default\"]();\n    const [allVesselCrews, setAllVesselCrews] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allDuties, setAllDuties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams)();\n    var _searchParams_get;\n    const vesselID = (_searchParams_get = searchParams.get(\"vesselID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loaded, setLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [crewMember, setCrewMember] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [duty, setDuty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loginTime, setLoginTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    // Store logoutTime as a standard Date object or null\n    const [logoutTime, setLogoutTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [duties, setDuties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [crew, setCrew] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(crewSections);\n    const [crewConfig, setCrewConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Field labels and status\n    const [punchInStatus, setPunchInStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [punchInLabel, setPunchInLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Sign In\");\n    const [punchOutStatus, setPunchOutStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [punchOutLabel, setPunchOutLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Sign Out\");\n    const [workDetailsStatus, setWorkDetailsStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [workDetailsLabel, setWorkDetailsLabel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Work Details\");\n    // const [editCrew, setEditCrew] = useState(false);\n    // const [editCrewMember, setEditCrewMember] = useState(null);\n    const [crewManifestEntry, setCrewManifestEntry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [openAddCrewMemberDialog, setopenAddCrewMemberDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Function removed as we're now directly using handleSave\n    const [openEditLogoutTimeDialog, setOpenEditLogoutTimeDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [crewMemberOptions, setCrewMemberOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [openCrewTrainingDueDialog, setOpenCrewTrainingDueDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openConfirmCrewDeleteDialog, setOpenConfirmCrewDeleteDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [edit_logBookEntry, setEdit_logBookEntry] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allMembers, setAllMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_34__.useBreakpoints)();\n    const init_permissions = ()=>{\n        if (permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_16__.hasPermission)(process.env.EDIT_LOGBOOKENTRY || \"EDIT_LOGBOOKENTRY\", permissions)) {\n            setEdit_logBookEntry(true);\n        } else {\n            setEdit_logBookEntry(false);\n        }\n    };\n    const createOfflineCrewWelfareCheck = async ()=>{\n        // I need to add a 2-second delay to fix ConstraintError: Key already exists in the object store.\n        const delay = (ms)=>new Promise((resolve)=>setTimeout(resolve, ms));\n        await delay(2000);\n        const id = (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)();\n        const data = await lbWelfareModel.save({\n            id: id,\n            logBookEntryID: logBookEntryID,\n            fitness: null,\n            imSafe: null,\n            safetyActions: null,\n            waterQuality: null,\n            __typename: \"CrewWelfare_LogBookEntrySection\"\n        });\n        updateCrewWelfare(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_16__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (logEntrySections && Array.isArray(logEntrySections)) {\n            const hasCrewWelfare = logEntrySections.filter((section)=>section && section.className === \"SeaLogs\\\\CrewWelfare_LogBookEntrySection\").length;\n            if (hasCrewWelfare === 0 && !crewWelfareCheck && !loaded && !createCrewWelfareCheckLoading) {\n                setLoaded(true);\n                if (offline) {\n                    createOfflineCrewWelfareCheck();\n                } else {\n                    createCrewWelfareCheck({\n                        variables: {\n                            input: {\n                                logBookEntryID: +logBookEntryID\n                            }\n                        }\n                    });\n                }\n            }\n        }\n    }, [\n        logEntrySections\n    ]);\n    const [queryCrewDetail] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.CREW_DETAIL_WITH_TRAINING_STATUS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"GetCrewDetailError\", error);\n        }\n    });\n    const [queryVesselCrews] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.CREW_LIST_WITHOUT_TRAINING_STATUS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers;\n            if (data) {\n                const allMembers = data.nodes.filter((item)=>{\n                    return +item.id !== +masterID;\n                }).map((member)=>{\n                    // const crewWithTraining = GetCrewListWithTrainingStatus(\n                    //     [member],\n                    //     [vessel],\n                    // )[0]\n                    return {\n                        label: \"\".concat(member.firstName || \"\", \" \").concat(member.surname || \"\").trim(),\n                        value: member.id,\n                        // data: crewWithTraining,\n                        profile: {\n                            firstName: member.firstName,\n                            surname: member.surname,\n                            avatar: member.profileImage\n                        }\n                    };\n                });\n                setAllMembers(allMembers);\n                const members = allMembers.filter((member)=>{\n                    if (!crewSections) {\n                        return true;\n                    }\n                    return !Array.isArray(crewSections) || !crewSections.some((section)=>section && section.crewMember && section.crewMember.id === member.value && section.punchOut === null);\n                });\n                const memberOptions = members.filter((member)=>!crewMembersList || !Array.isArray(crewMembersList) || !crewMembersList.includes(+member.value));\n                setCrewMemberOptions(memberOptions);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVesselCrews error\", error);\n        }\n    });\n    const loadVesselCrews = async ()=>{\n        if (offline) {\n            const data = await seaLogsMemberModel.getByVesselId(vesselID);\n            setAllVesselCrews(data);\n            if (data) {\n                const members = data.filter((item)=>{\n                    return +item.id !== +masterID;\n                }).map((member)=>{\n                    const crewWithTraining = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n                        member\n                    ], [\n                        vessel\n                    ])[0];\n                    return {\n                        label: \"\".concat(member.firstName || \"\", \" \").concat(member.surname || \"\").trim(),\n                        value: member.id,\n                        data: crewWithTraining,\n                        profile: {\n                            firstName: member.firstName,\n                            surname: member.surname,\n                            avatar: member.profileImage\n                        }\n                    };\n                }) // filter out members who are already in the crew list\n                .filter((member)=>{\n                    if (!crewSections) {\n                        return true;\n                    }\n                    return !Array.isArray(crewSections) || !crewSections.some((section)=>section && section.crewMember && section.crewMember.id === member.value && section.punchOut === null);\n                });\n                setCrewMemberOptions(members);\n            }\n        } else {\n            await queryVesselCrews({\n                variables: {\n                    filter: {\n                        vehicles: {\n                            id: {\n                                eq: vesselID\n                            }\n                        },\n                        isArchived: {\n                            eq: false\n                        }\n                    }\n                }\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadDuties();\n            // handleSetCrewConfig()\n            loadVesselCrews();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    // Group crew duties by crew member\n    const groupCrewDutiesByMember = (crewData)=>{\n        if (!crewData || !Array.isArray(crewData) || crewData.length === 0) return [];\n        const groupedCrew = {};\n        // First, sort the crew data by punchIn time to ensure consistent ordering\n        const sortedCrewData = [\n            ...crewData\n        ].sort((a, b)=>{\n            if (!a || !b) return 0;\n            const timeA = a.punchIn ? new Date(a.punchIn).getTime() : 0;\n            const timeB = b.punchIn ? new Date(b.punchIn).getTime() : 0;\n            return timeA - timeB // Ascending order (oldest first)\n            ;\n        });\n        // Filter out archived members first\n        const activeCrewData = sortedCrewData.filter((member)=>member && !member.archived);\n        activeCrewData.forEach((member)=>{\n            if (!member) return;\n            const crewMemberId = member.crewMemberID;\n            if (!crewMemberId) return;\n            // If this member already has duties array, preserve it\n            if (member.duties && Array.isArray(member.duties) && member.duties.length > 0) {\n                if (!groupedCrew[crewMemberId]) {\n                    // Initialize with the existing duties\n                    groupedCrew[crewMemberId] = {\n                        ...member\n                    };\n                } else {\n                    // Merge duties from this member with existing duties\n                    const existingDuties = groupedCrew[crewMemberId].duties || [];\n                    member.duties.forEach((duty)=>{\n                        if (!duty) return;\n                        // Check if this duty is already in the list (avoid duplicates by ID)\n                        const isDuplicateById = existingDuties.some((existingDuty)=>existingDuty && existingDuty.id === duty.id);\n                        // Only add if it's not a duplicate\n                        if (!isDuplicateById) {\n                            existingDuties.push(duty);\n                        }\n                    });\n                    groupedCrew[crewMemberId] = {\n                        ...groupedCrew[crewMemberId],\n                        duties: existingDuties\n                    };\n                }\n            } else {\n                // Handle members without a duties array\n                if (!groupedCrew[crewMemberId]) {\n                    // Initialize with the first duty\n                    groupedCrew[crewMemberId] = {\n                        ...member,\n                        duties: [\n                            {\n                                id: member.id,\n                                dutyPerformed: member.dutyPerformed,\n                                punchIn: member.punchIn,\n                                punchOut: member.punchOut,\n                                workDetails: member.workDetails,\n                                dutyPerformedID: member.dutyPerformedID,\n                                logBookEntryID: member.logBookEntryID\n                            }\n                        ]\n                    };\n                } else if (groupedCrew[crewMemberId].duties && Array.isArray(groupedCrew[crewMemberId].duties)) {\n                    // Check if this duty is already in the list (avoid duplicates by ID)\n                    const isDuplicateById = groupedCrew[crewMemberId].duties.some((existingDuty)=>existingDuty && existingDuty.id === member.id);\n                    // Also check if this is a duplicate duty type with the same time (which would be redundant)\n                    const isDuplicateDutyType = groupedCrew[crewMemberId].duties.some((existingDuty)=>existingDuty && existingDuty.dutyPerformedID === member.dutyPerformedID && existingDuty.punchIn === member.punchIn);\n                    // Only add if it's not a duplicate by ID or duty type\n                    if (!isDuplicateById && !isDuplicateDutyType) {\n                        groupedCrew[crewMemberId].duties.push({\n                            id: member.id,\n                            dutyPerformed: member.dutyPerformed,\n                            punchIn: member.punchIn,\n                            punchOut: member.punchOut,\n                            workDetails: member.workDetails,\n                            dutyPerformedID: member.dutyPerformedID,\n                            logBookEntryID: member.logBookEntryID\n                        });\n                    }\n                }\n            }\n        });\n        // Sort duties by punchIn time in ascending order for each crew member\n        Object.values(groupedCrew).forEach((crewMember)=>{\n            if (crewMember && crewMember.duties && Array.isArray(crewMember.duties) && crewMember.duties.length > 1) {\n                crewMember.duties.sort((a, b)=>{\n                    if (!a || !b) return 0;\n                    const timeA = a.punchIn ? new Date(a.punchIn).getTime() : 0;\n                    const timeB = b.punchIn ? new Date(b.punchIn).getTime() : 0;\n                    return timeA - timeB // Ascending order (oldest first)\n                    ;\n                });\n            }\n        });\n        return Object.values(groupedCrew);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (crewSections && Array.isArray(crewSections)) {\n            // Process each crew member's training status\n            const processedCrewSections = crewSections.map((section)=>{\n                if (section && section.crewMember) {\n                    // Apply GetCrewListWithTrainingStatus to the crewMember property\n                    const crewMemberWithTrainingStatus = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n                        section.crewMember\n                    ], [\n                        vessel\n                    ])[0];\n                    return {\n                        ...section,\n                        crewMember: crewMemberWithTrainingStatus\n                    };\n                }\n                return section;\n            });\n            // Preserve existing duties if they exist\n            let updatedData = processedCrewSections;\n            if (crew && Array.isArray(crew) && crew.length > 0) {\n                // Create a map of existing crew members with their duties\n                const existingCrewMap = crew.reduce((map, member)=>{\n                    if (member && member.crewMemberID) {\n                        map[member.crewMemberID] = member;\n                    }\n                    return map;\n                }, {});\n                // Check if any existing crew members have duties that need to be preserved\n                const hasExistingDuties = Object.values(existingCrewMap).some((member)=>member && member.duties && Array.isArray(member.duties) && member.duties.length > 0);\n                if (hasExistingDuties) {\n                    // Update processed data with existing duties where applicable\n                    updatedData = processedCrewSections.map((section)=>{\n                        if (!section || !section.crewMemberID) return section;\n                        const existingMember = existingCrewMap[section.crewMemberID];\n                        if (existingMember && existingMember.duties && Array.isArray(existingMember.duties) && existingMember.duties.length > 0) {\n                            // Check if this section's ID is already in the existing duties\n                            const dutyExists = existingMember.duties.some((duty)=>duty && duty.id === section.id);\n                            if (dutyExists) {\n                                // This section is already in the duties, so return the section with duties\n                                return {\n                                    ...section,\n                                    duties: existingMember.duties\n                                };\n                            } else {\n                                // This is a new duty for this crew member, add it to their duties\n                                const updatedDuties = [\n                                    ...existingMember.duties\n                                ];\n                                updatedDuties.push({\n                                    id: section.id,\n                                    dutyPerformed: section.dutyPerformed,\n                                    punchIn: section.punchIn,\n                                    punchOut: section.punchOut,\n                                    workDetails: section.workDetails,\n                                    dutyPerformedID: section.dutyPerformedID,\n                                    logBookEntryID: section.logBookEntryID\n                                });\n                                return {\n                                    ...section,\n                                    duties: updatedDuties\n                                };\n                            }\n                        }\n                        // No existing duties for this crew member, create a new duties array\n                        return {\n                            ...section,\n                            duties: [\n                                {\n                                    id: section.id,\n                                    dutyPerformed: section.dutyPerformed,\n                                    punchIn: section.punchIn,\n                                    punchOut: section.punchOut,\n                                    workDetails: section.workDetails,\n                                    dutyPerformedID: section.dutyPerformedID,\n                                    logBookEntryID: section.logBookEntryID\n                                }\n                            ]\n                        };\n                    });\n                }\n            }\n            // Group crew duties by crew member\n            const groupedCrewSections = groupCrewDutiesByMember(updatedData);\n            setCrew(groupedCrewSections);\n        }\n    }, [\n        crewSections,\n        vessel\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (masterID > 0) {\n            loadVesselCrews();\n        }\n    }, [\n        masterID\n    ]);\n    const loadDuties = async ()=>{\n        if (offline) {\n            const data = await crewDutyModel.getAll();\n            setAllDuties(data);\n            if (data) {\n                const activeDuties = data.filter((duty)=>!duty.archived);\n                setDuties(activeDuties);\n            }\n        } else {\n            await queryDuties();\n        }\n    };\n    const handleSetCrewConfig = ()=>{\n        if (logBookConfig && logBookConfig.customisedLogBookComponents && logBookConfig.customisedLogBookComponents.nodes && Array.isArray(logBookConfig.customisedLogBookComponents.nodes)) {\n            const crewMembersConfigs = logBookConfig.customisedLogBookComponents.nodes.filter((config)=>config && config.title === \"Crew Members\");\n            const length = crewMembersConfigs.length;\n            if (length === 1) {\n                const config = crewMembersConfigs[0];\n                if (config && config.customisedComponentFields && config.customisedComponentFields.nodes && Array.isArray(config.customisedComponentFields.nodes)) {\n                    setCrewConfig(config.customisedComponentFields.nodes.map((field)=>({\n                            title: field.fieldName,\n                            status: field.status\n                        })));\n                }\n            } else if (length > 1) {\n                const sortedConfigs = [\n                    ...crewMembersConfigs\n                ].sort((a, b)=>parseInt(b.id) - parseInt(a.id));\n                const config = sortedConfigs[0];\n                if (config && config.customisedComponentFields && config.customisedComponentFields.nodes && Array.isArray(config.customisedComponentFields.nodes)) {\n                    setCrewConfig(config.customisedComponentFields.nodes.map((field)=>({\n                            title: field.fieldName,\n                            status: field.status\n                        })));\n                }\n            }\n        } else {\n            setCrewConfig(false);\n        }\n    };\n    const handleSetStatus = ()=>{\n        if (Array.isArray(crewConfig) && crewConfig.length > 0 && logBookConfig && logBookConfig.customisedLogBookComponents && logBookConfig.customisedLogBookComponents.nodes && Array.isArray(logBookConfig.customisedLogBookComponents.nodes)) {\n            const crewMemberComponents = logBookConfig.customisedLogBookComponents.nodes.filter((config)=>config && config.title === \"Crew Members\");\n            const crewMemberComponent = crewMemberComponents.length > 0 ? crewMemberComponents[0] : null;\n            if (crewMemberComponent && crewMemberComponent.customisedComponentFields && crewMemberComponent.customisedComponentFields.nodes && Array.isArray(crewMemberComponent.customisedComponentFields.nodes)) {\n                // Crew Member\n                let title = \"CrewMemberID\";\n                const crewMemberField = crewMemberComponent.customisedComponentFields.nodes.find((item)=>item && item.fieldName === title);\n                // We already have a default value set in the useState, so we only need to update if we have a valid value\n                if (crewMemberField) {\n                // We don't need to set crew member label anymore as it's not used\n                // Keeping the code structure for future reference\n                }\n                // Primary Duty\n                title = \"DutyPerformedID\";\n                const primaryDutyField = crewMemberComponent.customisedComponentFields.nodes.find((item)=>item && item.fieldName === title);\n                // We already have a default value set in the useState, so we only need to update if we have a valid value\n                if (primaryDutyField) {\n                // We don't need to set primary duty label anymore as it's not used\n                // Keeping the code structure for future reference\n                }\n                // Punch in\n                title = \"PunchIn\";\n                const punchInConfig = crewConfig.find((config)=>config && config.title === title);\n                setPunchInStatus((punchInConfig === null || punchInConfig === void 0 ? void 0 : punchInConfig.status) || \"On\");\n                const punchInField = crewMemberComponent.customisedComponentFields.nodes.find((item)=>item && item.fieldName === title);\n                // We already have a default value set in the useState, so we only need to update if we have a valid value\n                if (punchInField) {\n                    const customTitle = punchInField.customisedFieldTitle;\n                    const fieldNameValue = (0,_vessels_actions__WEBPACK_IMPORTED_MODULE_17__.getFieldName)(punchInField, _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_18__.SLALL_LogBookFields);\n                    // Only update if we have a valid value\n                    if (customTitle && customTitle.trim() !== \"\") {\n                        setPunchInLabel(customTitle);\n                    } else if (fieldNameValue && fieldNameValue.trim() !== \"\") {\n                        setPunchInLabel(fieldNameValue);\n                    }\n                // Otherwise keep the default 'Sign In'\n                }\n                // Punch out\n                title = \"PunchOut\";\n                const punchOutConfig = crewConfig.find((config)=>config && config.title === title);\n                setPunchOutStatus((punchOutConfig === null || punchOutConfig === void 0 ? void 0 : punchOutConfig.status) || \"On\");\n                const punchOutField = crewMemberComponent.customisedComponentFields.nodes.find((item)=>item && item.fieldName === title);\n                // We already have a default value set in the useState, so we only need to update if we have a valid value\n                if (punchOutField) {\n                    const customTitle = punchOutField.customisedFieldTitle;\n                    const fieldNameValue = (0,_vessels_actions__WEBPACK_IMPORTED_MODULE_17__.getFieldName)(punchOutField, _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_18__.SLALL_LogBookFields);\n                    // Only update if we have a valid value\n                    if (customTitle && customTitle.trim() !== \"\") {\n                        setPunchOutLabel(customTitle);\n                    } else if (fieldNameValue && fieldNameValue.trim() !== \"\") {\n                        setPunchOutLabel(fieldNameValue);\n                    }\n                // Otherwise keep the default 'Sign Out'\n                }\n                // Work details\n                title = \"WorkDetails\";\n                const workDetailsConfig = crewConfig.find((config)=>config && config.title === title);\n                setWorkDetailsStatus((workDetailsConfig === null || workDetailsConfig === void 0 ? void 0 : workDetailsConfig.status) || \"On\");\n                const workDetailsField = crewMemberComponent.customisedComponentFields.nodes.find((item)=>item && item.fieldName === title);\n                // We already have a default value set in the useState, so we only need to update if we have a valid value\n                if (workDetailsField) {\n                    const customTitle = workDetailsField.customisedFieldTitle;\n                    const fieldNameValue = (0,_vessels_actions__WEBPACK_IMPORTED_MODULE_17__.getFieldName)(workDetailsField, _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_18__.SLALL_LogBookFields);\n                    // Only update if we have a valid value\n                    if (customTitle && customTitle.trim() !== \"\") {\n                        setWorkDetailsLabel(customTitle);\n                    } else if (fieldNameValue && fieldNameValue.trim() !== \"\") {\n                        setWorkDetailsLabel(fieldNameValue);\n                    }\n                // Otherwise keep the default 'Work Details'\n                }\n            }\n        } else {\n            // Set default values if crewConfig is not valid\n            setPunchInStatus(\"On\");\n            setPunchInLabel(\"Sign In\");\n            setPunchOutStatus(\"On\");\n            setPunchOutLabel(\"Sign Out\");\n            setWorkDetailsStatus(\"On\");\n            setWorkDetailsLabel(\"Work Details\");\n        }\n    };\n    const [queryDuties] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.CREW_DUTY, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readCrewDuties.nodes;\n            if (data) {\n                const activeDuties = data.filter((duty)=>!duty.archived);\n                setDuties(activeDuties);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryDutiesEntry error\", error);\n        }\n    });\n    const handleLogin = (date)=>{\n        if (!date) {\n            // Handle the case when date is null or undefined (unselected)\n            const currentTime = new Date();\n            setLoginTime(currentTime);\n            setCrewManifestEntry({\n                ...crewManifestEntry,\n                punchIn: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(currentTime)\n            });\n            return;\n        }\n        try {\n            // Ensure we have a valid date by creating a new Date object\n            const validDate = new Date(date);\n            // Check if the date is valid\n            if (isNaN(validDate.getTime())) {\n                console.error(\"Invalid date provided to handleLogin:\", date);\n                return;\n            }\n            setLoginTime(validDate);\n            setCrewManifestEntry({\n                ...crewManifestEntry,\n                punchIn: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(validDate)\n            });\n            // If logout time is set and is before the new login time, reset it\n            if (logoutTime && validDate.getTime() > logoutTime.getTime()) {\n                setLogoutTime(null);\n                setCrewManifestEntry((prev)=>({\n                        ...prev,\n                        punchOut: null\n                    }));\n            }\n        } catch (error) {\n            console.error(\"Error in handleLogin:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"An error occurred while setting the sign in time\");\n        }\n    };\n    const handleLogout = (date)=>{\n        if (!date) {\n            // Handle the case when date is null or undefined (unselected)\n            setLogoutTime(null);\n            setCrewManifestEntry({\n                ...crewManifestEntry,\n                punchOut: null\n            });\n            return;\n        }\n        try {\n            // Ensure we have a valid date by creating a new Date object\n            const validDate = new Date(date);\n            // Check if the date is valid\n            if (isNaN(validDate.getTime())) {\n                console.error(\"Invalid date provided to handleLogout:\", date);\n                return;\n            }\n            // If the date doesn't have time set (hours and minutes are 0),\n            // set the current time\n            if (validDate.getHours() === 0 && validDate.getMinutes() === 0) {\n                const now = new Date();\n                validDate.setHours(now.getHours());\n                validDate.setMinutes(now.getMinutes());\n            }\n            // Convert to dayjs for easier comparison\n            const dayjsDate = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(validDate);\n            // Ensure logout time is after login time\n            if (loginTime && dayjsDate.isBefore(loginTime)) {\n                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Sign out time must be after sign in time\");\n                return;\n            }\n            // Store the date as a standard Date object to avoid any issues with dayjs\n            setLogoutTime(validDate);\n            // Update crew manifest entry with formatted date string\n            setCrewManifestEntry({\n                ...crewManifestEntry,\n                punchOut: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(validDate)\n            });\n        } catch (error) {\n            console.error(\"Error in handleLogout:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"An error occurred while setting the sign out time\");\n        }\n    };\n    const handleAddManifest = ()=>{\n        // Check permissions first\n        if (!edit_logBookEntry) {\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"You do not have permission to edit this log entry\");\n            return;\n        }\n        // Filter crew member options to only show available crew members\n        if (allMembers && Array.isArray(allMembers)) {\n            // Get a list of crew members who are already signed in (without sign-out time)\n            const signedInCrewMemberIDs = new Set();\n            if (crew && Array.isArray(crew)) {\n                crew.forEach((member)=>{\n                    // Only consider members who are signed in without a sign-out time\n                    if (member && member.duties && Array.isArray(member.duties)) {\n                        // Check if any duty has no punch out time\n                        const hasActiveShift = member.duties.some((duty)=>duty && duty.punchOut === null);\n                        if (hasActiveShift) {\n                            signedInCrewMemberIDs.add(member.crewMemberID);\n                        }\n                    } else if (member && member.punchOut === null) {\n                        signedInCrewMemberIDs.add(member.crewMemberID);\n                    }\n                });\n            }\n            // Filter out crew members who are already signed in\n            const availableCrewMembers = allMembers.filter((member)=>{\n                if (!member) return false;\n                return !signedInCrewMemberIDs.has(member.value);\n            });\n            // Further filter out crew members who are in the crewMembersList (if applicable)\n            const filteredCrewOptions = availableCrewMembers.filter((member)=>!member || !crewMembersList || !Array.isArray(crewMembersList) || !crewMembersList.includes(+member.value));\n            setCrewMemberOptions(filteredCrewOptions);\n        } else {\n            // If allMembers is not valid, just proceed with empty options\n            setCrewMemberOptions([]);\n        }\n        // Set up the new crew manifest entry with current time\n        const currentTime = new Date();\n        const crewManifestEntry = {\n            id: 0,\n            logBookEntryID: +logBookEntryID,\n            crewMemberID: 0,\n            dutyPerformedID: 0,\n            punchIn: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(currentTime),\n            punchOut: null\n        };\n        setCrewManifestEntry(crewManifestEntry);\n        setLoginTime(currentTime);\n        setLogoutTime(null);\n        setCrewMember(null);\n        setDuty(null);\n        setopenAddCrewMemberDialog(true);\n    };\n    const handleEditManifest = (memberData)=>{\n        if (!edit_logBookEntry) {\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"You do not have permission to edit this log entry\");\n            return;\n        }\n        // If this is a grouped crew member with multiple duties, use the first duty\n        const dutyToEdit = memberData.duties && memberData.duties.length > 0 ? memberData.duties[0] : memberData;\n        setCrewManifestEntry({\n            id: dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.id,\n            logBookEntryID: dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.logBookEntryID,\n            crewMemberID: memberData === null || memberData === void 0 ? void 0 : memberData.crewMemberID,\n            dutyPerformedID: dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.dutyPerformedID,\n            punchIn: dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.punchIn,\n            punchOut: dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.punchOut,\n            workDetails: dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.workDetails\n        });\n        // Create a proper crew member object with profile details\n        const crewMemberWithProfile = {\n            label: \"\".concat(memberData.crewMember.firstName || \"\", \" \").concat(memberData.crewMember.surname !== null ? memberData.crewMember.surname : \"\").trim(),\n            value: memberData.crewMember.id,\n            data: memberData.crewMember,\n            profile: {\n                firstName: memberData.crewMember.firstName,\n                surname: memberData.crewMember.surname,\n                avatar: memberData.crewMember.profileImage\n            }\n        };\n        setCrewMember(crewMemberWithProfile);\n        // Find the correct duty in the duties array\n        const selectedDuty = duties.find((memberDuty)=>memberDuty.id === (dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.dutyPerformedID));\n        if (selectedDuty) {\n            setDuty({\n                label: selectedDuty.title,\n                value: selectedDuty.id\n            });\n        }\n        setLoginTime((dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.punchIn) ? new Date(dutyToEdit.punchIn) : new Date());\n        setLogoutTime((dutyToEdit === null || dutyToEdit === void 0 ? void 0 : dutyToEdit.punchOut) ? new Date(dutyToEdit.punchOut) : null);\n        setopenAddCrewMemberDialog(true);\n    };\n    const handleSignOutTime = (memberData)=>{\n        if (!edit_logBookEntry) {\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"You do not have permission to edit this log entry\");\n            return;\n        }\n        // Determine if this is a nested duty or a main crew member\n        const isNestedDuty = !memberData.crewMember;\n        if (isNestedDuty) {\n            // This is a nested duty\n            // Find the parent crew member for this duty\n            const parentMember = crew && Array.isArray(crew) ? crew.find((c)=>{\n                return c && c.duties && Array.isArray(c.duties) && c.duties.some((d)=>d && d.id === memberData.id);\n            }) : null;\n            if (parentMember) {\n                var _memberData_dutyPerformed;\n                // Set crew manifest entry with the parent crew member ID\n                setCrewManifestEntry({\n                    id: memberData === null || memberData === void 0 ? void 0 : memberData.id,\n                    logBookEntryID: memberData === null || memberData === void 0 ? void 0 : memberData.logBookEntryID,\n                    crewMemberID: parentMember.crewMemberID,\n                    dutyPerformedID: memberData === null || memberData === void 0 ? void 0 : (_memberData_dutyPerformed = memberData.dutyPerformed) === null || _memberData_dutyPerformed === void 0 ? void 0 : _memberData_dutyPerformed.id,\n                    punchIn: memberData === null || memberData === void 0 ? void 0 : memberData.punchIn,\n                    punchOut: memberData === null || memberData === void 0 ? void 0 : memberData.punchOut,\n                    workDetails: memberData === null || memberData === void 0 ? void 0 : memberData.workDetails\n                });\n                // Create a proper crew member object with profile details\n                const crewMemberWithProfile = {\n                    label: \"\".concat(parentMember.crewMember.firstName || \"\", \" \").concat(parentMember.crewMember.surname !== null ? parentMember.crewMember.surname : \"\").trim(),\n                    value: parentMember.crewMember.id,\n                    data: parentMember.crewMember,\n                    profile: {\n                        firstName: parentMember.crewMember.firstName,\n                        surname: parentMember.crewMember.surname,\n                        avatar: parentMember.crewMember.profileImage\n                    }\n                };\n                setCrewMember(crewMemberWithProfile);\n                // Set duty\n                if (memberData.dutyPerformed) {\n                    const selectedDuty = duties.find((memberDuty)=>{\n                        var _memberData_dutyPerformed;\n                        return memberDuty.id === (memberData === null || memberData === void 0 ? void 0 : (_memberData_dutyPerformed = memberData.dutyPerformed) === null || _memberData_dutyPerformed === void 0 ? void 0 : _memberData_dutyPerformed.id);\n                    });\n                    if (selectedDuty) {\n                        setDuty({\n                            label: selectedDuty.title,\n                            value: selectedDuty.id\n                        });\n                    }\n                }\n            } else {\n                // If parent member not found, show an error\n                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Could not find the associated crew member\");\n                return;\n            }\n        } else {\n            var _memberData_dutyPerformed1;\n            // This is a main crew member\n            setCrewManifestEntry({\n                id: memberData === null || memberData === void 0 ? void 0 : memberData.id,\n                logBookEntryID: memberData === null || memberData === void 0 ? void 0 : memberData.logBookEntryID,\n                crewMemberID: memberData === null || memberData === void 0 ? void 0 : memberData.crewMemberID,\n                dutyPerformedID: memberData === null || memberData === void 0 ? void 0 : (_memberData_dutyPerformed1 = memberData.dutyPerformed) === null || _memberData_dutyPerformed1 === void 0 ? void 0 : _memberData_dutyPerformed1.id,\n                punchIn: memberData === null || memberData === void 0 ? void 0 : memberData.punchIn,\n                punchOut: memberData === null || memberData === void 0 ? void 0 : memberData.punchOut,\n                workDetails: memberData === null || memberData === void 0 ? void 0 : memberData.workDetails\n            });\n            // Create a proper crew member object with profile details\n            const crewMemberWithProfile = {\n                label: \"\".concat(memberData.crewMember.firstName || \"\", \" \").concat(memberData.crewMember.surname !== null ? memberData.crewMember.surname : \"\").trim(),\n                value: memberData.crewMember.id,\n                data: memberData.crewMember,\n                profile: {\n                    firstName: memberData.crewMember.firstName,\n                    surname: memberData.crewMember.surname,\n                    avatar: memberData.crewMember.profileImage\n                }\n            };\n            setCrewMember(crewMemberWithProfile);\n            // Set duty\n            if (memberData.dutyPerformed) {\n                const selectedDuty = duties.find((memberDuty)=>{\n                    var _memberData_dutyPerformed;\n                    return memberDuty.id === (memberData === null || memberData === void 0 ? void 0 : (_memberData_dutyPerformed = memberData.dutyPerformed) === null || _memberData_dutyPerformed === void 0 ? void 0 : _memberData_dutyPerformed.id);\n                });\n                if (selectedDuty) {\n                    setDuty({\n                        label: selectedDuty.title,\n                        value: selectedDuty.id\n                    });\n                }\n            }\n        }\n        // Set times\n        setLoginTime(memberData.punchIn ? new Date(memberData.punchIn) : new Date());\n        setLogoutTime(memberData.punchOut ? new Date(memberData.punchOut) : null);\n        setOpenEditLogoutTimeDialog(true);\n    };\n    const handleCrewMember = async (selected)=>{\n        if (!selected) return;\n        setDuty({\n            label: \"-- Select Duty --\",\n            value: 0\n        });\n        const { data } = await queryCrewDetail({\n            variables: {\n                crewMemberID: selected.value\n            }\n        });\n        const member = data.readOneSeaLogsMember;\n        const crewWithTraining = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n            member\n        ], [\n            vessel\n        ])[0];\n        const value = {\n            ...selected,\n            data: crewWithTraining\n        };\n        setCrewMember(value);\n        // Check if the crew has a training due\n        if (value.data && value.data.trainingStatus && value.data.trainingStatus.label !== \"Good\") {\n            setOpenCrewTrainingDueDialog(true);\n        }\n        // Set default duty\n        if (allCrew && Array.isArray(allCrew)) {\n            const crewMember = allCrew.find((member)=>member && member.id === value.value);\n            if (crewMember && crewMember.primaryDutyID) {\n                if (duties && Array.isArray(duties)) {\n                    const crewDuty = duties.find((d)=>d && d.id === crewMember.primaryDutyID);\n                    if (crewDuty) {\n                        const newDuty = {\n                            label: crewDuty.title,\n                            value: crewDuty.id\n                        };\n                        setDuty(newDuty);\n                        setCrewManifestEntry({\n                            ...crewManifestEntry,\n                            crewMemberID: crewMember.id,\n                            dutyPerformedID: crewDuty.id\n                        });\n                    } else {\n                        setCrewManifestEntry({\n                            ...crewManifestEntry,\n                            crewMemberID: crewMember.id\n                        });\n                    }\n                } else {\n                    setCrewManifestEntry({\n                        ...crewManifestEntry,\n                        crewMemberID: crewMember.id\n                    });\n                }\n            } else if (crewMember) {\n                setCrewManifestEntry({\n                    ...crewManifestEntry,\n                    crewMemberID: crewMember.id\n                });\n            }\n        }\n    };\n    const handleDuty = (value)=>{\n        setDuty(value);\n        setCrewManifestEntry({\n            ...crewManifestEntry,\n            dutyPerformedID: (value === null || value === void 0 ? void 0 : value.value) || 0\n        });\n    };\n    const handleCancel = ()=>{\n        setCrewManifestEntry({});\n        setCrewMember(null);\n        setDuty(null);\n        setLoginTime(new Date());\n        setLogoutTime(null);\n        setopenAddCrewMemberDialog(false);\n        setOpenEditLogoutTimeDialog(false);\n    };\n    const handleSave = async (callBy)=>{\n        // Validate required fields\n        if (!crewManifestEntry.crewMemberID || crewManifestEntry.crewMemberID === 0) {\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Please select a crew member\");\n            return;\n        }\n        if (!crewManifestEntry.dutyPerformedID || crewManifestEntry.dutyPerformedID === 0) {\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Please select a duty\");\n            return;\n        }\n        // Get work details from the textarea\n        const workDetailsElement = document.getElementById(\"work-details\");\n        const workDetails = (workDetailsElement === null || workDetailsElement === void 0 ? void 0 : workDetailsElement.value) || \"\";\n        const variables = {\n            id: crewManifestEntry.id,\n            crewMemberID: crewManifestEntry.crewMemberID,\n            dutyPerformedID: +(crewManifestEntry === null || crewManifestEntry === void 0 ? void 0 : crewManifestEntry.dutyPerformedID),\n            logBookEntryID: +logBookEntryID,\n            punchIn: loginTime ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(loginTime) : null,\n            punchOut: logoutTime ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(logoutTime) : null,\n            workDetails: workDetails\n        };\n        try {\n            // Case 1: Updating an existing crew entry\n            if (crewManifestEntry.id > 0) {\n                if (offline) {\n                    // Save the updated crew member to the database\n                    await lbCrewModel.save(variables);\n                    // Get all crew IDs to fetch updated data\n                    const crewIds = crew && Array.isArray(crew) ? crew.map((c)=>c.id).filter(Boolean) : [];\n                    // Reset the form\n                    setCrewManifestEntry({});\n                    // Fetch the updated crew data\n                    let crewData = await lbCrewModel.getByIds(crewIds);\n                    if (crewData) {\n                        // Process crew members with training status\n                        const processedData = crewData.map((section)=>{\n                            if (section && section.crewMember) {\n                                const crewMemberWithTrainingStatus = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n                                    section.crewMember\n                                ], [\n                                    vessel\n                                ])[0];\n                                return {\n                                    ...section,\n                                    crewMember: crewMemberWithTrainingStatus\n                                };\n                            }\n                            return section;\n                        });\n                        // Group crew duties by crew member\n                        const groupedData = groupCrewDutiesByMember(processedData);\n                        // Update state\n                        setCrew(groupedData);\n                        setCrewMembers(groupedData);\n                    }\n                } else {\n                    // Online mode - use GraphQL mutation\n                    updateCrewMembers_LogBookEntrySection({\n                        variables: {\n                            input: variables\n                        }\n                    });\n                }\n                // Close dialogs\n                setopenAddCrewMemberDialog(false);\n                if (callBy === \"update\") {\n                    setOpenEditLogoutTimeDialog(false);\n                }\n            } else if (crewManifestEntry.crewMemberID > 0) {\n                if (offline) {\n                    // Generate a unique ID for the new entry\n                    const uniqueId = (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)();\n                    const data = {\n                        ...variables,\n                        id: uniqueId\n                    };\n                    // Save the new crew member to the database\n                    await lbCrewModel.save(data);\n                    // Get the selected crew member and duty information\n                    const selectedMember = allVesselCrews.find((c)=>c.id === crewManifestEntry.crewMemberID);\n                    const selectedDuty = allDuties.find((d)=>d.id === crewManifestEntry.dutyPerformedID);\n                    if (!selectedMember || !selectedDuty) {\n                        sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Could not find crew member or duty information\");\n                        return;\n                    }\n                    // Create a new crew entry with the necessary data for immediate display\n                    const newCrewEntry = {\n                        ...data,\n                        crewMember: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n                            selectedMember\n                        ], [\n                            vessel\n                        ])[0],\n                        dutyPerformed: selectedDuty\n                    };\n                    // Get existing crew data or initialize empty array\n                    const existingCrew = Array.isArray(crew) ? [\n                        ...crew\n                    ] : [];\n                    // Check if this crew member already exists in the list\n                    const existingCrewMemberIndex = existingCrew.findIndex((c)=>c && c.crewMemberID === data.crewMemberID);\n                    let updatedCrewData = [\n                        ...existingCrew\n                    ];\n                    if (existingCrewMemberIndex !== -1) {\n                        // If the crew member already exists, add this duty to their duties array\n                        const existingCrewMember = {\n                            ...updatedCrewData[existingCrewMemberIndex]\n                        };\n                        if (existingCrewMember.duties && Array.isArray(existingCrewMember.duties)) {\n                            // Add the new duty to the existing duties array\n                            existingCrewMember.duties.push({\n                                id: data.id,\n                                dutyPerformed: selectedDuty,\n                                punchIn: data.punchIn,\n                                punchOut: data.punchOut,\n                                workDetails: data.workDetails,\n                                dutyPerformedID: data.dutyPerformedID,\n                                logBookEntryID: data.logBookEntryID\n                            });\n                        } else {\n                            // Create a duties array if it doesn't exist\n                            existingCrewMember.duties = [\n                                {\n                                    id: data.id,\n                                    dutyPerformed: selectedDuty,\n                                    punchIn: data.punchIn,\n                                    punchOut: data.punchOut,\n                                    workDetails: data.workDetails,\n                                    dutyPerformedID: data.dutyPerformedID,\n                                    logBookEntryID: data.logBookEntryID\n                                }\n                            ];\n                        }\n                        // Update the crew member in the array\n                        updatedCrewData[existingCrewMemberIndex] = existingCrewMember;\n                    } else {\n                        // If this is a new crew member, add them to the list with their first duty\n                        updatedCrewData.push({\n                            ...newCrewEntry,\n                            duties: [\n                                {\n                                    id: data.id,\n                                    dutyPerformed: selectedDuty,\n                                    punchIn: data.punchIn,\n                                    punchOut: data.punchOut,\n                                    workDetails: data.workDetails,\n                                    dutyPerformedID: data.dutyPerformedID,\n                                    logBookEntryID: data.logBookEntryID\n                                }\n                            ]\n                        });\n                    }\n                    // Group the updated crew data\n                    const groupedData = groupCrewDutiesByMember(updatedCrewData);\n                    // Update state with the new grouped data\n                    setCrew(groupedData);\n                    setCrewMembers(groupedData);\n                    setCrewManifestEntry({});\n                    // Also fetch the latest data from the database to ensure consistency\n                    const crewIds = updatedCrewData.map((c)=>c && c.id).filter(Boolean).concat([\n                        uniqueId\n                    ]);\n                    let crewData = await lbCrewModel.getByIds(crewIds);\n                    if (crewData) {\n                        // Process crew members with training status\n                        const processedDbData = crewData.map((section)=>{\n                            if (section && section.crewMember) {\n                                const crewMemberWithTrainingStatus = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n                                    section.crewMember\n                                ], [\n                                    vessel\n                                ])[0];\n                                return {\n                                    ...section,\n                                    crewMember: crewMemberWithTrainingStatus\n                                };\n                            }\n                            return section;\n                        });\n                        // Group crew duties by crew member\n                        const groupedDbData = groupCrewDutiesByMember(processedDbData);\n                        // Update with the database data to ensure consistency\n                        setCrew(groupedDbData);\n                        setCrewMembers(groupedDbData);\n                    }\n                } else {\n                    // Online mode - use GraphQL mutation\n                    createCrewMembers_LogBookEntrySection({\n                        variables: {\n                            input: variables\n                        }\n                    });\n                }\n                // Close dialog\n                setopenAddCrewMemberDialog(false);\n            } else {\n                // No valid crew member selected, just cancel\n                handleCancel();\n            }\n        } catch (error) {\n            console.error(\"Error saving crew member:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Failed to save crew member. Please try again.\");\n        }\n    };\n    const [updateCrewMembers_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.UpdateCrewMembers_LogBookEntrySection, {\n        onCompleted: ()=>{\n            // First, update the UI immediately with the updated data\n            if (crewManifestEntry.id > 0 && crew && Array.isArray(crew)) {\n                // Create a deep copy of the current crew data\n                let updatedCrewData = JSON.parse(JSON.stringify(crew));\n                // Find the crew member and duty that was updated\n                let foundAndUpdated = false;\n                // Loop through all crew members\n                for(let i = 0; i < updatedCrewData.length; i++){\n                    const member = updatedCrewData[i];\n                    // Check if this is the main duty that was updated\n                    if (member.id === crewManifestEntry.id) {\n                        // Update the main duty\n                        updatedCrewData[i] = {\n                            ...member,\n                            punchOut: crewManifestEntry.punchOut\n                        };\n                        foundAndUpdated = true;\n                        break;\n                    }\n                    // Check if this is a nested duty that was updated\n                    if (member.duties && Array.isArray(member.duties)) {\n                        for(let j = 0; j < member.duties.length; j++){\n                            const duty = member.duties[j];\n                            if (duty.id === crewManifestEntry.id) {\n                                // Update the nested duty\n                                member.duties[j] = {\n                                    ...duty,\n                                    punchOut: crewManifestEntry.punchOut\n                                };\n                                foundAndUpdated = true;\n                                break;\n                            }\n                        }\n                        if (foundAndUpdated) {\n                            break;\n                        }\n                    }\n                }\n                if (foundAndUpdated) {\n                    // Group the updated crew data\n                    const groupedData = groupCrewDutiesByMember(updatedCrewData);\n                    // Update state with the new grouped data\n                    setCrew(groupedData);\n                    setCrewMembers(groupedData);\n                }\n            }\n            // Then, fetch the latest data from the server to ensure consistency\n            const appendData = [\n                ...crew.map((c)=>c.id)\n            ];\n            setCrewManifestEntry({});\n            const searchFilter = {};\n            searchFilter.id = {\n                in: appendData\n            };\n            searchFilter.archived = {\n                eq: false\n            };\n            getSectionCrewMembers_LogBookEntrySection({\n                variables: {\n                    filter: searchFilter\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"updateCrewMembers_LogBookEntrySection\", error);\n        }\n    });\n    const [createCrewWelfareCheck, { loading: createCrewWelfareCheckLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateCrewWelfare_LogBookEntrySection, {\n        onCompleted: (response)=>{\n            const data = response.createCrewWelfare_LogBookEntrySection;\n            updateCrewWelfare(data);\n        },\n        onError: (error)=>{\n            console.error(\"createCrewWelfareCheck\", error);\n        }\n    });\n    const [createCrewMembers_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.CreateCrewMembers_LogBookEntrySection, {\n        onCompleted: (data)=>{\n            var _allMembers_find;\n            // First, update the UI immediately with the new crew member\n            // Get the selected crew member and duty information\n            const selectedMember = (_allMembers_find = allMembers.find((m)=>m.value === crewManifestEntry.crewMemberID)) === null || _allMembers_find === void 0 ? void 0 : _allMembers_find.data;\n            const selectedDuty = duties.find((d)=>d.id === crewManifestEntry.dutyPerformedID);\n            if (selectedMember && selectedDuty) {\n                var _document_getElementById;\n                // Create a new crew entry with the necessary data for immediate display\n                const newCrewEntry = {\n                    id: data.createCrewMembers_LogBookEntrySection.id,\n                    crewMemberID: crewManifestEntry.crewMemberID,\n                    dutyPerformedID: crewManifestEntry.dutyPerformedID,\n                    logBookEntryID: +logBookEntryID,\n                    punchIn: loginTime ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(loginTime) : null,\n                    punchOut: logoutTime ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDBDateTime)(logoutTime) : null,\n                    workDetails: ((_document_getElementById = document.getElementById(\"work-details\")) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.value) || \"\",\n                    crewMember: selectedMember,\n                    dutyPerformed: selectedDuty\n                };\n                // Create a new array with existing crew data plus the new entry\n                let updatedCrewData = crew ? [\n                    ...crew\n                ] : [];\n                // Check if this crew member already exists in the list\n                const existingCrewMemberIndex = updatedCrewData.findIndex((c)=>c.crewMemberID === crewManifestEntry.crewMemberID);\n                if (existingCrewMemberIndex >= 0) {\n                    // If the crew member already exists, add this duty to their duties array\n                    const existingCrewMember = updatedCrewData[existingCrewMemberIndex];\n                    const updatedDuties = existingCrewMember.duties ? [\n                        ...existingCrewMember.duties\n                    ] : [];\n                    updatedDuties.push({\n                        id: data.createCrewMembers_LogBookEntrySection.id,\n                        dutyPerformed: selectedDuty,\n                        punchIn: newCrewEntry.punchIn,\n                        punchOut: newCrewEntry.punchOut,\n                        workDetails: newCrewEntry.workDetails,\n                        dutyPerformedID: newCrewEntry.dutyPerformedID,\n                        logBookEntryID: newCrewEntry.logBookEntryID\n                    });\n                    // Update the crew member with the new duties array\n                    updatedCrewData[existingCrewMemberIndex] = {\n                        ...existingCrewMember,\n                        duties: updatedDuties\n                    };\n                } else {\n                    // If this is a new crew member, add them to the list with their first duty\n                    updatedCrewData.push({\n                        ...newCrewEntry,\n                        duties: [\n                            {\n                                id: data.createCrewMembers_LogBookEntrySection.id,\n                                dutyPerformed: selectedDuty,\n                                punchIn: newCrewEntry.punchIn,\n                                punchOut: newCrewEntry.punchOut,\n                                workDetails: newCrewEntry.workDetails,\n                                dutyPerformedID: newCrewEntry.dutyPerformedID,\n                                logBookEntryID: newCrewEntry.logBookEntryID\n                            }\n                        ]\n                    });\n                }\n                // Group the updated crew data\n                const groupedData = groupCrewDutiesByMember(updatedCrewData);\n                // Update state with the new grouped data\n                setCrew(groupedData);\n                setCrewMembers(groupedData);\n            }\n            // Then, fetch the latest data from the server to ensure consistency\n            const appendData = crew ? [\n                ...crew === null || crew === void 0 ? void 0 : crew.map((c)=>c.id),\n                data.createCrewMembers_LogBookEntrySection.id\n            ] : [\n                data.createCrewMembers_LogBookEntrySection.id\n            ];\n            setCrewManifestEntry({});\n            const searchFilter = {};\n            searchFilter.id = {\n                in: appendData\n            };\n            getSectionCrewMembers_LogBookEntrySection({\n                variables: {\n                    filter: searchFilter\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"createCrewMembers_LogBookEntrySection\", error);\n        }\n    });\n    const [getSectionCrewMembers_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.CrewMembers_LogBookEntrySection, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readCrewMembers_LogBookEntrySections.nodes;\n            if (data) {\n                // Process crew members with training status\n                const processedData = data.map((section)=>{\n                    if (section.crewMember) {\n                        const crewMemberWithTrainingStatus = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n                            section.crewMember\n                        ], [\n                            vessel\n                        ])[0];\n                        return {\n                            ...section,\n                            crewMember: crewMemberWithTrainingStatus\n                        };\n                    }\n                    return section;\n                });\n                // Preserve existing duties if they exist\n                let updatedData = processedData;\n                if (crew && crew.length > 0) {\n                    // Create a map of existing crew members with their duties\n                    const existingCrewMap = crew.reduce((map, member)=>{\n                        if (member.crewMemberID) {\n                            map[member.crewMemberID] = member;\n                        }\n                        return map;\n                    }, {});\n                    // Update processed data with existing duties where applicable\n                    updatedData = processedData.map((section)=>{\n                        const existingMember = existingCrewMap[section.crewMemberID];\n                        if (existingMember && existingMember.duties && existingMember.duties.length > 0) {\n                            // Find the matching duty in the existing duties array\n                            const existingDutyIndex = existingMember.duties.findIndex((duty)=>duty.id === section.id);\n                            if (existingDutyIndex >= 0) {\n                                // This section is already in the duties, update it with the latest data\n                                const updatedDuties = [\n                                    ...existingMember.duties\n                                ];\n                                updatedDuties[existingDutyIndex] = {\n                                    ...updatedDuties[existingDutyIndex],\n                                    // Update with the latest data from the server\n                                    punchIn: section.punchIn,\n                                    punchOut: section.punchOut,\n                                    workDetails: section.workDetails\n                                };\n                                return {\n                                    ...section,\n                                    duties: updatedDuties\n                                };\n                            } else {\n                                // This is a new duty for this crew member, add it to their duties\n                                const updatedDuties = [\n                                    ...existingMember.duties\n                                ];\n                                updatedDuties.push({\n                                    id: section.id,\n                                    dutyPerformed: section.dutyPerformed,\n                                    punchIn: section.punchIn,\n                                    punchOut: section.punchOut,\n                                    workDetails: section.workDetails,\n                                    dutyPerformedID: section.dutyPerformedID,\n                                    logBookEntryID: section.logBookEntryID\n                                });\n                                return {\n                                    ...section,\n                                    duties: updatedDuties\n                                };\n                            }\n                        }\n                        // No existing duties for this crew member, create a new duties array\n                        return {\n                            ...section,\n                            duties: [\n                                {\n                                    id: section.id,\n                                    dutyPerformed: section.dutyPerformed,\n                                    punchIn: section.punchIn,\n                                    punchOut: section.punchOut,\n                                    workDetails: section.workDetails,\n                                    dutyPerformedID: section.dutyPerformedID,\n                                    logBookEntryID: section.logBookEntryID\n                                }\n                            ]\n                        };\n                    });\n                }\n                // Group crew duties by crew member\n                const groupedData = groupCrewDutiesByMember(updatedData);\n                setCrew(groupedData);\n                setCrewMembers(groupedData);\n                const members = allMembers.filter((member)=>{\n                    if (!data) {\n                        return true;\n                    }\n                    return !data.some((section)=>section.crewMember.id === member.value && section.punchOut === null);\n                });\n                setCrewMemberOptions(members.filter((member)=>!crewMembersList || !crewMembersList.includes(+member.value)));\n            }\n        },\n        onError: (error)=>{\n            console.error(\"getSectionCrewMembers_LogBookEntrySection\", error);\n        }\n    });\n    const handleArchive = async ()=>{\n        setOpenConfirmCrewDeleteDialog(false);\n        if (!crewManifestEntry.id) {\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"No crew member selected to delete\");\n            return;\n        }\n        if (offline) {\n            try {\n                // First try to delete the record\n                const result = await lbCrewModel.delete({\n                    id: crewManifestEntry.id\n                });\n                if (!result) {\n                    // If delete fails, mark as archived\n                    await lbCrewModel.save({\n                        id: crewManifestEntry.id,\n                        archived: true\n                    });\n                }\n                const appendData = [\n                    ...crew.map((c)=>c.id)\n                ];\n                setCrewManifestEntry({});\n                const data = await lbCrewModel.getByIds(appendData);\n                if (data) {\n                    // Process crew members with training status\n                    const processedData = data.map((section)=>{\n                        if (section.crewMember) {\n                            const crewMemberWithTrainingStatus = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_8__.GetCrewListWithTrainingStatus)([\n                                section.crewMember\n                            ], [\n                                vessel\n                            ])[0];\n                            return {\n                                ...section,\n                                crewMember: crewMemberWithTrainingStatus\n                            };\n                        }\n                        return section;\n                    });\n                    // Group crew duties by crew member\n                    const groupedData = groupCrewDutiesByMember(processedData);\n                    setCrew(groupedData);\n                    setCrewMembers(groupedData);\n                }\n            } catch (error) {\n                console.error(\"Error deleting crew member:\", error);\n                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Failed to delete crew member\");\n            }\n        } else {\n            try {\n                // Use the delete mutation instead of update\n                await deleteCrewMembersLogBookEntrySections({\n                    variables: {\n                        ids: [\n                            crewManifestEntry.id\n                        ]\n                    }\n                });\n            } catch (error) {\n                console.error(\"Error deleting crew member:\", error);\n                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Failed to delete crew member\");\n            }\n        }\n        setopenAddCrewMemberDialog(false);\n    };\n    // Function removed as we're directly using setOpenConfirmCrewDeleteDialog\n    const [deleteCrewMembersLogBookEntrySections] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_2__.DeleteCrewMembers_LogBookEntrySections, {\n        onCompleted: ()=>{\n            const appendData = [\n                ...crew.map((c)=>c.id)\n            ];\n            const searchFilter = {};\n            searchFilter.id = {\n                in: appendData\n            };\n            getSectionCrewMembers_LogBookEntrySection({\n                variables: {\n                    filter: searchFilter\n                }\n            });\n            setOpenConfirmCrewDeleteDialog(false);\n            setopenAddCrewMemberDialog(false);\n        },\n        onError: (error)=>{\n            console.error(\"deleteCrewMembersLogBookEntrySections error:\", error);\n        }\n    });\n    // Function removed as we're using handleArchive instead\n    const crewCount = ()=>{\n        if (!crew || !Array.isArray(crew)) return 0;\n        const count = crew.filter((member)=>member && member.crewMemberID > 0 && member.punchOut === null).length;\n        return count;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (crewConfig) {\n            handleSetStatus();\n        }\n    }, [\n        crewConfig\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_19___default()(logBookConfig)) {\n            handleSetCrewConfig();\n        }\n    }, [\n        logBookConfig\n    ]);\n    var _vessel_maxPOB, _vessel_maxPOB1;\n    // Removed unused overdueTextWarning variable\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid lg:grid-cols-8 gap-36 lg:gap-6 xl:gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_33__.Card, {\n                        className: \"lg:col-span-5 space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_28__.H2, {\n                                children: \"Crew\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                lineNumber: 2006,\n                                columnNumber: 21\n                            }, this),\n                            crew ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.Table, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                                            className: \"pl-2.5 text-left align-bottom standard:align-top\",\n                                                            children: \"Crew\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                            lineNumber: 2012,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                                            className: \"px-[5px] text-left align-bottom standard:align-top\",\n                                                            children: \"Duty\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                            lineNumber: 2015,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        bp.standard ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                punchInStatus !== \"Off\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                                                    className: \"px-[5px] text-right\",\n                                                                    children: punchInLabel || \"Sign In\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                    lineNumber: 2022,\n                                                                    columnNumber: 53\n                                                                }, this),\n                                                                punchOutStatus !== \"Off\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                                                    className: \"pl-[5px] pr-2.5 text-right\",\n                                                                    children: punchOutLabel || \"Sign Out\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                    lineNumber: 2029,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                                            className: \"text-wrap standard:text-nowrap pr-0 text-right\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    punchInStatus !== \"Off\" ? punchInLabel || \"Sign In\" : \"\",\n                                                                    \"/\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {\n                                                                        className: \"standard:hidden\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                        lineNumber: 2043,\n                                                                        columnNumber: 53\n                                                                    }, this),\n                                                                    punchOutStatus !== \"Off\" ? punchOutLabel || \"Sign Out\" : \"\"\n                                                                ]\n                                                            }, void 0, true)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                            lineNumber: 2036,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                    lineNumber: 2011,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                lineNumber: 2010,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableBody, {\n                                                children: crew.filter((member)=>+member.crewMemberID > 0 && member.archived === false).map((member)=>{\n                                                    var _member_crewMember_trainingStatus;\n                                                    // Check if member has multiple duties\n                                                    const hasMultipleDuties = member.duties && Array.isArray(member.duties) && member.duties.length > 1;\n                                                    // Get additional duties (if any)\n                                                    const additionalDuties = hasMultipleDuties ? member.duties.slice(1).filter((duty)=>{\n                                                        // Get the first duty's title\n                                                        const firstDutyTitle = member.duties[0].dutyPerformed && member.duties[0].dutyPerformed.title;\n                                                        // Get current duty's title\n                                                        const currentDutyTitle = duty.dutyPerformed && duty.dutyPerformed.title;\n                                                        // Only include duties with different titles\n                                                        return currentDutyTitle && firstDutyTitle !== currentDutyTitle;\n                                                    }) : [];\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                                                \"aria-disabled\": locked,\n                                                                className: \"group \".concat(hasMultipleDuties ? \"border-b-0\" : \"\"),\n                                                                onClick: (e)=>{\n                                                                    // Don't do anything if locked\n                                                                    if (locked) return;\n                                                                    // Prevent row click if the event originated from a button\n                                                                    if (e.target instanceof HTMLElement && (e.target.closest(\"button\") || e.target.closest('[role=\"button\"]'))) {\n                                                                        return;\n                                                                    }\n                                                                    handleEditManifest(member);\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-left\", additionalDuties.length > 0 && \" text-foreground\"),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__.Avatar, {\n                                                                                    size: \"sm\",\n                                                                                    variant: ((_member_crewMember_trainingStatus = member.crewMember.trainingStatus) === null || _member_crewMember_trainingStatus === void 0 ? void 0 : _member_crewMember_trainingStatus.label) !== \"Good\" ? \"destructive\" : \"success\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__.AvatarFallback, {\n                                                                                        children: (0,_components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__.getCrewInitials)(member.crewMember.firstName, member.crewMember.surname)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2150,\n                                                                                        columnNumber: 69\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                    lineNumber: 2139,\n                                                                                    columnNumber: 65\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"hidden leading-none sm:flex flex-col justify-center ml-2\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"flex gap-2.5 items-center\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"text-foreground\",\n                                                                                                children: [\n                                                                                                    member.crewMember.firstName,\n                                                                                                    \" \",\n                                                                                                    member.crewMember.surname\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                                lineNumber: 2163,\n                                                                                                columnNumber: 73\n                                                                                            }, this),\n                                                                                            member.workDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_25__.Popover, {\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_25__.PopoverTrigger, {\n                                                                                                        onClick: (e)=>{\n                                                                                                            e.stopPropagation();\n                                                                                                        },\n                                                                                                        className: \"p-0 text-muted-foreground\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_InfoIcon_lucide_react__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                                                            className: \"text-light-blue-vivid-900 fill-light-blue-vivid-50\",\n                                                                                                            size: 24\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                                            lineNumber: 2184,\n                                                                                                            columnNumber: 85\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                                        lineNumber: 2177,\n                                                                                                        columnNumber: 81\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_25__.PopoverContent, {\n                                                                                                        className: \"w-80\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"text-sm\",\n                                                                                                            children: member.workDetails\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                                            lineNumber: 2192,\n                                                                                                            columnNumber: 85\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                                        lineNumber: 2191,\n                                                                                                        columnNumber: 81\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                                lineNumber: 2176,\n                                                                                                columnNumber: 77\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2162,\n                                                                                        columnNumber: 69\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                    lineNumber: 2161,\n                                                                                    columnNumber: 65\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                            lineNumber: 2138,\n                                                                            columnNumber: 61\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                        lineNumber: 2131,\n                                                                        columnNumber: 57\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-left grid items-center\", additionalDuties.length > 0 && \"text-foreground\"),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"truncate\",\n                                                                            children: member.dutyPerformed && member.dutyPerformed.title ? member.dutyPerformed.title : \"Not assigned\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                            lineNumber: 2212,\n                                                                            columnNumber: 61\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                        lineNumber: 2205,\n                                                                        columnNumber: 57\n                                                                    }, this),\n                                                                    bp.standard ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-right\", additionalDuties.length > 0 && \"text-foreground\"),\n                                                                                children: punchInStatus !== \"Off\" && ((member === null || member === void 0 ? void 0 : member.punchIn) ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(member.punchIn) : \"Not Available\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                lineNumber: 2226,\n                                                                                columnNumber: 65\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-right phablet:pr-2.5 relaive\", additionalDuties.length > 0 && \"text-input\"),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex justify-end\",\n                                                                                    children: punchOutStatus !== \"Off\" && (!member.punchOut ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                                                                        variant: \"text\",\n                                                                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-fill-inherit font-normal h-fit\"),\n                                                                                        disabled: locked,\n                                                                                        onClick: (e)=>{\n                                                                                            e.stopPropagation();\n                                                                                            handleSignOutTime(member);\n                                                                                        },\n                                                                                        children: punchOutLabel || \"Sign Out\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2253,\n                                                                                        columnNumber: 81\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"whitespace-nowrap h-8 flex items-center\"),\n                                                                                        children: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(member.punchOut)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2273,\n                                                                                        columnNumber: 81\n                                                                                    }, this))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                    lineNumber: 2249,\n                                                                                    columnNumber: 69\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                lineNumber: 2242,\n                                                                                columnNumber: 65\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-right relaive flex flex-col phablet:pr-2.5 justify-center items-end\", additionalDuties.length > 0 && \"text-input\"),\n                                                                            children: [\n                                                                                punchInStatus !== \"Off\" && ((member === null || member === void 0 ? void 0 : member.punchIn) ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(member.punchIn) : \"Not Available\"),\n                                                                                punchOutStatus !== \"Off\" && (!member.punchOut ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                                                                    variant: \"text\",\n                                                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-fill-inherit font-normal h-fit ml-auto\"),\n                                                                                    disabled: locked,\n                                                                                    onClick: (e)=>{\n                                                                                        e.stopPropagation();\n                                                                                        handleSignOutTime(member);\n                                                                                    },\n                                                                                    children: punchOutLabel || \"Sign Out\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                    lineNumber: 2305,\n                                                                                    columnNumber: 77\n                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"whitespace-nowrap h-8 flex items-center w-full justify-end\"),\n                                                                                    children: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(member.punchOut)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                    lineNumber: 2325,\n                                                                                    columnNumber: 77\n                                                                                }, this))\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                            lineNumber: 2287,\n                                                                            columnNumber: 65\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                        lineNumber: 2286,\n                                                                        columnNumber: 61\n                                                                    }, this)\n                                                                ]\n                                                            }, member.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                lineNumber: 2106,\n                                                                columnNumber: 53\n                                                            }, this),\n                                                            additionalDuties.map((duty, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                                                    \"aria-disabled\": locked,\n                                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"group\", index === additionalDuties.length - 1 ? \"\" : \"border-b-0\"),\n                                                                    onClick: (e)=>{\n                                                                        // Don't do anything if locked\n                                                                        if (locked) return;\n                                                                        // Prevent row click if the event originated from a button\n                                                                        if (e.target instanceof HTMLElement && (e.target.closest(\"button\") || e.target.closest('[role=\"button\"]'))) {\n                                                                            return;\n                                                                        }\n                                                                        handleEditManifest(member);\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\" text-input tiny:px-1 phablet:px-2.5 z-[5] py-2 text-left relative\"),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex flex-col absolute -top-[42%] items-center w-8 h-full\",\n                                                                                children: [\n                                                                                    index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-full h-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2389,\n                                                                                        columnNumber: 77\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-[1px] flex-1 border-l border-dashed border-neutral-400\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2391,\n                                                                                        columnNumber: 73\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"size-[5px] rounded-full bg-background border border-neutral-400\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2392,\n                                                                                        columnNumber: 73\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                lineNumber: 2386,\n                                                                                columnNumber: 69\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                            lineNumber: 2382,\n                                                                            columnNumber: 65\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\" text-input grid items-center text-left\"),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"truncate\",\n                                                                                children: duty.dutyPerformed && duty.dutyPerformed.title ? duty.dutyPerformed.title : \"Not assigned\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                lineNumber: 2400,\n                                                                                columnNumber: 69\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                            lineNumber: 2396,\n                                                                            columnNumber: 65\n                                                                        }, this),\n                                                                        bp.standard ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\" text-input py-2 text-right\"),\n                                                                                    children: punchInStatus !== \"Off\" && ((duty === null || duty === void 0 ? void 0 : duty.punchIn) ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(duty.punchIn) : \"Not Available\")\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                    lineNumber: 2414,\n                                                                                    columnNumber: 73\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\" text-input phablet:pr-2.5 py-2 text-right\"),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: punchOutStatus !== \"Off\" && (!duty.punchOut ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                                                                            variant: \"text\",\n                                                                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-fill-inherit font-normal size-fit\"),\n                                                                                            disabled: locked,\n                                                                                            onClick: (e)=>{\n                                                                                                e.stopPropagation();\n                                                                                                handleSignOutTime(duty);\n                                                                                            },\n                                                                                            children: punchOutLabel || \"Sign Out\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                            lineNumber: 2434,\n                                                                                            columnNumber: 89\n                                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"whitespace-nowrap flex items-center w-full justify-end\"),\n                                                                                            children: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(duty.punchOut)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                            lineNumber: 2454,\n                                                                                            columnNumber: 89\n                                                                                        }, this))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2430,\n                                                                                        columnNumber: 77\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                    lineNumber: 2426,\n                                                                                    columnNumber: 73\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-right flex-1 phablet:pr-2.5 relaive flex flex-col justify-end items-end\",\n                                                                                children: [\n                                                                                    punchInStatus !== \"Off\" && ((duty === null || duty === void 0 ? void 0 : duty.punchIn) ? (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(duty.punchIn) : \"Not Available\"),\n                                                                                    punchOutStatus !== \"Off\" && (!duty.punchOut ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                                                                        variant: \"text\",\n                                                                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"text-fill-inherit font-normal size-fit\"),\n                                                                                        disabled: locked,\n                                                                                        onClick: (e)=>{\n                                                                                            e.stopPropagation();\n                                                                                            handleSignOutTime(duty);\n                                                                                        },\n                                                                                        children: punchOutLabel || \"Sign Out\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2479,\n                                                                                        columnNumber: 85\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"whitespace-nowrap flex items-center w-full justify-end\"),\n                                                                                        children: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_10__.formatDateTime)(duty.punchOut)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                        lineNumber: 2499,\n                                                                                        columnNumber: 85\n                                                                                    }, this))\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                                lineNumber: 2468,\n                                                                                columnNumber: 73\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                            lineNumber: 2467,\n                                                                            columnNumber: 69\n                                                                        }, this)\n                                                                    ]\n                                                                }, \"duty-\".concat(duty.id, \"-\").concat(index), true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                    lineNumber: 2345,\n                                                                    columnNumber: 61\n                                                                }, this))\n                                                        ]\n                                                    }, \"crew-\".concat(member.id), true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                        lineNumber: 2103,\n                                                        columnNumber: 49\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                lineNumber: 2053,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                        lineNumber: 2009,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_35__.FormFooter, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_20__.Label, {\n                                                        className: \"mb-0 font-semibold\",\n                                                        children: \"Minimum crew:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                        lineNumber: 2522,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_30__.Badge, {\n                                                        variant: (0,_types__WEBPACK_IMPORTED_MODULE_32__.isVessel)(vessel) && crewCount() > ((_vessel_maxPOB = vessel === null || vessel === void 0 ? void 0 : vessel.maxPOB) !== null && _vessel_maxPOB !== void 0 ? _vessel_maxPOB : 0) ? \"destructive\" : \"success\",\n                                                        className: \"rounded-full flex items-center justify-center size-[25px]\",\n                                                        children: (0,_types__WEBPACK_IMPORTED_MODULE_32__.isVessel)(vessel) ? vessel.minCrew : 0\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                        lineNumber: 2525,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    (0,_types__WEBPACK_IMPORTED_MODULE_32__.isVessel)(vessel) && crewCount() > ((_vessel_maxPOB1 = vessel === null || vessel === void 0 ? void 0 : vessel.maxPOB) !== null && _vessel_maxPOB1 !== void 0 ? _vessel_maxPOB1 : 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                        className: \"text-destructive\",\n                                                        children: \"You have more people on board than your vessel is configured to carry\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                        lineNumber: 2537,\n                                                        columnNumber: 45\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                lineNumber: 2521,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                                className: \"w-full tiny:w-fit px-2.5\",\n                                                disabled: locked,\n                                                onClick: handleAddManifest,\n                                                children: \"Add crew\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                lineNumber: 2545,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                        lineNumber: 2520,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                lineNumber: 2008,\n                                columnNumber: 25\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                    disabled: locked,\n                                    onClick: handleAddManifest,\n                                    children: \"Add crew members to this trip\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2555,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                lineNumber: 2554,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                        lineNumber: 2005,\n                        columnNumber: 17\n                    }, this),\n                    crew && logBookConfig && (logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.find((config)=>config.title === \"Crew Welfare\" && config.active === true)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_33__.Card, {\n                        className: \"lg:col-span-3 space-y-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_daily_checks_crew_welfare__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            offline: offline,\n                            logBookConfig: logBookConfig,\n                            locked: locked || !edit_logBookEntry,\n                            crewWelfareCheck: crewWelfareCheck,\n                            updateCrewWelfare: updateCrewWelfare\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                            lineNumber: 2572,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                        lineNumber: 2571,\n                        columnNumber: 25\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                lineNumber: 2004,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_27__.AlertDialogNew, {\n                openDialog: openAddCrewMemberDialog,\n                setOpenDialog: setopenAddCrewMemberDialog,\n                handleCreate: handleSave,\n                handleCancel: handleCancel,\n                handleDestructiveAction: crewManifestEntry.id > 0 ? ()=>setOpenConfirmCrewDeleteDialog(true) : undefined,\n                showDestructiveAction: crewManifestEntry.id > 0,\n                destructiveActionText: \"Delete\",\n                title: crewManifestEntry.id > 0 ? \"Update crew member\" : \"Add crew member\",\n                actionText: crewManifestEntry.id > 0 ? \"Update\" : \"Add\",\n                cancelText: \"Cancel\",\n                contentClassName: \"max-w-2xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-top gap-4 mb-4\",\n                            children: [\n                                crewMember && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__.Avatar, {\n                                    variant: ((_crewMember_data = crewMember.data) === null || _crewMember_data === void 0 ? void 0 : (_crewMember_data_trainingStatus = _crewMember_data.trainingStatus) === null || _crewMember_data_trainingStatus === void 0 ? void 0 : _crewMember_data_trainingStatus.label) !== \"Good\" ? \"destructive\" : \"success\",\n                                    className: \"size-12 border-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__.AvatarImage, {\n                                            src: (_crewMember_profile = crewMember.profile) === null || _crewMember_profile === void 0 ? void 0 : _crewMember_profile.avatar,\n                                            alt: \"\".concat(((_crewMember_profile1 = crewMember.profile) === null || _crewMember_profile1 === void 0 ? void 0 : _crewMember_profile1.firstName) || \"\", \" \").concat(((_crewMember_profile2 = crewMember.profile) === null || _crewMember_profile2 === void 0 ? void 0 : _crewMember_profile2.surname) || \"\").trim()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                            lineNumber: 2614,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__.AvatarFallback, {\n                                            children: (0,_components_ui_avatar__WEBPACK_IMPORTED_MODULE_29__.getCrewInitials)(((_crewMember_profile3 = crewMember.profile) === null || _crewMember_profile3 === void 0 ? void 0 : _crewMember_profile3.firstName) || ((_crewMember_data1 = crewMember.data) === null || _crewMember_data1 === void 0 ? void 0 : _crewMember_data1.firstName), ((_crewMember_profile4 = crewMember.profile) === null || _crewMember_profile4 === void 0 ? void 0 : _crewMember_profile4.surname) || ((_crewMember_data2 = crewMember.data) === null || _crewMember_data2 === void 0 ? void 0 : _crewMember_data2.surname))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                            lineNumber: 2618,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2606,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_28__.H3, {\n                                            className: \"text-lg\",\n                                            children: (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_profile5 = crewMember.profile) === null || _crewMember_profile5 === void 0 ? void 0 : _crewMember_profile5.firstName) || (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data3 = crewMember.data) === null || _crewMember_data3 === void 0 ? void 0 : _crewMember_data3.firstName) || (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_profile6 = crewMember.profile) === null || _crewMember_profile6 === void 0 ? void 0 : _crewMember_profile6.surname) || (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data4 = crewMember.data) === null || _crewMember_data4 === void 0 ? void 0 : _crewMember_data4.surname) ? \"\".concat(((_crewMember_profile7 = crewMember.profile) === null || _crewMember_profile7 === void 0 ? void 0 : _crewMember_profile7.firstName) || ((_crewMember_data5 = crewMember.data) === null || _crewMember_data5 === void 0 ? void 0 : _crewMember_data5.firstName) || \"\", \" \").concat(((_crewMember_profile8 = crewMember.profile) === null || _crewMember_profile8 === void 0 ? void 0 : _crewMember_profile8.surname) || ((_crewMember_data6 = crewMember.data) === null || _crewMember_data6 === void 0 ? void 0 : _crewMember_data6.surname) || \"\").trim() : \"\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                            lineNumber: 2629,\n                                            columnNumber: 29\n                                        }, this),\n                                        crewMember && ((crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data7 = crewMember.data) === null || _crewMember_data7 === void 0 ? void 0 : (_crewMember_data_trainingStatus1 = _crewMember_data7.trainingStatus) === null || _crewMember_data_trainingStatus1 === void 0 ? void 0 : _crewMember_data_trainingStatus1.label) !== \"Good\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-destructive\",\n                                                    children: \"Training is overdue\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                    lineNumber: 2641,\n                                                    columnNumber: 41\n                                                }, this),\n                                                (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data8 = crewMember.data) === null || _crewMember_data8 === void 0 ? void 0 : (_crewMember_data_trainingStatus2 = _crewMember_data8.trainingStatus) === null || _crewMember_data_trainingStatus2 === void 0 ? void 0 : _crewMember_data_trainingStatus2.dues) && crewMember.data.trainingStatus.dues.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-sm text-destructive\",\n                                                    children: crewMember.data.trainingStatus.dues.map((due, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-destructive text-lg\",\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                    lineNumber: 2657,\n                                                                    columnNumber: 65\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        due.trainingType.title,\n                                                                        \" \",\n                                                                        \"-\",\n                                                                        \" \",\n                                                                        due.status.label\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                                    lineNumber: 2660,\n                                                                    columnNumber: 65\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                            lineNumber: 2654,\n                                                            columnNumber: 61\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                                    lineNumber: 2648,\n                                                    columnNumber: 49\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                            lineNumber: 2640,\n                                            columnNumber: 37\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-bright-turquoise-600\",\n                                            children: \"Training up to date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                            lineNumber: 2680,\n                                            columnNumber: 37\n                                        }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2628,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                            lineNumber: 2604,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 sm:grid-cols-2 gap-[31px]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_23__.Combobox, {\n                                    label: \"Crew member\",\n                                    modal: true,\n                                    buttonClassName: \"w-full\",\n                                    options: crewMemberOptions.map((option)=>({\n                                            ...option,\n                                            value: String(option.value)\n                                        })),\n                                    value: crewMember,\n                                    onChange: handleCrewMember\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2687,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_crew_duty_dropdown__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                    label: \"Primary duty\",\n                                    crewDutyID: Number(duty === null || duty === void 0 ? void 0 : duty.value) || 0,\n                                    onChange: handleDuty,\n                                    multi: false,\n                                    modal: true,\n                                    offline: offline,\n                                    hideCreateOption: false\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2700,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                            lineNumber: 2686,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 xs:grid-cols-2 pr-px gap-[31px]\",\n                            children: [\n                                punchInStatus !== \"Off\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DateRange__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                    id: \"signin-date\",\n                                    modal: true,\n                                    value: loginTime,\n                                    onChange: handleLogin,\n                                    label: punchInLabel || \"Sign In\",\n                                    dateFormat: \"dd MMM,\",\n                                    placeholder: \"\".concat(punchInLabel || \"Sign In\", \" Time\"),\n                                    mode: \"single\",\n                                    type: \"datetime\",\n                                    closeOnSelect: false,\n                                    icon: _barrel_optimize_names_Clock_InfoIcon_lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"],\n                                    className: \"w-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2713,\n                                    columnNumber: 29\n                                }, this),\n                                punchOutStatus !== \"Off\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DateRange__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                    id: \"signout-date\",\n                                    modal: true,\n                                    value: logoutTime || undefined,\n                                    onChange: handleLogout,\n                                    label: punchOutLabel || \"Sign Out\",\n                                    placeholder: \"\".concat(punchOutLabel || \"Sign Out\", \" Time\"),\n                                    mode: \"single\",\n                                    type: \"datetime\" // Keep datetime to include time picker\n                                    ,\n                                    dateFormat: \"dd MMM,\",\n                                    timeFormat: \"HH:mm\" // Explicitly set time format\n                                    ,\n                                    closeOnSelect: false,\n                                    clearable: true,\n                                    icon: _barrel_optimize_names_Clock_InfoIcon_lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"],\n                                    className: \"w-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2730,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                            lineNumber: 2711,\n                            columnNumber: 21\n                        }, this),\n                        workDetailsStatus !== \"Off\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_20__.Label, {\n                            htmlFor: \"work-details\",\n                            label: workDetailsLabel || \"Work Details\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_21__.Textarea, {\n                                id: \"work-details\",\n                                rows: 4,\n                                className: \"w-full resize-none\",\n                                placeholder: \"Enter work details\",\n                                defaultValue: crewManifestEntry === null || crewManifestEntry === void 0 ? void 0 : crewManifestEntry.workDetails\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                lineNumber: 2753,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                            lineNumber: 2750,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                    lineNumber: 2603,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                lineNumber: 2583,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_27__.AlertDialogNew, {\n                openDialog: openEditLogoutTimeDialog,\n                setOpenDialog: setOpenEditLogoutTimeDialog,\n                handleCreate: ()=>handleSave(\"update\"),\n                handleCancel: handleCancel,\n                actionText: (0,_utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_36__.getResponsiveLabel)(bp.phablet, \"Update\", \"Update Time\"),\n                cancelText: \"Cancel\",\n                contentClassName: \"top-[38svh]\",\n                size: \"sm\",\n                title: \"Update sign out time\",\n                className: \"space-y-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full relative\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DateRange__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                        modal: true,\n                        id: \"signout-date\",\n                        name: \"signout-date\",\n                        label: \"\".concat(punchOutLabel || \"Sign Out\", \" Time\"),\n                        value: logoutTime || undefined,\n                        mode: \"single\",\n                        type: \"datetime\" // Keep datetime to include time picker\n                        ,\n                        onChange: handleLogout,\n                        dateFormat: \"dd MMM,\",\n                        timeFormat: \"HH:mm\" // Explicitly set time format\n                        ,\n                        placeholder: \"\".concat(punchOutLabel || \"Sign Out\", \" Time\"),\n                        closeOnSelect: false,\n                        clearable: true,\n                        icon: _barrel_optimize_names_Clock_InfoIcon_lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"],\n                        className: \"w-full\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                        lineNumber: 2781,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                    lineNumber: 2780,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                lineNumber: 2765,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_27__.AlertDialogNew, {\n                openDialog: openCrewTrainingDueDialog,\n                setOpenDialog: setOpenCrewTrainingDueDialog,\n                contentClassName: \"max-w-xl\",\n                className: \"space-y-4\",\n                cancelText: \"Cancel\",\n                actionText: \"Yes, Continue\",\n                handleCreate: ()=>setOpenCrewTrainingDueDialog(false),\n                handleCancel: ()=>{\n                    setOpenCrewTrainingDueDialog(false);\n                    setCrewMember(null);\n                    setDuty(null);\n                },\n                title: \"Crew member training status\",\n                variant: \"warning\",\n                showIcon: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data9 = crewMember.data) === null || _crewMember_data9 === void 0 ? void 0 : _crewMember_data9.firstName) || (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data10 = crewMember.data) === null || _crewMember_data10 === void 0 ? void 0 : _crewMember_data10.surname) ? \"\".concat(crewMember.data.firstName || \"\", \" \").concat(crewMember.data.surname || \"\").trim() : \"This crew member\",\n                                \" \",\n                                \"has overdue training sessions on this vessel. These sessions are:\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                            lineNumber: 2818,\n                            columnNumber: 21\n                        }, this),\n                        crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data11 = crewMember.data) === null || _crewMember_data11 === void 0 ? void 0 : (_crewMember_data_trainingStatus3 = _crewMember_data11.trainingStatus) === null || _crewMember_data_trainingStatus3 === void 0 ? void 0 : (_crewMember_data_trainingStatus_dues = _crewMember_data_trainingStatus3.dues) === null || _crewMember_data_trainingStatus_dues === void 0 ? void 0 : _crewMember_data_trainingStatus_dues.map((item, dueIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"\".concat(item.trainingType.title, \" - \").concat(item.status.label)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                    lineNumber: 2830,\n                                    columnNumber: 33\n                                }, this)\n                            }, dueIndex, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                                lineNumber: 2829,\n                                columnNumber: 29\n                            }, this)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Do you still want to add this crew member to this vessel?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                            lineNumber: 2837,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                    lineNumber: 2817,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                lineNumber: 2801,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_27__.AlertDialogNew, {\n                openDialog: openConfirmCrewDeleteDialog,\n                setOpenDialog: setOpenConfirmCrewDeleteDialog,\n                handleCreate: handleArchive,\n                handleCancel: ()=>{\n                    setOpenConfirmCrewDeleteDialog(false);\n                // Don't reset crew member here as it's needed for the parent dialog\n                },\n                actionText: \"Remove\",\n                cancelText: \"Cancel\",\n                contentClassName: \"max-w-md\",\n                variant: \"warning\",\n                showIcon: true,\n                title: \"Remove crew member\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm\",\n                    children: [\n                        \"Are you sure you want to remove\",\n                        \" \",\n                        (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data12 = crewMember.data) === null || _crewMember_data12 === void 0 ? void 0 : _crewMember_data12.firstName) || (crewMember === null || crewMember === void 0 ? void 0 : (_crewMember_data13 = crewMember.data) === null || _crewMember_data13 === void 0 ? void 0 : _crewMember_data13.surname) ? \"\".concat(crewMember.data.firstName || \"\", \" \").concat(crewMember.data.surname || \"\").trim() : \"this crew member\",\n                        \" \",\n                        \"from this trip manifest?\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                    lineNumber: 2858,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\crew.tsx\",\n                lineNumber: 2844,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Crew, \"gN5juuh1ndxpEhNnlqFFWEx5KI0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useSearchParams,\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_34__.useBreakpoints,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_37__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_38__.useMutation\n    ];\n});\n_c = Crew;\nvar _c;\n$RefreshReg$(_c, \"Crew\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew/crew.tsx\n"));

/***/ })

});